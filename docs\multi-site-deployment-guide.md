# RepairLift Airtable Integration - Multi-Site Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the RepairLift Airtable integration across multiple WordPress sites, enabling centralized content management for multiple repair shop locations or clients.

## Architecture Overview

### Centralized Management Model
- **Single Airtable Base**: Manages all sites from one location
- **Multiple WordPress Sites**: Each with unique configuration
- **Unified Form**: One form handles submissions for all sites
- **Site-Specific Processing**: Content routed to correct WordPress site

### Benefits
- Centralized content management
- Consistent branding across locations
- Streamlined onboarding process
- Bulk operations capability
- Unified reporting and analytics

## Prerequisites
- Multiple WordPress sites with RepairLift plugin installed
- Single Airtable account (Pro plan recommended for advanced features)
- Admin access to all WordPress sites
- Understanding of webhook configuration

---

## Phase 1: Airtable Base Setup for Multi-Site

### Step 1.1: Enhanced Site Configurations Table
Modify the Site Configurations table to support multiple sites:

| Field Name | Field Type | Configuration | Description |
|------------|------------|---------------|-------------|
| Site ID | Single Line Text | Primary key | Unique identifier (e.g., "miami-downtown") |
| Site Name | Single Line Text | Required | Friendly name (e.g., "TechFix Miami Downtown") |
| Client Name | Single Line Text | Optional | Client/franchise name if applicable |
| WordPress URL | URL | Required | Full site URL |
| Webhook Endpoint | URL | Required | Complete webhook URL |
| API Key | Single Line Text | Required | Unique API key for this site |
| Status | Single Select | Active/Inactive/Testing | Current site status |
| Location | Single Line Text | Optional | Physical location |
| Contact Person | Single Line Text | Optional | Site administrator |
| Contact Email | Email | Optional | Admin email |
| Created Date | Date | Auto | Setup date |
| Last Updated | Date | Auto | Last modification |
| Last Webhook | Date | Optional | Last successful webhook |
| Notes | Long Text | Optional | Additional information |

### Step 1.2: Enhanced Submissions Table
Add site-specific tracking fields:

| Field Name | Field Type | Configuration | Description |
|------------|------------|---------------|-------------|
| Site Info | Link to Record | Link to Site Configurations | Linked site record |
| Site Name | Lookup | From Site Info → Site Name | Display site name |
| Site Status | Lookup | From Site Info → Status | Display site status |
| Webhook URL | Lookup | From Site Info → Webhook Endpoint | Dynamic webhook URL |
| API Key | Lookup | From Site Info → API Key | Dynamic API key |

### Step 1.3: Create Site-Specific Views
1. **By Site** - Group by Site ID
2. **Active Sites Only** - Filter: Site Status = "Active"
3. **Recent by Site** - Group by Site ID, filter last 7 days
4. **Failed by Site** - Filter: Status = "Failed", group by Site ID

---

## Phase 2: WordPress Sites Configuration

### Step 2.1: Site Identifier Strategy
Develop a consistent naming convention:

**Format**: `{location}-{identifier}`
**Examples**:
- `miami-downtown`
- `orlando-mall`
- `tampa-westside`
- `client1-location1`

### Step 2.2: Configure Each WordPress Site

For each WordPress site:

1. **Install RepairLift Plugin**
   - Upload and activate plugin
   - Verify all dependencies

2. **Configure Airtable Integration**
   - Navigate to Airtable Integration tab
   - Enable integration
   - Generate unique API key for this site
   - Set site identifier (following naming convention)
   - Configure rate limiting (60 requests/hour recommended)
   - Save configuration

3. **Document Configuration**
   - Record site identifier
   - Record API key
   - Record webhook URL
   - Add to Site Configurations table in Airtable

### Step 2.3: API Key Management
Create a secure system for managing API keys:

```
Site: miami-downtown
API Key: airtable_miami_dt_[32-char-random]
Webhook: https://miamirepair.com/wp-admin/admin-ajax.php?action=airtable_webhook

Site: orlando-mall  
API Key: airtable_orlando_ml_[32-char-random]
Webhook: https://orlandofix.com/wp-admin/admin-ajax.php?action=airtable_webhook
```

---

## Phase 3: Enhanced Airtable Automation

### Step 3.1: Dynamic Webhook Automation
Create automation that routes to correct site:

**Trigger**: When record created in Website Content Submissions
**Conditions**: 
- Site Identifier is not empty
- Site Info (linked record) exists
- Site Status = "Active"

**Actions**:
1. **Webhook Action** (Dynamic)
   - URL: Use lookup field "Webhook URL"
   - Method: POST
   - Headers: 
     ```
     X-API-Key: {{API Key}}
     Content-Type: application/json
     ```

### Step 3.2: Error Handling Automation
Create secondary automation for error handling:

**Trigger**: When record updated in Website Content Submissions
**Conditions**: Status = "Failed"

**Actions**:
1. **Send Email** to site administrator
2. **Update** Processing Notes with error details
3. **Create** follow-up task in project management system

### Step 3.3: Success Tracking Automation
Track successful submissions:

**Trigger**: When record updated in Website Content Submissions  
**Conditions**: Status = "Processed"

**Actions**:
1. **Update** Site Configurations → Last Webhook field
2. **Send** confirmation email to submitter
3. **Log** success metrics

---

## Phase 4: Enhanced Form Configuration

### Step 4.1: Dynamic Site Selection
Convert Site Identifier to dynamic dropdown:

1. **Create Linked Record Field**
   - Link to Site Configurations table
   - Filter to show only Active sites
   - Display Site Name in dropdown

2. **Update Form**
   - Replace Site Identifier text field
   - Add linked record field for site selection
   - Include site description/location in dropdown

### Step 4.2: Conditional Field Display
Configure form to show relevant fields based on site:

1. **Site-Specific Branding**
   - Show different logos based on site selection
   - Display site-specific instructions
   - Customize color schemes per site

2. **Location-Specific Fields**
   - Show relevant service areas
   - Display appropriate contact information
   - Include location-specific specializations

---

## Phase 5: Testing & Validation

### Step 5.1: Individual Site Testing
For each configured site:

1. **WordPress Webhook Test**
   - Use built-in test function
   - Verify logs show success
   - Check error handling

2. **End-to-End Test**
   - Submit test form for this site
   - Verify content updates correctly
   - Check backup creation
   - Validate field mapping

### Step 5.2: Multi-Site Integration Testing
1. **Concurrent Submissions**
   - Submit forms for multiple sites simultaneously
   - Verify no cross-contamination
   - Check rate limiting behavior

2. **Error Scenario Testing**
   - Test with invalid site identifiers
   - Test with inactive sites
   - Verify error handling and logging

### Step 5.3: Load Testing
1. **Volume Testing**
   - Submit multiple forms rapidly
   - Monitor webhook response times
   - Check for timeout issues

2. **Rate Limit Testing**
   - Exceed rate limits intentionally
   - Verify proper error responses
   - Test rate limit recovery

---

## Phase 6: Monitoring & Maintenance

### Step 6.1: Monitoring Dashboard
Create Airtable dashboard with:

1. **Site Status Overview**
   - Active/Inactive site counts
   - Last webhook success per site
   - Error rates by site

2. **Submission Metrics**
   - Daily submission counts by site
   - Success/failure rates
   - Processing time averages

3. **Health Indicators**
   - Sites with recent failures
   - Sites with no recent activity
   - API key expiration alerts

### Step 6.2: Automated Monitoring
Set up automations for:

1. **Daily Health Check**
   - Check each site's last successful webhook
   - Alert if no activity for 7+ days
   - Verify site accessibility

2. **Error Alerting**
   - Immediate alerts for webhook failures
   - Daily summary of errors by site
   - Escalation for repeated failures

### Step 6.3: Maintenance Schedule
**Weekly Tasks**:
- Review error logs for all sites
- Check webhook response times
- Verify backup system functionality

**Monthly Tasks**:
- Rotate API keys for security
- Clean up old submissions
- Update site configurations as needed
- Performance optimization review

**Quarterly Tasks**:
- Full integration testing
- Security audit
- Documentation updates
- Client feedback review

---

## Phase 7: Scaling & Optimization

### Step 7.1: Performance Optimization
1. **Webhook Optimization**
   - Implement webhook queuing for high volume
   - Add retry logic with exponential backoff
   - Optimize payload size

2. **Database Optimization**
   - Index frequently queried fields
   - Archive old submissions
   - Optimize backup storage

### Step 7.2: Advanced Features
1. **Bulk Operations**
   - Mass updates across multiple sites
   - Bulk configuration changes
   - Synchronized deployments

2. **Advanced Reporting**
   - Cross-site analytics
   - Performance comparisons
   - Trend analysis

3. **API Extensions**
   - Custom endpoints for specific needs
   - Integration with other tools
   - Advanced authentication options

---

## Security Considerations

### Multi-Site Security Best Practices
1. **API Key Isolation**
   - Unique API key per site
   - Regular key rotation schedule
   - Secure key storage

2. **Access Control**
   - Site-specific admin access
   - Role-based permissions
   - Audit logging

3. **Network Security**
   - HTTPS enforcement
   - IP whitelisting where possible
   - DDoS protection

### Compliance & Privacy
1. **Data Isolation**
   - Ensure site data separation
   - Prevent cross-site data leakage
   - Implement data retention policies

2. **Privacy Protection**
   - GDPR compliance measures
   - Data anonymization options
   - User consent management

---

## Troubleshooting Multi-Site Issues

### Common Multi-Site Problems
1. **Cross-Site Data Contamination**
   - Verify site identifier mapping
   - Check webhook URL routing
   - Review field mapping logic

2. **Performance Degradation**
   - Monitor concurrent webhook processing
   - Check rate limiting configuration
   - Optimize database queries

3. **Authentication Issues**
   - Verify API key uniqueness
   - Check key rotation procedures
   - Validate webhook signatures

### Debugging Tools
1. **Centralized Logging**
   - Aggregate logs from all sites
   - Correlation IDs for tracking
   - Real-time monitoring

2. **Testing Framework**
   - Automated testing across all sites
   - Regression testing procedures
   - Performance benchmarking

---

## Success Metrics

### Key Performance Indicators
1. **Reliability Metrics**
   - Webhook success rate (target: >99%)
   - Average processing time (target: <5 seconds)
   - Error recovery time (target: <1 hour)

2. **Operational Metrics**
   - Sites successfully onboarded
   - Time to deploy new site (target: <2 hours)
   - Support ticket volume

3. **Business Metrics**
   - Client satisfaction scores
   - Content update frequency
   - System adoption rates

This completes the comprehensive multi-site deployment guide for RepairLift Airtable integration.
