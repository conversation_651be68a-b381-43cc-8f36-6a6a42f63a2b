// 🚀 COMPLETE AIRTABLE AUTOMATION SCRIPT - STANDARDIZED & DEBUGGED
// Field Name Standardization: hero_title, hero_tagline (clean 1:1 mapping)
// Includes comprehensive debugging to identify the working data access method

console.log('🔥 === AIRTABLE AUTOMATION SCRIPT STARTED ===');
console.log('📅 Timestamp:', new Date().toISOString());

// ===== STEP 1: GET RECORD DATA =====
console.log('\n📋 STEP 1: Getting record data...');

let inputConfig = input.config();
let record = inputConfig.record;

console.log('🔍 Input config type:', typeof inputConfig);
console.log('🔍 Record object type:', typeof record);
console.log('🔍 Record object keys:', Object.keys(record || {}));

if (record) {
    console.log('🔍 Record methods available:', Object.getOwnPropertyNames(record));
    console.log('🔍 Record prototype:', Object.getPrototypeOf(record));
}

// ===== STEP 2: INITIALIZE VARIABLES =====
console.log('\n📋 STEP 2: Initializing variables...');

let heroTitle = '';
let heroTagline = '';
let siteIdentifier = 'example-site';
let businessName = '';

// Track which method works
let workingMethod = 'none';
let methodResults = {
    getCellValue: { attempted: false, success: false, data: {} },
    directFields: { attempted: false, success: false, data: {} },
    directAccess: { attempted: false, success: false, data: {} }
};

// ===== STEP 3: DATA EXTRACTION WITH DEBUGGING =====
console.log('\n📋 STEP 3: Attempting data extraction methods...');

if (record) {
    
    // METHOD 1: getCellValue() - Preferred Airtable method
    console.log('\n🔬 METHOD 1: Testing getCellValue()...');
    methodResults.getCellValue.attempted = true;
    
    try {
        if (typeof record.getCellValue === 'function') {
            console.log('✅ getCellValue function exists');
            
            let testTitle = record.getCellValue('hero_title');
            let testTagline = record.getCellValue('hero_tagline');
            let testSite = record.getCellValue('Site Identifier');
            let testBusiness = record.getCellValue('Business Name');
            
            console.log('📤 getCellValue results:');
            console.log('   hero_title:', testTitle, '(type:', typeof testTitle, ')');
            console.log('   hero_tagline:', testTagline, '(type:', typeof testTagline, ')');
            console.log('   Site Identifier:', testSite, '(type:', typeof testSite, ')');
            console.log('   Business Name:', testBusiness, '(type:', typeof testBusiness, ')');
            
            // Check if we got valid data
            if (testTitle || testTagline || testSite || testBusiness) {
                heroTitle = testTitle || '';
                heroTagline = testTagline || '';
                siteIdentifier = testSite || 'example-site';
                businessName = testBusiness || '';
                
                workingMethod = 'getCellValue';
                methodResults.getCellValue.success = true;
                methodResults.getCellValue.data = {
                    hero_title: testTitle,
                    hero_tagline: testTagline,
                    site_identifier: testSite,
                    business_name: testBusiness
                };
                
                console.log('✅ METHOD 1 SUCCESS: getCellValue() worked!');
            } else {
                console.log('⚠️ METHOD 1 PARTIAL: getCellValue() exists but returned no data');
            }
        } else {
            console.log('❌ METHOD 1 FAILED: getCellValue is not a function');
        }
    } catch (error) {
        console.log('❌ METHOD 1 ERROR:', error.message);
    }
    
    // METHOD 2: Direct fields access
    console.log('\n🔬 METHOD 2: Testing direct fields access...');
    methodResults.directFields.attempted = true;
    
    try {
        if (record.fields && typeof record.fields === 'object') {
            console.log('✅ record.fields exists');
            console.log('🔍 Available fields:', Object.keys(record.fields));
            
            let testTitle = record.fields['hero_title'];
            let testTagline = record.fields['hero_tagline'];
            let testSite = record.fields['Site Identifier'];
            let testBusiness = record.fields['Business Name'];
            
            console.log('📤 Direct fields results:');
            console.log('   hero_title:', testTitle, '(type:', typeof testTitle, ')');
            console.log('   hero_tagline:', testTagline, '(type:', typeof testTagline, ')');
            console.log('   Site Identifier:', testSite, '(type:', typeof testSite, ')');
            console.log('   Business Name:', testBusiness, '(type:', typeof testBusiness, ')');
            
            // Use this method if getCellValue didn't work
            if (workingMethod === 'none' && (testTitle || testTagline || testSite || testBusiness)) {
                heroTitle = testTitle || '';
                heroTagline = testTagline || '';
                siteIdentifier = testSite || 'example-site';
                businessName = testBusiness || '';
                
                workingMethod = 'directFields';
                methodResults.directFields.success = true;
                methodResults.directFields.data = {
                    hero_title: testTitle,
                    hero_tagline: testTagline,
                    site_identifier: testSite,
                    business_name: testBusiness
                };
                
                console.log('✅ METHOD 2 SUCCESS: Direct fields access worked!');
            } else if (workingMethod !== 'none') {
                console.log('ℹ️ METHOD 2 SKIPPED: Already have working method');
            } else {
                console.log('⚠️ METHOD 2 PARTIAL: Fields exist but no data found');
            }
        } else {
            console.log('❌ METHOD 2 FAILED: record.fields does not exist or is not an object');
        }
    } catch (error) {
        console.log('❌ METHOD 2 ERROR:', error.message);
    }
    
    // METHOD 3: Direct property access
    console.log('\n🔬 METHOD 3: Testing direct property access...');
    methodResults.directAccess.attempted = true;
    
    try {
        let testTitle = record['hero_title'] || record.hero_title;
        let testTagline = record['hero_tagline'] || record.hero_tagline;
        let testSite = record['Site Identifier'] || record.site_identifier;
        let testBusiness = record['Business Name'] || record.business_name;
        
        console.log('📤 Direct access results:');
        console.log('   hero_title:', testTitle, '(type:', typeof testTitle, ')');
        console.log('   hero_tagline:', testTagline, '(type:', typeof testTagline, ')');
        console.log('   Site Identifier:', testSite, '(type:', typeof testSite, ')');
        console.log('   Business Name:', testBusiness, '(type:', typeof testBusiness, ')');
        
        // Use this method if others didn't work
        if (workingMethod === 'none' && (testTitle || testTagline || testSite || testBusiness)) {
            heroTitle = testTitle || '';
            heroTagline = testTagline || '';
            siteIdentifier = testSite || 'example-site';
            businessName = testBusiness || '';
            
            workingMethod = 'directAccess';
            methodResults.directAccess.success = true;
            methodResults.directAccess.data = {
                hero_title: testTitle,
                hero_tagline: testTagline,
                site_identifier: testSite,
                business_name: testBusiness
            };
            
            console.log('✅ METHOD 3 SUCCESS: Direct property access worked!');
        } else if (workingMethod !== 'none') {
            console.log('ℹ️ METHOD 3 SKIPPED: Already have working method');
        } else {
            console.log('⚠️ METHOD 3 FAILED: No data found via direct access');
        }
    } catch (error) {
        console.log('❌ METHOD 3 ERROR:', error.message);
    }
    
} else {
    console.log('❌ CRITICAL ERROR: No record object found');
}

// ===== STEP 4: DEBUGGING SUMMARY =====
console.log('\n📊 STEP 4: Data extraction summary...');
console.log('🎯 Working method:', workingMethod);
console.log('📋 Method results summary:');
console.log('   getCellValue - Attempted:', methodResults.getCellValue.attempted, 'Success:', methodResults.getCellValue.success);
console.log('   directFields - Attempted:', methodResults.directFields.attempted, 'Success:', methodResults.directFields.success);
console.log('   directAccess - Attempted:', methodResults.directAccess.attempted, 'Success:', methodResults.directAccess.success);

console.log('\n✅ Final extracted values:');
console.log('   Hero Title:', heroTitle, '(length:', heroTitle.length, ')');
console.log('   Hero Tagline:', heroTagline, '(length:', heroTagline.length, ')');
console.log('   Site Identifier:', siteIdentifier);
console.log('   Business Name:', businessName);

// ===== STEP 5: VALIDATION =====
console.log('\n📋 STEP 5: Data validation...');

if (!heroTitle && !heroTagline) {
    console.log('⚠️ WARNING: No hero content found - webhook will still be sent for testing');
} else {
    console.log('✅ Hero content found - proceeding with webhook');
}

// ===== STEP 6: WEBHOOK CONFIGURATION =====
console.log('\n📋 STEP 6: Webhook configuration...');

const webhookUrl = 'https://your-wordpress-site.com/wp-json/website-generator/v1/webhook';
const apiKey = 'your-api-key-here';

console.log('🔗 Webhook URL:', webhookUrl);
console.log('🔑 API Key:', apiKey ? 'Set (length: ' + apiKey.length + ')' : 'Not set');

// ===== STEP 7: BUILD PAYLOAD =====
console.log('\n📋 STEP 7: Building webhook payload...');

const payload = {
    api_key: apiKey,
    site_identifier: siteIdentifier,
    hero_title: heroTitle,
    hero_tagline: heroTagline,
    business_name: businessName,
    // Debug info
    debug_info: {
        working_method: workingMethod,
        timestamp: new Date().toISOString(),
        method_results: methodResults
    }
};

console.log('📤 Payload built:');
console.log(JSON.stringify(payload, null, 2));

// ===== STEP 8: SEND WEBHOOK =====
console.log('\n📋 STEP 8: Sending webhook...');

try {
    const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
    });

    const responseText = await response.text();
    
    console.log('📥 Response received:');
    console.log('   Status:', response.status);
    console.log('   Status Text:', response.statusText);
    console.log('   Headers:', JSON.stringify([...response.headers.entries()]));
    console.log('   Body:', responseText);

    if (response.ok) {
        console.log('✅ WEBHOOK SUCCESS!');
        console.log('🎯 Successfully updated:');
        if (heroTitle) console.log('   - Hero Title:', heroTitle);
        if (heroTagline) console.log('   - Hero Tagline:', heroTagline);
        console.log('🔧 Working method was:', workingMethod);
    } else {
        console.log('❌ WEBHOOK FAILED');
        console.log('   Status:', response.status);
        console.log('   Response:', responseText);
    }
    
} catch (error) {
    console.log('❌ WEBHOOK ERROR:', error.message);
    console.log('   Error details:', error);
}

// ===== STEP 9: FINAL SUMMARY =====
console.log('\n🏁 FINAL SUMMARY:');
console.log('✅ Script execution completed');
console.log('🎯 Working data access method:', workingMethod);
console.log('📊 Data extracted:', heroTitle || heroTagline ? 'Yes' : 'No');
console.log('🚀 Webhook sent:', 'Yes');
console.log('📅 Completed at:', new Date().toISOString());

console.log('\n💡 OPTIMIZATION RECOMMENDATION:');
if (workingMethod !== 'none') {
    console.log('✅ For future script optimization, use only the "' + workingMethod + '" method');
    console.log('✅ Remove the other methods to simplify the script');
} else {
    console.log('⚠️ No working method found - check Airtable record structure');
}

console.log('\n🔥 === AIRTABLE AUTOMATION SCRIPT COMPLETED ===');
