<?php
/**
 * Admin page for Website Generator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load saved Claude AI configuration
$claude_api_key = get_option('website_generator_claude_api_key', '');
$claude_model = get_option('website_generator_claude_model', 'claude-3-5-haiku-20241022');
$claude_max_tokens = get_option('website_generator_claude_max_tokens', 1000);
$claude_temperature = get_option('website_generator_claude_temperature', 0.7);

// Mask API key for display (show only first 10 characters)
$claude_api_key_display = !empty($claude_api_key) ? substr($claude_api_key, 0, 10) . '...' : '';
$has_api_key = !empty($claude_api_key);
?>

<div class="wrap">
    <h1>RepairLift WP Customizer</h1>
    <p>Customize your repair shop website with our comprehensive visual editor, multi-page management, and professional design system.</p>
    <div class="notice notice-info" style="margin: 15px 0;">
        <p><strong>🛡️ Ultra Safe Button Targeting:</strong> Primary Color = Main CTAs (Get Instant Quote, Start a Repair), Secondary Color = Hero "Call Us Now" button only. Header navigation excluded to prevent conflicts. Use "Revert All Changes" to restore.</p>
    </div>
    
    <!-- Navigation Tabs -->
    <div class="nav-tab-wrapper">
        <a href="#" class="nav-tab nav-tab-active" data-tab="homepage">🏠 Homepage</a>
        <a href="#" class="nav-tab" data-tab="business-info">🏢 Business Info</a>
        <a href="#" class="nav-tab" data-tab="ai-configuration">🤖 AI Settings</a>
        <a href="#" class="nav-tab" data-tab="ai-images">🖼️ Generate Images with AI</a>
        <a href="#" class="nav-tab" data-tab="airtable-integration">📋 Airtable Integration</a>
        <a href="#" class="nav-tab" data-tab="backup-restore">🛡️ Backup & Restore</a>
    </div>

    <div id="website-generator-container">

        <!-- Homepage Tab -->
        <div id="homepage-tab" class="tab-content active">
            <div class="visual-editor-header">
                <h2>Visual Block Editor</h2>
                <p>Edit each section of your website and see live preview changes</p>
                <div class="editor-controls">
                    <button type="button" id="load-current-content" class="button button-secondary">
                        <span class="dashicons dashicons-download"></span>
                        Load Current Content
                    </button>
                    <div class="editor-help">
                        <p><strong>💡 New:</strong> Each section now has its own Apply Changes button for better control. Edit any section and apply changes immediately without scrolling back to the top.</p>
                    </div>
                </div>
            </div>

            <div class="visual-blocks-container">
                <!-- Hero Section Block -->
                <div class="visual-block" data-block="hero">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-format-image"></span> Hero Section</h3>
                        <div class="block-actions">
                            <div class="block-status">
                                <span class="status-indicator" data-status="unchanged">Unchanged</span>
                            </div>
                            <div class="section-buttons">
                                <button type="button" class="button button-secondary section-preview-btn" data-section="hero">
                                    <span class="dashicons dashicons-visibility"></span>
                                    Preview
                                </button>
                                <button type="button" class="button button-primary section-apply-btn" data-section="hero">
                                    <span class="dashicons dashicons-yes"></span>
                                    Apply Changes
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="hero_title">Main Heading:</label>
                                <div class="input-with-actions">
                                    <input type="text" id="hero_title" name="hero_title" class="block-input"
                                           value="Premier Device Repair in Anytown, FL" data-preview="hero-heading">
                                    <button type="button" class="restore-default-text-btn" data-field="hero_title" data-default="Premier Device Repair in Anytown, FL">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Edit this text to customize your main heading</p>
                            </div>
                            <div class="form-row">
                                <label for="hero_tagline">Tagline:</label>
                                <div class="input-with-actions">
                                    <input type="text" id="hero_tagline" name="hero_tagline" class="block-input"
                                           value="Smartphones | Tablets | Computers | & More" data-preview="hero-tagline">
                                    <button type="button" class="restore-default-text-btn" data-field="hero_tagline" data-default="Smartphones | Tablets | Computers | & More">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Describe the devices you repair</p>
                            </div>
                            <div class="form-row">
                                <label for="hero_button1">Button 1 Text:</label>
                                <input type="text" id="hero_button1" name="hero_button1" class="block-input"
                                       value="Start a Repair" data-preview="hero-button1">
                                <p class="field-help">Primary call-to-action button</p>
                            </div>
                            <div class="form-row">
                                <label for="hero_button2">Button 2 Text:</label>
                                <input type="text" id="hero_button2" name="hero_button2" class="block-input"
                                       value="Call Us Now" data-preview="hero-button2">
                                <p class="field-help">Secondary call-to-action button</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container hero-preview">
                                <h1 id="preview-hero-heading">Premier Device Repair in Anytown, FL</h1>
                                <p id="preview-hero-tagline">Smartphones | Tablets | Computers | & More</p>
                                <div class="preview-buttons">
                                    <button class="preview-btn primary" id="preview-hero-button1">Start a Repair</button>
                                    <button class="preview-btn secondary" id="preview-hero-button2">Call Us Now</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- One-Stop Shop Section Block -->
                <div class="visual-block" data-block="onestop">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-store"></span> One-Stop Shop Section</h3>
                        <div class="block-actions">
                            <div class="block-status">
                                <span class="status-indicator" data-status="unchanged">Unchanged</span>
                            </div>
                            <div class="section-buttons">
                                <button type="button" class="button button-secondary section-preview-btn" data-section="onestop">
                                    <span class="dashicons dashicons-visibility"></span>
                                    Preview
                                </button>
                                <button type="button" class="button button-primary section-apply-btn" data-section="onestop">
                                    <span class="dashicons dashicons-yes"></span>
                                    Apply Changes
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="onestop_heading">Section Heading:</label>
                                <div class="input-with-actions">
                                    <input type="text" id="onestop_heading" name="onestop_heading" class="block-input"
                                           value="Your One-Stop Shop Repair Store" data-preview="onestop-heading">
                                    <button type="button" class="restore-default-text-btn" data-field="onestop_heading" data-default="Your One-Stop Shop Repair Store">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Main title for your services section</p>
                            </div>
                            <div class="form-row">
                                <label for="onestop_description">Description:</label>
                                <div class="textarea-with-actions">
                                    <textarea id="onestop_description" name="onestop_description" class="block-input" rows="3"
                                              data-preview="onestop-description">Same Day Service on Major Brands & Devices. We repair smartphones, tablets, computers, and more with professional quality and fast turnaround times.</textarea>
                                    <button type="button" class="restore-default-text-btn" data-field="onestop_description" data-default="Same Day Service on Major Brands & Devices. We repair smartphones, tablets, computers, and more with professional quality and fast turnaround times.">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Describe your repair services and capabilities</p>
                            </div>
                            <div class="form-row">
                                <label for="onestop_button">Button Text:</label>
                                <input type="text" id="onestop_button" name="onestop_button" class="block-input"
                                       value="Our Location" data-preview="onestop-button">
                                <p class="field-help">Button to show your store location</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container onestop-preview">
                                <h3 id="preview-onestop-heading">Your One-Stop Shop Repair Store</h3>
                                <p id="preview-onestop-description">Same Day Service on Major Brands & Devices. Visit us at a location near you. We take pride in what we do.</p>
                                <button class="preview-btn primary" id="preview-onestop-button">Our Location</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Buy Devices Section Block -->
                <div class="visual-block" data-block="buydevices">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-cart"></span> Buy Devices Section</h3>
                        <div class="block-actions">
                            <div class="block-status">
                                <span class="status-indicator" data-status="unchanged">Unchanged</span>
                            </div>
                            <div class="section-buttons">
                                <button type="button" class="button button-secondary section-preview-btn" data-section="buydevices">
                                    <span class="dashicons dashicons-visibility"></span>
                                    Preview
                                </button>
                                <button type="button" class="button button-primary section-apply-btn" data-section="buydevices">
                                    <span class="dashicons dashicons-yes"></span>
                                    Apply Changes
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="buy_heading">Section Heading:</label>
                                <div class="input-with-actions">
                                    <input type="text" id="buy_heading" name="buy_heading" class="block-input"
                                           value="Buy Certified Pre-Owned Devices" data-preview="buy-heading">
                                    <button type="button" class="restore-default-text-btn" data-field="buy_heading" data-default="Buy Certified Pre-Owned Devices">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Title for device sales section</p>
                            </div>
                            <div class="form-row">
                                <label for="buy_description">Description:</label>
                                <div class="textarea-with-actions">
                                    <textarea id="buy_description" name="buy_description" class="block-input" rows="3"
                                              data-preview="buy-description">Our inventory changes rapidly and moves quickly. We have a variety of refurbished devices available at competitive prices with warranty included.</textarea>
                                    <button type="button" class="restore-default-text-btn" data-field="buy_description" data-default="Our inventory changes rapidly and moves quickly. We have a variety of refurbished devices available at competitive prices with warranty included.">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Describe your device sales and inventory</p>
                            </div>
                            <div class="form-row">
                                <label for="buy_button">Button Text:</label>
                                <input type="text" id="buy_button" name="buy_button" class="block-input"
                                       value="Buy a Device" data-preview="buy-button">
                                <p class="field-help">Button for device purchasing</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container buy-preview">
                                <h4 id="preview-buy-heading">Buy Certified Pre-Owned Devices</h4>
                                <p id="preview-buy-description">Our inventory changes rapidly and moves quickly because of our low prices.</p>
                                <button class="preview-btn primary" id="preview-buy-button">Buy a Device</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sell Devices Section Block -->
                <div class="visual-block" data-block="selldevices">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-money"></span> Sell Devices Section</h3>
                        <div class="block-actions">
                            <div class="block-status">
                                <span class="status-indicator" data-status="unchanged">Unchanged</span>
                            </div>
                            <div class="section-buttons">
                                <button type="button" class="button button-secondary section-preview-btn" data-section="selldevices">
                                    <span class="dashicons dashicons-visibility"></span>
                                    Preview
                                </button>
                                <button type="button" class="button button-primary section-apply-btn" data-section="selldevices">
                                    <span class="dashicons dashicons-yes"></span>
                                    Apply Changes
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="sell_heading">Section Heading:</label>
                                <div class="input-with-actions">
                                    <input type="text" id="sell_heading" name="sell_heading" class="block-input"
                                           value="Sell Your Used Or Broken Devices" data-preview="sell-heading">
                                    <button type="button" class="restore-default-text-btn" data-field="sell_heading" data-default="Sell Your Used Or Broken Devices">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Title for device buyback section</p>
                            </div>
                            <div class="form-row">
                                <label for="sell_description">Description:</label>
                                <div class="textarea-with-actions">
                                    <textarea id="sell_description" name="sell_description" class="block-input" rows="3"
                                              data-preview="sell-description">We pay you for your devices same day. Get instant quotes and fair market value for your smartphones, tablets, and other electronics.</textarea>
                                    <button type="button" class="restore-default-text-btn" data-field="sell_description" data-default="We pay you for your devices same day. Get instant quotes and fair market value for your smartphones, tablets, and other electronics.">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Describe your device buyback program</p>
                            </div>
                            <div class="form-row">
                                <label for="sell_button">Button Text:</label>
                                <input type="text" id="sell_button" name="sell_button" class="block-input"
                                       value="Sell a Device" data-preview="sell-button">
                                <p class="field-help">Button for device selling</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container sell-preview">
                                <h5 id="preview-sell-heading">Sell Your Used Or Broken Devices</h5>
                                <p id="preview-sell-description">We pay you for your devices same day, just come by our store.</p>
                                <button class="preview-btn primary" id="preview-sell-button">Sell a Device</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CTA Section Block -->
                <div class="visual-block" data-block="cta">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-megaphone"></span> Call-to-Action Section</h3>
                        <div class="block-actions">
                            <div class="block-status">
                                <span class="status-indicator" data-status="unchanged">Unchanged</span>
                            </div>
                            <div class="section-buttons">
                                <button type="button" class="button button-secondary section-preview-btn" data-section="cta">
                                    <span class="dashicons dashicons-visibility"></span>
                                    Preview
                                </button>
                                <button type="button" class="button button-primary section-apply-btn" data-section="cta">
                                    <span class="dashicons dashicons-yes"></span>
                                    Apply Changes
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="cta_heading">CTA Heading:</label>
                                <div class="input-with-actions">
                                    <input type="text" id="cta_heading" name="cta_heading" class="block-input"
                                           value="Repair Your Device Today!" data-preview="cta-heading">
                                    <button type="button" class="restore-default-text-btn" data-field="cta_heading" data-default="Repair Your Device Today!">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Final call-to-action heading</p>
                            </div>
                            <div class="form-row">
                                <label for="cta_description">Description:</label>
                                <div class="textarea-with-actions">
                                    <textarea id="cta_description" name="cta_description" class="block-input" rows="3"
                                              data-preview="cta-description">We offer reliable and professional service with fast turnaround times. Get your device back to working condition quickly and affordably.</textarea>
                                    <button type="button" class="restore-default-text-btn" data-field="cta_description" data-default="We offer reliable and professional service with fast turnaround times. Get your device back to working condition quickly and affordably.">
                                        <span class="dashicons dashicons-undo"></span>
                                        Restore Default
                                    </button>
                                </div>
                                <p class="field-help">Encourage visitors to take action</p>
                            </div>
                            <div class="form-row">
                                <label for="cta_button">Button Text:</label>
                                <input type="text" id="cta_button" name="cta_button" class="block-input"
                                       value="Get Instant Quote" data-preview="cta-button">
                                <p class="field-help">Final call-to-action button</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container cta-preview">
                                <h2 id="preview-cta-heading">Repair Your Device Today!</h2>
                                <p id="preview-cta-description">We offer reliable and professional service for your electronic device.</p>
                                <button class="preview-btn primary" id="preview-cta-button">Get Instant Quote</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Block -->
                <div class="visual-block" data-block="contact">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-phone"></span> Contact Information</h3>
                        <div class="block-actions">
                            <div class="block-status">
                                <span class="status-indicator" data-status="unchanged">Unchanged</span>
                            </div>
                            <div class="section-buttons">
                                <button type="button" class="button button-secondary section-preview-btn" data-section="contact">
                                    <span class="dashicons dashicons-visibility"></span>
                                    Preview
                                </button>
                                <button type="button" class="button button-primary section-apply-btn" data-section="contact">
                                    <span class="dashicons dashicons-yes"></span>
                                    Apply Changes
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-row">
                                <label for="contact_phone">Phone Number:</label>
                                <input type="tel" id="contact_phone" name="contact_phone" class="block-input"
                                       value="(*************" data-preview="contact-phone">
                                <p class="field-help">Your business phone number</p>
                            </div>
                            <div class="form-row">
                                <label for="contact_email">Email Address:</label>
                                <input type="email" id="contact_email" name="contact_email" class="block-input"
                                       value="<EMAIL>" data-preview="contact-email">
                                <p class="field-help">Your business email address</p>
                            </div>
                            <div class="form-row">
                                <label for="contact_address">Business Address:</label>
                                <textarea id="contact_address" name="contact_address" class="block-input" rows="2"
                                          data-preview="contact-address">123 Main Street, Anytown, FL 12345</textarea>
                                <p class="field-help">Your business address</p>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container contact-preview">
                                <p><strong>Visit Our Store</strong></p>
                                <p id="preview-contact-phone">(*************</p>
                                <p id="preview-contact-email"><EMAIL></p>
                                <p id="preview-contact-address">123 Main Street, Anytown, FL 12345</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Logo & Branding Block -->
                <div class="visual-block" data-block="logo">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-format-image"></span> Logo & Branding</h3>
                        <div class="block-actions">
                            <div class="block-status">
                                <span class="status-indicator" data-status="unchanged">Unchanged</span>
                            </div>
                            <div class="section-buttons">
                                <button type="button" class="button button-secondary section-preview-btn" data-section="logo">
                                    <span class="dashicons dashicons-visibility"></span>
                                    Preview
                                </button>
                                <button type="button" class="button button-primary section-apply-btn" data-section="logo">
                                    <span class="dashicons dashicons-yes"></span>
                                    Apply Changes
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-section">
                                <h4>Logo Upload</h4>
                                <div class="form-row">
                                    <label for="logo_upload">Upload New Logo:</label>
                                    <div class="logo-upload-container">
                                        <input type="file" id="logo_upload" name="logo_upload" class="block-input"
                                               accept="image/png,image/jpeg,image/jpg,image/svg+xml,image/webp"
                                               data-preview="logo">
                                        <div class="upload-help">
                                            <p>Supported: PNG, JPG, SVG, WebP | Max: 10MB | Min: 32x32px | Any aspect ratio</p>
                                            <p class="webp-info">💡 JPG/PNG files will be automatically converted to WebP for better performance</p>
                                        </div>
                                    </div>
                                    <div id="upload-results" class="upload-results-container" style="display: none;"></div>
                                </div>

                                <div class="form-row">
                                    <label>Logo Management:</label>
                                    <div class="logo-management-toolbar">
                                        <button type="button" id="restore-previous-logo" class="button button-secondary">
                                            <span class="dashicons dashicons-undo"></span> Restore Previous Logo
                                        </button>
                                        <button type="button" id="remove-current-logo" class="button button-secondary" style="margin-left: 10px;">
                                            <span class="dashicons dashicons-trash"></span> Remove Logo
                                        </button>
                                    </div>
                                    <p class="field-help">Restore the previous logo or remove the current one</p>
                                </div>

                                <div class="form-row" id="logo-editing-tools" style="display: none;">
                                    <label>Logo Editing Tools:</label>
                                    <div class="editing-toolbar">
                                        <button type="button" class="edit-btn" data-tool="crop">
                                            <span class="dashicons dashicons-image-crop"></span> Crop
                                        </button>
                                        <button type="button" class="edit-btn" data-tool="resize">
                                            <span class="dashicons dashicons-image-resize"></span> Resize
                                        </button>
                                        <button type="button" class="edit-btn" data-tool="adjust">
                                            <span class="dashicons dashicons-admin-appearance"></span> Adjust
                                        </button>
                                        <button type="button" class="edit-btn" data-tool="filter">
                                            <span class="dashicons dashicons-art"></span> Filters
                                        </button>
                                    </div>
                                </div>

                                <!-- Crop Controls -->
                                <div class="editing-panel" id="crop-panel" style="display: none;">
                                    <h5>Crop Logo</h5>
                                    <div class="crop-controls">
                                        <label for="aspect_ratio">Aspect Ratio:</label>
                                        <select id="aspect_ratio" class="block-input">
                                            <option value="free">Free Form</option>
                                            <option value="1:1">Square (1:1)</option>
                                            <option value="16:9">Wide (16:9)</option>
                                            <option value="4:3">Standard (4:3)</option>
                                            <option value="3:2">Photo (3:2)</option>
                                        </select>
                                        <button type="button" class="apply-btn" data-action="apply-crop">Apply Crop</button>
                                    </div>
                                </div>

                                <!-- Resize Controls -->
                                <div class="editing-panel" id="resize-panel" style="display: none;">
                                    <h5>Resize Logo</h5>
                                    <div class="resize-controls">
                                        <div class="size-inputs">
                                            <label for="logo_width">Width:</label>
                                            <input type="number" id="logo_width" class="block-input" min="50" max="2000" value="200">
                                            <label for="logo_height">Height:</label>
                                            <input type="number" id="logo_height" class="block-input" min="50" max="2000" value="200">
                                            <label>
                                                <input type="checkbox" id="maintain_aspect" checked> Maintain Aspect Ratio
                                            </label>
                                        </div>
                                        <div class="size-presets">
                                            <button type="button" class="preset-btn" data-size="32,32">Favicon</button>
                                            <button type="button" class="preset-btn" data-size="180,180">Mobile</button>
                                            <button type="button" class="preset-btn" data-size="200,60">Header</button>
                                            <button type="button" class="preset-btn" data-size="400,400">Social</button>
                                        </div>
                                        <button type="button" class="apply-btn" data-action="apply-resize">Apply Resize</button>
                                    </div>
                                </div>

                                <!-- Adjustment Controls -->
                                <div class="editing-panel" id="adjust-panel" style="display: none;">
                                    <h5>Adjust Logo</h5>
                                    <div class="adjustment-controls">
                                        <div class="slider-group">
                                            <label for="brightness">Brightness:</label>
                                            <input type="range" id="brightness" min="-100" max="100" value="0" class="adjustment-slider">
                                            <span class="slider-value">0</span>
                                        </div>
                                        <div class="slider-group">
                                            <label for="contrast">Contrast:</label>
                                            <input type="range" id="contrast" min="-100" max="100" value="0" class="adjustment-slider">
                                            <span class="slider-value">0</span>
                                        </div>
                                        <div class="slider-group">
                                            <label for="saturation">Saturation:</label>
                                            <input type="range" id="saturation" min="-100" max="100" value="0" class="adjustment-slider">
                                            <span class="slider-value">0</span>
                                        </div>
                                        <div class="slider-group">
                                            <label for="hue">Hue:</label>
                                            <input type="range" id="hue" min="0" max="360" value="0" class="adjustment-slider">
                                            <span class="slider-value">0°</span>
                                        </div>
                                        <button type="button" class="apply-btn" data-action="apply-adjustments">Apply Adjustments</button>
                                        <button type="button" class="reset-btn" data-action="reset-adjustments">Reset</button>
                                    </div>
                                </div>

                                <!-- Filter Controls -->
                                <div class="editing-panel" id="filter-panel" style="display: none;">
                                    <h5>Apply Filters</h5>
                                    <div class="filter-controls">
                                        <div class="filter-grid">
                                            <button type="button" class="filter-btn" data-filter="none">None</button>
                                            <button type="button" class="filter-btn" data-filter="grayscale">Grayscale</button>
                                            <button type="button" class="filter-btn" data-filter="sepia">Sepia</button>
                                            <button type="button" class="filter-btn" data-filter="high-contrast">High Contrast</button>
                                            <button type="button" class="filter-btn" data-filter="vintage">Vintage</button>
                                            <button type="button" class="filter-btn" data-filter="modern">Modern</button>
                                        </div>
                                        <button type="button" class="apply-btn" data-action="apply-filter">Apply Filter</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container logo-preview">
                                <h4>Logo Preview</h4>
                                <div class="logo-preview-area">
                                    <div class="preview-logo-container">
                                        <img id="preview-logo" src="" alt="Logo Preview" style="display: none;">
                                        <div class="logo-placeholder">
                                            <span class="dashicons dashicons-format-image"></span>
                                            <p>Upload a logo to see preview</p>
                                        </div>
                                    </div>
                                    <div class="logo-info">
                                        <p><strong>Current Logo:</strong> <span id="logo-filename">None</span></p>
                                        <p><strong>Dimensions:</strong> <span id="logo-dimensions">-</span></p>
                                        <p><strong>File Size:</strong> <span id="logo-filesize">-</span></p>
                                    </div>
                                </div>

                                <!-- Canvas for editing -->
                                <canvas id="logo-edit-canvas" style="display: none; max-width: 100%; border: 1px solid #ddd;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Design System Block -->
                <div class="visual-block" data-block="design">
                    <div class="block-header">
                        <h3><span class="dashicons dashicons-art"></span> Design System</h3>
                        <div class="block-actions">
                            <div class="block-status">
                                <span class="status-indicator" data-status="unchanged">Unchanged</span>
                            </div>
                            <div class="section-buttons">
                                <button type="button" class="button button-secondary section-preview-btn" data-section="design">
                                    <span class="dashicons dashicons-visibility"></span>
                                    Preview
                                </button>
                                <button type="button" class="button button-primary section-apply-btn" data-section="design">
                                    <span class="dashicons dashicons-yes"></span>
                                    Apply Changes
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="block-content">
                        <div class="block-editor">
                            <div class="form-section">
                                <h4>Color Scheme</h4>
                                <div class="form-row">
                                    <label for="primary_color">Primary Color:</label>
                                    <div class="color-input-group">
                                        <input type="color" id="primary_color" name="primary_color" class="block-input color-input"
                                               value="#165C9C" data-preview="primary-color">
                                        <span class="color-value">#165C9C</span>
                                    </div>
                                    <p class="description">"Get Instant Quote" button color</p>
                                </div>
                                <div class="form-row">
                                    <label for="secondary_color">Secondary Color:</label>
                                    <div class="color-input-group">
                                        <input type="color" id="secondary_color" name="secondary_color" class="block-input color-input"
                                               value="#111111" data-preview="secondary-color">
                                        <span class="color-value">#111111</span>
                                    </div>
                                    <p class="description">"Call Us Now" button color</p>
                                </div>
                                <div class="form-row">
                                    <label for="accent_color">Accent Color:</label>
                                    <div class="color-input-group">
                                        <input type="color" id="accent_color" name="accent_color" class="block-input color-input"
                                               value="#FFFFFF" data-preview="accent-color">
                                        <span class="color-value">#FFFFFF</span>
                                    </div>
                                    <p class="description">Additional accent color (background)</p>
                                </div>
                            </div>

                            <div class="form-section">
                                <h4>Button Style</h4>
                                <div class="form-row">
                                    <label for="button_style">Button Shape:</label>
                                    <select id="button_style" name="button_style" class="block-input" data-preview="button-style">
                                        <option value="pill">Pill (Fully Rounded)</option>
                                        <option value="rounded">Modern (Slightly Rounded)</option>
                                        <option value="square">Square (Sharp Corners)</option>
                                    </select>
                                    <p class="description">Choose button corner style</p>
                                </div>
                                <div class="form-row">
                                    <label for="button_size">Button Size:</label>
                                    <select id="button_size" name="button_size" class="block-input" data-preview="button-size">
                                        <option value="small">Small</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="large">Large</option>
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label for="button_shadow">Button Shadow:</label>
                                    <select id="button_shadow" name="button_shadow" class="block-input" data-preview="button-shadow">
                                        <option value="none">None</option>
                                        <option value="subtle">Subtle</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="strong">Strong</option>
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label for="button_hover">Hover Effect:</label>
                                    <select id="button_hover" name="button_hover" class="block-input" data-preview="button-hover">
                                        <option value="none">None</option>
                                        <option value="lift" selected>Lift</option>
                                        <option value="scale">Scale</option>
                                        <option value="glow">Glow</option>
                                        <option value="slide">Slide</option>
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label for="button_text_color">Button Text Color:</label>
                                    <div class="color-input-group">
                                        <input type="color" id="button_text_color" name="button_text_color" class="block-input color-input"
                                               value="#FFFFFF" data-preview="button-text-color">
                                        <span class="color-value">#FFFFFF</span>
                                    </div>
                                    <p class="description">Text color for buttons (independent from background)</p>
                                </div>
                            </div>

                            <div class="form-section">
                                <h4>Typography</h4>
                                <div class="form-row">
                                    <label for="font_pair">Font Combination:</label>
                                    <select id="font_pair" name="font_pair" class="block-input" data-preview="font-pair">
                                        <option value="modern" selected>Modern (Inter)</option>
                                        <option value="classic">Classic (Playfair + Source Sans)</option>
                                        <option value="tech">Tech (Space Grotesk + DM Sans)</option>
                                        <option value="friendly">Friendly (Poppins + Open Sans)</option>
                                    </select>
                                    <p class="description">Professional font combinations</p>
                                </div>
                                <div class="form-row">
                                    <label for="heading_size">Heading Size:</label>
                                    <select id="heading_size" name="heading_size" class="block-input" data-preview="heading-size">
                                        <option value="small">Small</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="large">Large</option>
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label for="body_size">Body Text Size:</label>
                                    <select id="body_size" name="body_size" class="block-input" data-preview="body-size">
                                        <option value="14">14px</option>
                                        <option value="16" selected>16px (Recommended)</option>
                                        <option value="18">18px</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-section">
                                <h4>Spacing & Layout</h4>
                                <div class="form-row">
                                    <label for="section_spacing">Section Spacing:</label>
                                    <select id="section_spacing" name="section_spacing" class="block-input" data-preview="section-spacing">
                                        <option value="compact">Compact</option>
                                        <option value="normal" selected>Normal</option>
                                        <option value="spacious">Spacious</option>
                                    </select>
                                    <p class="description">Padding between sections</p>
                                </div>
                                <div class="form-row">
                                    <label for="container_width">Container Width:</label>
                                    <select id="container_width" name="container_width" class="block-input" data-preview="container-width">
                                        <option value="narrow">Narrow (800px)</option>
                                        <option value="normal" selected>Normal (1200px)</option>
                                        <option value="wide">Wide (1400px)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-section">
                                <h4>Animations</h4>
                                <div class="form-row">
                                    <label for="hero_animation">Hero Animation:</label>
                                    <select id="hero_animation" name="hero_animation" class="block-input" data-preview="hero-animation">
                                        <option value="none">None</option>
                                        <option value="fadeInUp" selected>Fade In Up</option>
                                        <option value="fadeInLeft">Fade In Left</option>
                                        <option value="scaleIn">Scale In</option>
                                        <option value="slideInRight">Slide In Right</option>
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label for="image_animation">Image Animation:</label>
                                    <select id="image_animation" name="image_animation" class="block-input" data-preview="image-animation">
                                        <option value="none">None</option>
                                        <option value="fadeIn" selected>Fade In</option>
                                        <option value="scaleIn">Scale In</option>
                                        <option value="rotateIn">Rotate In</option>
                                        <option value="pulse">Pulse</option>
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label for="animation_speed">Animation Speed:</label>
                                    <select id="animation_speed" name="animation_speed" class="block-input" data-preview="animation-speed">
                                        <option value="fast">Fast (0.3s)</option>
                                        <option value="normal" selected>Normal (0.6s)</option>
                                        <option value="slow">Slow (1s)</option>
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label for="animation_delay">Animation Delay:</label>
                                    <select id="animation_delay" name="animation_delay" class="block-input" data-preview="animation-delay">
                                        <option value="none" selected>None</option>
                                        <option value="short">Short (0.2s)</option>
                                        <option value="medium">Medium (0.5s)</option>
                                        <option value="long">Long (1s)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="block-preview">
                            <div class="preview-container design-preview">
                                <h4>Color Preview</h4>
                                <div class="color-swatches">
                                    <div class="color-swatch">
                                        <div class="swatch" id="preview-primary-color" style="background: #165C9C;"></div>
                                        <span>Primary</span>
                                    </div>
                                    <div class="color-swatch">
                                        <div class="swatch" id="preview-secondary-color" style="background: #111111;"></div>
                                        <span>Secondary</span>
                                    </div>
                                    <div class="color-swatch">
                                        <div class="swatch" id="preview-accent-color" style="background: #FFFFFF; border: 1px solid #ddd;"></div>
                                        <span>Accent</span>
                                    </div>
                                    <div class="color-swatch">
                                        <div class="swatch" id="preview-button-text-color" style="background: #FFFFFF; border: 1px solid #ddd;"></div>
                                        <span>Button Text</span>
                                    </div>
                                </div>

                                <h4>Typography Preview</h4>
                                <div class="typography-preview" id="typography-preview">
                                    <h1 class="preview-heading">Sample Heading</h1>
                                    <p class="preview-body">This is sample body text to show how your typography choices will look on the website.</p>
                                </div>

                                <h4>Button Preview</h4>
                                <div class="button-preview">
                                    <button class="preview-btn primary" id="preview-button-style">Sample Button</button>
                                    <button class="preview-btn secondary" id="preview-button-secondary">Secondary Button</button>
                                </div>

                                <h4>Animation Preview</h4>
                                <div class="animation-preview">
                                    <div class="preview-hero" id="preview-hero-animation">
                                        <h2>Hero Section</h2>
                                        <p>Watch the animation effect</p>
                                        <button class="preview-btn primary">CTA Button</button>
                                    </div>
                                    <div class="preview-image-container" id="preview-image-animation">
                                        <div class="preview-image">📱</div>
                                        <span>Device Image</span>
                                    </div>
                                    <button type="button" class="replay-animation-btn" id="replay-animations">
                                        <span class="dashicons dashicons-controls-play"></span>
                                        Replay Animations
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


















        <!-- Business Info Tab -->
        <div id="business-info-tab" class="tab-content"><?php include 'business-info-single-page.php'; ?></div>

        <!-- AI Configuration Tab -->
        <div id="ai-configuration-tab" class="tab-content">
            <div class="ai-configuration-container">
                <div class="ai-config-header">
                    <h2>🤖 AI Configuration</h2>
                    <p>Configure Claude AI settings for automatic text generation</p>
                </div>

                <!-- API Configuration Section -->
                <div class="config-section">
                    <h3><span class="dashicons dashicons-admin-network"></span> Claude API Settings</h3>
                    <form id="ai-api-config-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="claude_api_key">Claude API Key</label>
                                </th>
                                <td>
                                    <div class="api-key-container">
                                        <input type="password" id="claude_api_key" name="claude_api_key" class="regular-text"
                                               placeholder="sk-ant-api03-..."
                                               value="<?php echo esc_attr($claude_api_key); ?>" />

                                        <?php if ($has_api_key): ?>
                                        <div class="api-key-status">
                                            <span class="api-key-indicator">
                                                <span class="dashicons dashicons-yes-alt"></span>
                                                API Key Configured (<?php echo esc_html($claude_api_key_display); ?>)
                                            </span>
                                            <button type="button" id="clear-api-key" class="button button-secondary button-small">
                                                <span class="dashicons dashicons-dismiss"></span>
                                                Clear Key
                                            </button>
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <p class="description">
                                        Get your API key from <a href="https://console.anthropic.com/" target="_blank">Anthropic Console</a>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="claude_model">AI Model</label>
                                </th>
                                <td>
                                    <select id="claude_model" name="claude_model">
                                        <option value="claude-3-5-haiku-20241022" <?php selected($claude_model, 'claude-3-5-haiku-20241022'); ?>>Claude 3.5 Haiku (Recommended - Fastest & Cheapest)</option>
                                        <option value="claude-sonnet-4-20250514" <?php selected($claude_model, 'claude-sonnet-4-20250514'); ?>>Claude Sonnet 4 (High Performance - $3/$15 per 1M tokens)</option>
                                        <option value="claude-3-7-sonnet-20250219" <?php selected($claude_model, 'claude-3-7-sonnet-20250219'); ?>>Claude Sonnet 3.7 (Extended Thinking - $3/$15 per 1M tokens)</option>
                                        <option value="claude-opus-4-20250514" <?php selected($claude_model, 'claude-opus-4-20250514'); ?>>Claude Opus 4 (Previous Flagship - $15/$75 per 1M tokens)</option>
                                        <option value="claude-opus-4-1-20250805" <?php selected($claude_model, 'claude-opus-4-1-20250805'); ?>>Claude Opus 4.1 (Most Capable - $15/$75 per 1M tokens)</option>
                                    </select>
                                    <div class="model-pricing-info">
                                        <p class="description">
                                            <strong>💰 Pricing:</strong> Claude 3.5 Haiku costs only $0.80/$4.00 per 1M tokens (input/output) -
                                            perfect for device repair content generation. Higher-tier models offer more advanced capabilities at higher costs.
                                        </p>
                                        <p class="description">
                                            <strong>📊 Recommendation:</strong> Claude 3.5 Haiku is ideal for this plugin as it's fast, affordable,
                                            and produces excellent content for device repair websites.
                                        </p>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="claude_temperature">Temperature</label>
                                </th>
                                <td>
                                    <input type="range" id="claude_temperature" name="claude_temperature"
                                           min="0" max="1" step="0.1" value="<?php echo esc_attr($claude_temperature); ?>" />
                                    <span class="temperature-value"><?php echo esc_html($claude_temperature); ?></span>
                                    <p class="description">Controls creativity (0 = focused, 1 = creative)</p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="claude_max_tokens">Max Tokens</label>
                                </th>
                                <td>
                                    <input type="number" id="claude_max_tokens" name="claude_max_tokens"
                                           min="100" max="8000" value="<?php echo esc_attr($claude_max_tokens); ?>" />
                                    <p class="description">Maximum length of generated content (100-8000 tokens). Claude 3.5 Haiku supports up to 8,192 tokens.</p>
                                </td>
                            </tr>
                        </table>

                        <div class="api-actions">
                            <button type="submit" class="button button-primary">Save API Settings</button>
                            <button type="button" id="test-api-connection" class="button button-secondary">Test Connection</button>
                            <?php if ($has_api_key): ?>
                            <button type="button" id="clear-all-config" class="button button-secondary" style="margin-left: 10px;">
                                <span class="dashicons dashicons-dismiss"></span>
                                Clear All Configuration
                            </button>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>

                <!-- AI Prompt Configuration Section -->
                <div class="config-section">
                    <h3><span class="dashicons dashicons-edit"></span> AI Content Generation Prompt</h3>
                    <p>Customize the AI prompt used for content regeneration. This prompt tells the AI how to understand and rewrite your content.</p>

                    <form id="ai-prompt-config-form">
                        <div class="prompt-config-container">
                            <div class="prompt-header">
                                <h4>Default AI Prompt</h4>
                                <p class="description">
                                    This prompt is used when regenerating content. It reads existing content and creates new versions
                                    tailored for the device repair industry. You can customize it to match your specific needs.
                                </p>
                            </div>

                            <div class="prompt-editor">
                                <label for="ai_default_prompt">AI Generation Prompt:</label>
                                <textarea id="ai_default_prompt" name="ai_default_prompt" rows="12" class="large-text code">
<?php
$default_prompt = get_option('website_generator_ai_default_prompt',
'Create exactly 3 different versions of the content below. Use this EXACT format:

**VERSION 1 - CONTEXT-AWARE CONTENT:**
[Rewrite the content to be more professional and trustworthy while maintaining the same message. Focus on building customer confidence and emphasizing benefits like fast service and quality repairs.]

**VERSION 2 - SEO-OPTIMIZED CONTENT:**
[Rewrite the content to include relevant keywords for device repair searches. Include terms like "phone repair," "computer repair," "screen replacement," "battery repair," "same day service," etc.]

**VERSION 3 - GEO-OPTIMIZED CONTENT:**
[Rewrite the content to be comprehensive and authoritative for AI search engines. Include specific details and complete information that AI models can easily reference and quote.]

**Original Content:**
{ORIGINAL_CONTENT}

**Important:**
- Keep the same length as the original
- Maintain any business details exactly
- Use the EXACT format above with the VERSION headers');

echo esc_textarea($default_prompt);
?>
                                </textarea>
                                <p class="description">
                                    <strong>Available Variables:</strong><br>
                                    <code>{ORIGINAL_CONTENT}</code> - The existing content that needs to be rewritten<br>
                                    <code>{BUSINESS_NAME}</code> - Business name (if set in Business Info)<br>
                                    <code>{BUSINESS_PHONE}</code> - Business phone (if set in Business Info)<br>
                                    <code>{BUSINESS_EMAIL}</code> - Business email (if set in Business Info)
                                </p>
                            </div>

                            <div class="prompt-actions">
                                <button type="submit" class="button button-primary">
                                    <span class="dashicons dashicons-yes"></span>
                                    Save Prompt
                                </button>
                                <button type="button" id="reset-default-prompt" class="button button-secondary">
                                    <span class="dashicons dashicons-undo"></span>
                                    Reset to Default
                                </button>
                                <button type="button" id="test-ai-prompt" class="button button-secondary" <?php echo !$has_api_key ? 'disabled' : ''; ?>>
                                    <span class="dashicons dashicons-admin-tools"></span>
                                    Test Prompt
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Usage Statistics Section -->
                <div class="config-section">
                    <h3><span class="dashicons dashicons-chart-bar"></span> Usage Statistics</h3>
                    <div id="usage-stats-container">
                        <p>Loading usage statistics...</p>
                    </div>
                </div>

                <!-- Prompt Templates Section -->
                <div class="config-section">
                    <h3><span class="dashicons dashicons-edit"></span> Prompt Templates</h3>
                    <p>Manage AI prompt templates for different content types</p>

                    <div class="prompt-templates-actions">
                        <button type="button" id="load-prompt-templates" class="button button-secondary">
                            Load Prompt Templates
                        </button>
                        <button type="button" id="reset-default-prompts" class="button button-secondary">
                            Reset to Defaults
                        </button>
                    </div>

                    <div id="prompt-templates-container" style="display: none;">
                        <!-- Prompt templates will be loaded here -->
                    </div>
                </div>



                <!-- AI Generation History Section -->
                <div class="config-section">
                    <h3><span class="dashicons dashicons-backup"></span> AI Generation History</h3>
                    <p>View and manage AI content generation history</p>

                    <div class="history-actions">
                        <button type="button" id="load-ai-history" class="button button-secondary">
                            Load Generation History
                        </button>
                        <button type="button" id="clear-ai-history" class="button button-secondary">
                            Clear History
                        </button>
                    </div>

                    <div id="ai-history-container" style="display: none;">
                        <!-- AI generation history will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Images Tab -->
        <div id="ai-images-tab" class="tab-content">
            <div class="ai-images-container">
                <div class="ai-images-header">
                    <h2>🖼️ Generate Images with AI</h2>
                    <p>Create professional device repair images using Google Gemini 2.5 Flash AI for your hero section</p>
                </div>

                <!-- API Configuration Section -->
                <div class="config-section">
                    <h3><span class="dashicons dashicons-admin-network"></span> Gemini API Settings</h3>
                    <form id="gemini-api-config-form">
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="gemini_api_key">Gemini API Key</label>
                                </th>
                                <td>
                                    <div class="api-key-container">
                                        <input type="password" id="gemini_api_key" name="gemini_api_key" class="regular-text"
                                               placeholder="Enter your Gemini API key..."
                                               value="<?php echo esc_attr(get_option('repairlift_gemini_api_key', '')); ?>" />
                                        
                                        <?php if (get_option('repairlift_gemini_api_key', '')): ?>
                                        <div class="api-key-status">
                                            <span class="api-key-indicator">
                                                <span class="dashicons dashicons-yes-alt"></span>
                                                API Key Configured
                                            </span>
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <p class="description">
                                        Get your API key from <a href="https://aistudio.google.com/app/apikey" target="_blank">Google AI Studio</a>
                                    </p>
                                </td>
                            </tr>
                        </table>

                        <div class="api-actions">
                            <button type="submit" class="button button-primary">Save API Key</button>
                            <button type="button" id="test-gemini-connection" class="button button-secondary">Test Connection</button>
                        </div>
                    </form>
                </div>

                <!-- Image Generation Section -->
                <div class="config-section">
                    <h3><span class="dashicons dashicons-format-image"></span> Generate Hero Images</h3>
                    <p>Create professional device repair images for your website's hero section.</p>

                    <div class="image-generation-container">
                        <div id="prompt-studio-layout" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- Left Column: Prompt Controls -->
                            <div id="prompt-controls-column">
                                <form id="prompt-studio-form">
                                    <table class="form-table">
                                        <!-- Composition -->
                                       <tr>
                                           <th scope="row"><label for="composition">Composition</label></th>
                                           <td>
                                               <select id="composition" name="composition" class="regular-text">
                                                   <option value="a single {{device_model}}">Single Device</option>
                                                   <option value="two {{device_model}}s side-by-side">Two Devices (Side-by-Side)</option>
                                                   <option value="a pyramid of three {{device_model}}s">Three Devices (Pyramid)</option>
                                               </select>
                                               <p class="description">Choose the number and arrangement of devices.</p>
                                           </td>
                                       </tr>
                                       <!-- Device Model -->
                                       <tr>
                                           <th scope="row"><label for="device_model">Device Model</label></th>
                                           <td>
                                               <input type="text" id="device_model" name="device_model" class="regular-text" placeholder="e.g., iPhone 15 Pro">
                                               <p class="description">Enter the primary device or subject of the image.</p>
                                           </td>
                                       </tr>
                                       <!-- Color -->
                                       <tr>
                                           <th scope="row"><label for="device_color">Color</label></th>
                                           <td>
                                               <select id="device_color" name="device_color" class="regular-text">
                                                   <option value="">Default</option>
                                               </select>
                                               <p class="description">Select the device color (options will load based on model).</p>
                                           </td>
                                       </tr>
                                        <!-- Screen State -->
                                        <tr>
                                            <th scope="row"><label for="screen_state">Screen State</label></th>
                                            <td>
                                                <select id="screen_state" name="screen_state" class="regular-text">
                                                    <option value="illuminated_screen" selected>Illuminated Screen</option>
                                                    <option value="dark_screen">Dark Screen</option>
                                                    <option value="broken_screen">Broken Screen</option>
                                                    <option value="abstract_wallpaper">Abstract Wallpaper</option>
                                                </select>
                                                <p class="description">Choose the state of the device's screen.</p>
                                            </td>
                                        </tr>
                                        <!-- Logo Visibility -->
                                        <tr>
                                            <th scope="row"><label for="logo_visibility">Logo Visibility</label></th>
                                            <td>
                                                <select id="logo_visibility" name="logo_visibility" class="regular-text">
                                                    <option value="logo_visible" selected>Logo Visible</option>
                                                    <option value="logo_not_visible">Logo Not Visible</option>
                                                </select>
                                                <p class="description">Specify if the device logo should be visible.</p>
                                            </td>
                                        </tr>
                                        <!-- Angle -->
                                        <tr>
                                            <th scope="row"><label for="angle">Angle</label></th>
                                            <td>
                                                <select id="angle" name="angle">
                                           <option value="straight-on front view">Straight-on (Front)</option>
                                           <option value="straight-on back view">Straight-on (Back)</option>
                                           <option value="angled view showing screen and side">Angled View (Screen & Side)</option>
                                           <option value="angled view showing back and side">Angled View (Back & Side)</option>
                                           <option value="top-down view over the screen">Top-down (Screen)</option>
                                           <option value="top-down view over the back">Top-down (Back)</option>
                                           <option value="side profile view">Side Profile</option>
                                       </select>
                                                <p class="description">Select the camera angle for the shot.</p>
                                            </td>
                                        </tr>
                                        <!-- Lighting Style -->
                                        <tr>
                                            <th scope="row"><label for="lighting_style">Lighting Style</label></th>
                                            <td>
                                                <select id="lighting_style" name="lighting_style" class="regular-text">
                                                    <option value="professional_lighting" selected>Professional Lighting</option>
                                                    <option value="dramatic_lighting">Dramatic Lighting</option>
                                                    <option value="natural_lighting">Natural Lighting</option>
                                                    <option value="studio_lighting">Studio Lighting</option>
                                                </select>
                                                <p class="description">Choose the lighting style for the scene.</p>
                                            </td>
                                        </tr>
                                        <!-- Shadow Intensity -->
                                        <tr>
                                            <th scope="row"><label for="shadow_intensity">Shadow Intensity</label></th>
                                            <td>
                                                <select id="shadow_intensity" name="shadow_intensity" class="regular-text">
                                                    <option value="soft_shadows" selected>Soft Shadows</option>
                                                    <option value="hard_shadows">Hard Shadows</option>
                                                    <option value="no_shadows">No Shadows</option>
                                                </select>
                                                <p class="description">Select the intensity of shadows.</p>
                                            </td>
                                        </tr>
                                        <!-- Background Style -->
                                        <tr>
                                            <th scope="row"><label for="background_style">Background Style</label></th>
                                            <td>
                                                <select id="background_style" name="background_style" class="regular-text">
                                                    <option value="solid white" selected>Solid Color</option>
                                                    <option value="gradient_background">Gradient Background</option>
                                                    <option value="abstract_background">Abstract Background</option>
                                                    <option value="repair_shop_background">Repair Shop Background</option>
                                                </select>
                                                <p class="description">Choose the background for the image.</p>
                                            </td>
                                        </tr>
                                        <!-- Lens Type -->
                                        <tr>
                                            <th scope="row"><label for="lens_type">Lens Type</label></th>
                                            <td>
                                                <select id="lens_type" name="lens_type" class="regular-text">
                                                    <option value="50mm_lens" selected>50mm Lens</option>
                                                    <option value="85mm_lens">85mm Lens</option>
                                                    <option value="macro_lens">Macro Lens</option>
                                                    <option value="wide-angle_lens">Wide-Angle Lens</option>
                                                </select>
                                                <p class="description">Select the camera lens type.</p>
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </div>
                            <!-- Right Column: Live Prompt Preview -->
                            <div id="live-prompt-preview-column">
                                <h4>Live Prompt Preview</h4>
                                <textarea id="live-prompt-preview" readonly rows="10" style="width: 100%; white-space: pre-wrap;"></textarea>
                                <button type="button" class="button" id="copy-prompt-btn" style="margin-top: 10px;">Copy Prompt</button>
                            </div>
                        </div>
                        <div class="generation-actions" style="margin-top: 20px;">
                            <button type="button" class="button button-primary button-large" id="generate-image-btn">
                                <span class="dashicons dashicons-format-image"></span>
                                Generate Image
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Generated Images Gallery -->
                <div class="config-section" id="generated-images-section">
                    <h3><span class="dashicons dashicons-images-alt2"></span> Generated Images</h3>
                    <p>View and manage your AI-generated hero images.</p>

                    <div class="gallery-controls">
                        <button type="button" id="refresh-generated-images" class="button button-secondary">
                            <span class="dashicons dashicons-update"></span>
                            Refresh Gallery
                        </button>
                    </div>

                    <div id="generated-images-gallery" class="images-gallery">
                        <p>Loading images...</p>
                    </div>
                </div>

                <!-- Hero Image Replacement Section -->
                <div class="config-section" id="hero-replacement-section">
                    <h3><span class="dashicons dashicons-admin-appearance"></span> Apply to Hero Section</h3>
                    <p>Replace your current hero image with a generated AI image.</p>

                    <div class="hero-replacement-container">
                        <div class="current-hero-preview">
                            <h4>Current Hero Image</h4>
                            <div id="current-hero-display">
                                <!-- Current hero image will be shown here -->
                                <p>Loading current hero image...</p>
                            </div>
                        </div>

                        <div class="replacement-actions">
                            <button type="button" id="preview-hero-replacement" class="button button-secondary" disabled>
                                <span class="dashicons dashicons-visibility"></span>
                                Preview Replacement
                            </button>
                            <button type="button" id="apply-hero-replacement" class="button button-primary" disabled>
                                <span class="dashicons dashicons-yes"></span>
                                Apply to Hero Section
                            </button>
                            <button type="button" id="revert-hero-image" class="button button-secondary">
                                <span class="dashicons dashicons-undo"></span>
                                Revert to Original
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Airtable Integration Tab -->
        <div id="airtable-integration-tab" class="tab-content">
            <?php include plugin_dir_path(__FILE__) . 'airtable-config.php'; ?>
        </div>

        <!-- Backup & Restore Tab -->
        <div id="backup-restore-tab" class="tab-content">
            <div class="backup-restore-container">
                <div class="page-header">
                    <h2>🛡️ Backup & Restore System</h2>
                    <p>Protect your website with comprehensive backup and restore capabilities. Create backups before making changes and restore if needed.</p>
                </div>

                <!-- Backup Creation Section -->
                <div class="backup-section">
                    <h3><span class="dashicons dashicons-shield"></span> Create New Backup</h3>
                    <p>Choose the type of backup you want to create and provide optional details.</p>

                    <div class="backup-creation-form">
                        <div class="backup-info">
                            <h4>Plugin Backup</h4>
                            <p><strong>What's included:</strong> Plugin options, custom texts, theme modifications, and logo references.</p>
                            <p><em>Note: This backup only includes plugin-specific data, not WordPress core content.</em></p>
                        </div>

                        <div class="backup-details-form">
                            <h4>Backup Details (Optional)</h4>
                            <div class="form-grid">


                                <div class="form-group full-width">
                                    <label for="backup_description">Description</label>
                                    <textarea id="backup_description" placeholder="Describe what changes you're about to make..." rows="3" class="regular-text"></textarea>
                                    <small>Optional description to help you remember why this backup was created</small>
                                </div>
                            </div>

                            <div class="backup-actions">
                                <button type="button" id="create-backup" class="button button-primary button-large">
                                    <span class="dashicons dashicons-shield"></span>
                                    Create Backup
                                </button>
                                <button type="button" id="clear-backup-form" class="button button-secondary">
                                    <span class="dashicons dashicons-dismiss"></span>
                                    Clear Form
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Backup Creation Section -->
                <div class="backup-section">
                    <h3><span class="dashicons dashicons-plus-alt"></span> Create Manual Backup</h3>
                    <p>Create a snapshot of your current website configuration.</p>

                    <div class="manual-backup-controls">
                        <button type="button" id="create-manual-backup" class="button button-primary">
                            <span class="dashicons dashicons-backup"></span>
                            Create Backup Now
                        </button>
                        <p class="description">This will create a backup of your current colors, content, and settings.</p>
                    </div>
                </div>

                <!-- Backup Management Section -->
                <div class="backup-section">
                    <h3><span class="dashicons dashicons-backup"></span> Manage Existing Backups</h3>
                    <p>View, restore, or delete your existing backups.</p>

                    <div class="backup-management">
                        <div class="backup-controls">
                            <button type="button" id="load-backups" class="button button-secondary">
                                <span class="dashicons dashicons-download"></span>
                                Load Backup List
                            </button>
                            <button type="button" id="refresh-backups" class="button button-secondary" style="display: none;">
                                <span class="dashicons dashicons-update"></span>
                                Refresh List
                            </button>
                        </div>

                        <div id="backup-list-container" style="display: none;">
                            <!-- Backup list will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Debug Section -->
                <div class="backup-section">
                    <h3><span class="dashicons dashicons-admin-tools"></span> Debug & Troubleshooting</h3>
                    <p>Use these tools if backups are not working properly.</p>

                    <div class="debug-tools">

                        <button type="button" id="check-table-status" class="button button-secondary">
                            <span class="dashicons dashicons-search"></span>
                            Check Table Status
                        </button>
                        <button type="button" id="reset-to-defaults" class="button button-secondary">
                            <span class="dashicons dashicons-undo"></span>
                            Reset All to Defaults
                        </button>
                        <button type="button" id="restore-default-content" class="button button-secondary">
                            <span class="dashicons dashicons-text"></span>
                            Restore Default Content
                        </button>
                    </div>

                    <div id="debug-results" style="display: none; margin-top: 15px;">
                        <!-- Debug results will be shown here -->
                    </div>
                </div>
            </div>
        </div>

    </div>
    
    <!-- Results Section -->
    <div id="generation-results" style="display: none; margin-top: 30px;">
        <h3>Generation Results</h3>
        <div id="results-content"></div>
    </div>
</div>

<style>
/* Tab Navigation */
.nav-tab-wrapper {
    margin-bottom: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Visual Block Editor Styles */
.visual-editor-header {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    margin-bottom: 20px;
    border-radius: 4px;
}

.editor-controls {
    margin-top: 15px;
}

.editor-controls .button {
    margin-right: 10px;
}

.visual-blocks-container {
    display: grid;
    gap: 20px;
}

.visual-block {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.block-header {
    background: #f1f1f1;
    padding: 15px 20px;
    border-bottom: 1px solid #ccd0d4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.block-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.section-buttons {
    display: flex;
    gap: 8px;
}

.section-preview-btn,
.section-apply-btn {
    padding: 6px 12px;
    font-size: 13px;
    line-height: 1.4;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}

.section-preview-btn {
    background: #f6f7f7;
    border-color: #ddd;
    color: #555;
}

.section-preview-btn:hover {
    background: #e8f4f8;
    border-color: #0073aa;
    color: #0073aa;
}

.section-apply-btn {
    background: #0073aa;
    border-color: #0073aa;
    color: white;
}

.section-apply-btn:hover {
    background: #005177;
    border-color: #005177;
    color: white;
}

.section-apply-btn:disabled,
.section-preview-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.editor-help {
    background: #e7f3ff;
    border: 1px solid #b8daff;
    border-radius: 4px;
    padding: 10px 15px;
    margin-left: 15px;
    flex: 1;
}

.editor-help p {
    margin: 0;
    font-size: 13px;
    color: #004085;
}

/* Section Preview Modal */
.section-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-preview-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    max-height: 80vh;
    width: 90%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.section-preview-header {
    background: #0073aa;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-preview-header h3 {
    margin: 0;
    font-size: 18px;
}

.section-preview-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.section-preview-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.section-preview-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.section-preview-footer {
    background: #f9f9f9;
    padding: 20px;
    border-top: 1px solid #e5e5e5;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.preview-content {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin-top: 15px;
}

.preview-content h2,
.preview-content h3 {
    margin-top: 0;
    color: #23282d;
}

.contact-preview p {
    margin: 8px 0;
}

.block-header h3 {
    margin: 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator[data-status="unchanged"] {
    background: #e5e5e5;
    color: #666;
}

.status-indicator[data-status="modified"] {
    background: #fff3cd;
    color: #856404;
}

.status-indicator[data-status="saved"] {
    background: #d4edda;
    color: #155724;
}

.block-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px;
}

.block-editor {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-row label {
    font-weight: 600;
    color: #23282d;
}

.block-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.block-input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

.block-preview {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.preview-container {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.hero-preview h1 {
    font-size: 32px;
    margin: 0 0 10px 0;
    color: #333;
}

.hero-preview p {
    font-size: 18px;
    margin: 0 0 20px 0;
    color: #666;
}

.preview-buttons {
    display: flex;
    gap: 10px;
}

.preview-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.preview-btn.primary {
    background: #165C9C;
    color: #FFFFFF;
}

.preview-btn.secondary {
    background: #111111;
    color: #FFFFFF;
}

.preview-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Design System Styles */
.form-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.color-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-input {
    width: 50px !important;
    height: 40px !important;
    padding: 0 !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    cursor: pointer;
}

.color-value {
    font-family: monospace;
    font-size: 12px;
    color: #666;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 3px;
    min-width: 70px;
    text-align: center;
}

.color-swatches {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.color-swatch {
    text-align: center;
}

.swatch {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    border: 2px solid #ddd;
    margin-bottom: 5px;
    transition: transform 0.2s;
}

.swatch:hover {
    transform: scale(1.1);
}

.color-swatch span {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.button-preview {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.design-preview h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

/* Button Style Variations */
.preview-btn.pill {
    border-radius: 65px;
}

.preview-btn.rounded {
    border-radius: 8px;
}

.preview-btn.square {
    border-radius: 0px;
}

.preview-btn.small {
    padding: 8px 20px;
    font-size: 14px;
}

.preview-btn.medium {
    padding: 12px 24px;
    font-size: 16px;
}

.preview-btn.large {
    padding: 16px 32px;
    font-size: 18px;
}

.description {
    font-size: 12px;
    color: #666;
    margin: 5px 0 0 0;
    font-style: italic;
}

/* Logo Editing Styles */
.logo-upload-container {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.3s;
}

.logo-upload-container:hover {
    border-color: #0073aa;
}

.logo-upload-container.dragover {
    border-color: #0073aa;
    background-color: #f0f8ff;
}

.upload-help {
    margin-top: 10px;
}

.upload-help p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.editing-toolbar {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.edit-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: #f7f7f7;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s;
}

.edit-btn:hover {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.edit-btn.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.editing-panel {
    margin-top: 15px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.editing-panel h5 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

.crop-controls, .resize-controls, .adjustment-controls, .filter-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.size-inputs {
    display: grid;
    grid-template-columns: auto 1fr auto 1fr;
    gap: 10px;
    align-items: center;
}

.size-inputs input[type="number"] {
    width: 80px;
}

.size-presets {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.preset-btn {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s;
}

.preset-btn:hover {
    background: #f0f0f0;
    border-color: #0073aa;
}

.slider-group {
    display: grid;
    grid-template-columns: 100px 1fr 50px;
    gap: 10px;
    align-items: center;
}

.adjustment-slider {
    width: 100%;
}

.slider-value {
    font-size: 12px;
    color: #666;
    text-align: center;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
}

.filter-btn {
    padding: 10px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s;
}

.filter-btn:hover {
    background: #f0f0f0;
    border-color: #0073aa;
}

.filter-btn.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.apply-btn {
    padding: 10px 20px;
    background: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background 0.2s;
}

.apply-btn:hover {
    background: #005a87;
}

.reset-btn {
    padding: 10px 20px;
    background: #666;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
    transition: background 0.2s;
}

.reset-btn:hover {
    background: #444;
}

.logo-preview-area {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.preview-logo-container {
    position: relative;
    min-height: 200px;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9f9f9;
}

.logo-placeholder {
    text-align: center;
    color: #666;
}

.logo-placeholder .dashicons {
    font-size: 48px;
    margin-bottom: 10px;
    opacity: 0.5;
}

.logo-placeholder p {
    margin: 0;
    font-size: 14px;
}

#preview-logo {
    max-width: 100%;
    max-height: 200px;
    object-fit: contain;
}

.logo-info {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
}

.logo-info p {
    margin: 5px 0;
}

#logo-edit-canvas {
    margin-top: 15px;
    border-radius: 4px;
}

/* Service Page Editor Styles */
.service-page-editor {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
}

.page-header {
    background: #f1f1f1;
    padding: 20px;
    border-bottom: 1px solid #ccd0d4;
}

.page-header h2 {
    margin: 0 0 10px 0;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-header p {
    margin: 0 0 15px 0;
    color: #666;
}

.page-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.service-content-blocks, .global-content-blocks {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.content-block {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
}

.content-block h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.block-fields {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.block-fields .form-row {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.block-fields label {
    font-weight: 600;
    color: #23282d;
    font-size: 14px;
}

.block-fields .block-input {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
    background: white;
}

.block-fields .block-input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Field Help Text */
.field-help {
    font-size: 12px;
    color: #666;
    margin: 5px 0 0 0;
    font-style: italic;
    line-height: 1.4;
}

/* Enhanced Form Styling */
.form-row {
    margin-bottom: 20px;
}

.form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #23282d;
}

.block-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    background: white;
    font-family: inherit;
}

.block-input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Textarea specific styling */
textarea.block-input {
    resize: vertical;
    min-height: 80px;
    line-height: 1.5;
}

/* Content State Indicators */
.block-input.has-content {
    border-left: 3px solid #46b450;
    background: #f9fff9;
}

.block-input.is-empty {
    border-left: 3px solid #ddd;
    background: #fafafa;
}

.block-input.is-focused {
    background: #fff;
    border-color: #0073aa;
}

/* Enhanced field help styling */
.field-help {
    font-size: 12px;
    color: #666;
    margin: 5px 0 0 0;
    font-style: italic;
    line-height: 1.4;
    padding: 5px 10px;
    background: #f8f9fa;
    border-radius: 3px;
    border-left: 3px solid #0073aa;
}

/* AI Regeneration Styles */
.has-ai-regeneration {
    position: relative;
}

.ai-regeneration-container {
    margin-top: 8px;
    position: relative;
}

.ai-regenerate-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.ai-regenerate-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

.ai-regenerate-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.ai-regenerate-btn.ai-not-ready {
    background: #f0f0f0;
    color: #666;
    border: 1px solid #ddd;
}

.ai-icon.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* AI Suggestions Panel - Modal Overlay */
.ai-suggestions {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.ai-suggestions-modal {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    max-width: 800px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.ai-suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    flex-shrink: 0;
}

.ai-suggestions-header h4 {
    margin: 0;
    font-size: 14px;
    color: #23282d;
}

.ai-close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.ai-close-btn:hover {
    background: #e0e0e0;
    color: #333;
}

.ai-suggestions-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    min-height: 200px;
}

.ai-suggestion-item {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 12px;
    background: white;
    transition: all 0.2s;
}

.ai-suggestion-item:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
}

.ai-suggestion-item.suggestion-updated {
    border-color: #46b450;
    background: #f0f8f0;
    animation: suggestionUpdate 0.5s ease-in-out;
}

@keyframes suggestionUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.suggestion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 6px 6px 0 0;
}

.suggestion-type {
    display: flex;
    align-items: center;
    gap: 8px;
}

.suggestion-label {
    background: #0073aa;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.character-count {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    border: 1px solid #c8e6c9;
}

.character-count.char-warning {
    background: #fff3e0;
    color: #f57c00;
    border-color: #ffcc02;
}

.char-limit-warning {
    padding: 8px 16px;
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    color: #e65100;
    font-size: 12px;
    font-weight: 500;
    margin-top: 8px;
}

.ai-length-warning {
    padding: 8px 16px;
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    color: #1565c0;
    font-size: 12px;
    font-weight: 500;
    margin-top: 8px;
}

.suggestion-purpose {
    padding: 8px 16px;
    background: #f0f8ff;
    border-bottom: 1px solid #e0e0e0;
    font-size: 12px;
    color: #555;
    line-height: 1.4;
}

.regenerate-single-btn {
    background: #f0f0f0 !important;
    border: 1px solid #ccc !important;
    color: #333 !important;
    font-size: 11px !important;
    padding: 4px 8px !important;
    height: auto !important;
    line-height: 1.2 !important;
}

.regenerate-single-btn:hover {
    background: #e0e0e0 !important;
    border-color: #999 !important;
}

.suggestion-number {
    font-size: 12px;
    font-weight: 600;
    color: #666;
}

.suggestion-actions {
    display: flex;
    gap: 6px;
}

.suggestion-actions .button {
    padding: 4px 8px;
    font-size: 11px;
    height: auto;
    line-height: 1.2;
}

.suggestion-content {
    padding: 16px;
    font-size: 13px;
    line-height: 1.5;
    color: #333;
    background: white;
    border-radius: 0 0 6px 6px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.ai-suggestions-actions {
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #ddd;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    flex-shrink: 0;
}

.ai-loading {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.ai-error {
    padding: 12px;
    background: #ffeaea;
    border: 1px solid #ffcccc;
    border-radius: 4px;
    color: #d63638;
    font-size: 13px;
}

/* AI Preview Modal */
.ai-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-preview-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 70vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.ai-preview-header {
    padding: 16px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.ai-preview-header h3 {
    margin: 0;
    font-size: 16px;
    color: #23282d;
}

.ai-preview-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.ai-preview-close:hover {
    background: #e0e0e0;
    color: #333;
}

.ai-preview-body {
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.preview-content {
    font-size: 14px;
    line-height: 1.5;
    color: #333;
}

.ai-preview-footer {
    padding: 16px 20px;
    border-top: 1px solid #ddd;
    background: #f8f9fa;
    text-align: right;
}

/* AI Undo Container */
.ai-undo-container {
    display: inline-flex;
    gap: 6px;
    margin-left: 8px;
    margin-top: 4px;
}

.ai-undo-btn, .ai-restore-backup-btn {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.ai-undo-btn {
    background: #ff6b35;
    color: white;
}

.ai-undo-btn:hover {
    background: #e55a2b;
}

.ai-restore-backup-btn {
    background: #0073aa;
    color: white;
}

.ai-restore-backup-btn:hover {
    background: #005a87;
}

.ai-undo-btn .dashicons, .ai-restore-backup-btn .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* AI Notice Styling */
.ai-notice {
    margin: 10px 0;
}

/* Model Pricing Info */
.model-pricing-info {
    margin-top: 10px;
    padding: 15px;
    background: #f0f8ff;
    border-left: 4px solid #0073aa;
    border-radius: 4px;
}

.model-pricing-info .description {
    margin: 8px 0;
    font-size: 13px;
    line-height: 1.4;
}

.model-pricing-info strong {
    color: #0073aa;
}

/* API Key Configuration */
.api-key-container {
    position: relative;
}

.api-key-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 8px;
    padding: 8px 12px;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    font-size: 13px;
}

.api-key-indicator {
    display: flex;
    align-items: center;
    color: #155724;
    font-weight: 500;
}

.api-key-indicator .dashicons {
    color: #28a745;
    margin-right: 5px;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

#clear-api-key {
    padding: 4px 8px;
    font-size: 11px;
    height: auto;
    line-height: 1.2;
    display: flex;
    align-items: center;
    gap: 4px;
}

#clear-api-key .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
}

.button-small {
    padding: 4px 8px !important;
    font-size: 11px !important;
    height: auto !important;
}

/* AI Prompt Configuration */
.prompt-config-container {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    margin-top: 15px;
}

.prompt-header h4 {
    margin: 0 0 10px 0;
    color: #23282d;
    font-size: 16px;
}

.prompt-header .description {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 13px;
    line-height: 1.5;
}

.prompt-editor {
    margin-bottom: 20px;
}

.prompt-editor label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #23282d;
}

.prompt-editor textarea {
    width: 100%;
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
    line-height: 1.5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    background: #fff;
    resize: vertical;
}

.prompt-editor textarea:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
    outline: none;
}

.prompt-editor .description {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.prompt-editor .description code {
    background: #f1f1f1;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
    color: #d63384;
}

.prompt-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.prompt-actions .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

.prompt-actions .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.prompt-actions .button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* AI Regeneration Basic Mode Indicator */
.ai-regenerate-btn.ai-basic-mode {
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    border-color: #005177;
    position: relative;
}

.ai-regenerate-btn.ai-basic-mode::after {
    content: "BASIC";
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff6b35;
    color: white;
    font-size: 9px;
    font-weight: bold;
    padding: 2px 4px;
    border-radius: 3px;
    line-height: 1;
    z-index: 10;
}

/* Backup & Restore Tab Styles */
.backup-restore-container {
    max-width: 1200px;
    margin: 0 auto;
}

.backup-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.backup-section h3 {
    margin: 0 0 15px 0;
    color: #23282d;
    font-size: 20px;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.backup-section h4 {
    margin: 20px 0 15px 0;
    color: #23282d;
    font-size: 16px;
}

/* Backup Type Selection */
.backup-type-selection {
    margin-bottom: 30px;
}

.backup-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.backup-option-card {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s;
    background: #fafafa;
}

.backup-option-card:hover {
    border-color: #0073aa;
    background: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 115, 170, 0.1);
}

.backup-option-card input[type="radio"] {
    display: none;
}

.backup-option-card input[type="radio"]:checked + .option-content {
    color: #0073aa;
}

.backup-option-card input[type="radio"]:checked + .option-content .option-icon {
    background: #0073aa;
    color: white;
}

.option-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.option-icon {
    font-size: 24px;
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.3s;
}

.option-details strong {
    display: block;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
}

.option-details p {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.option-details small {
    color: #888;
    font-size: 12px;
    font-style: italic;
}

/* Backup Details Form */
.backup-details-form {
    border-top: 1px solid #e0e0e0;
    padding-top: 30px;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #23282d;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

.form-group small {
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

/* Single-Page Business Info Form Styles */
.business-info-single-container {
    max-width: 1000px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.business-info-header {
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.business-info-header h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
}

.business-info-header p {
    margin: 0 0 20px 0;
    font-size: 16px;
    opacity: 0.9;
}

.completion-status {
    max-width: 400px;
    margin: 0 auto;
}

.completion-indicator {
    text-align: center;
}

.completion-text {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.completion-bar {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
}

.completion-fill {
    background: #4CAF50;
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.business-info-single-form {
    padding: 0;
}

.form-section {
    border-bottom: 1px solid #e5e5e5;
    padding: 30px;
}

.form-section:last-of-type {
    border-bottom: none;
}

.section-header {
    margin-bottom: 25px;
}

.section-header h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #23282d;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-icon {
    font-size: 22px;
}

.section-description {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group.required label::after {
    content: " *";
    color: #d63638;
    font-weight: bold;
}

.form-group select {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    transition: border-color 0.2s;
}

.form-group select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-top: 8px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    background: #fafafa;
}

.checkbox-item:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.checkbox-item input[type="checkbox"] {
    margin: 0;
}

.checkbox-item input[type="checkbox"]:checked + .checkbox-label {
    font-weight: 600;
    color: #0073aa;
}

.checkbox-label {
    font-size: 14px;
    margin: 0;
    cursor: pointer;
}

.field-help {
    color: #666 !important;
    font-style: italic;
}

.form-actions {
    padding: 30px;
    background: #f9f9f9;
    border-top: 1px solid #e5e5e5;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.form-actions .button {
    padding: 12px 24px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-actions .button-large {
    padding: 15px 30px;
}

.form-actions .button-danger {
    background: #d63638;
    border-color: #d63638;
    color: white;
}

.form-actions .button-danger:hover {
    background: #b32d2e;
    border-color: #b32d2e;
    color: white;
}

/* AI Preview Modal */
.ai-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-preview-content {
    background: white;
    border-radius: 8px;
    max-width: 800px;
    max-height: 80vh;
    width: 90%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.ai-preview-header {
    background: #0073aa;
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-preview-header h3 {
    margin: 0;
    font-size: 18px;
}

.ai-preview-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.ai-preview-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ai-preview-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.preview-comparison {
    margin-bottom: 25px;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 20px;
}

.preview-comparison:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.preview-comparison h4 {
    margin: 0 0 15px 0;
    color: #23282d;
    font-size: 16px;
}

.comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.before-content label,
.after-content label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
}

.before-content label {
    color: #666;
}

.after-content label {
    color: #0073aa;
}

.content-box {
    padding: 15px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.5;
    min-height: 50px;
}

.content-box.current {
    background: #f5f5f5;
    border: 1px solid #ddd;
    color: #666;
}

.content-box.preview {
    background: #f0f8ff;
    border: 1px solid #0073aa;
    color: #23282d;
    font-weight: 500;
}

.preview-note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-top: 20px;
}

.preview-note p {
    margin: 0;
    color: #856404;
    font-style: italic;
}

.ai-preview-footer {
    background: #f9f9f9;
    padding: 20px;
    border-top: 1px solid #e5e5e5;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
    .business-info-single-container {
        margin: 10px;
        border-radius: 0;
    }

    .business-info-header {
        padding: 20px;
    }

    .business-info-header h2 {
        font-size: 24px;
    }

    .form-section {
        padding: 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        padding: 20px;
        flex-direction: column;
    }

    .form-actions .button {
        width: 100%;
        justify-content: center;
    }

    .ai-preview-content {
        width: 95%;
        max-height: 90vh;
    }

    .comparison-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .ai-preview-footer {
        flex-direction: column;
    }

    .ai-preview-footer .button {
        width: 100%;
        justify-content: center;
    }
}

.backup-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

/* Backup Management */
.backup-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.backup-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    background: #fafafa;
    transition: border-color 0.2s;
}

.backup-item:hover {
    border-color: #0073aa;
}

.backup-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.backup-item-title {
    font-weight: 600;
    color: #23282d;
    font-size: 16px;
}

.backup-type {
    background: #0073aa;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin-left: 10px;
}

.backup-item-date {
    font-size: 13px;
    color: #666;
}

.backup-item-info {
    margin-bottom: 15px;
}

.backup-item-info p {
    margin: 5px 0;
    font-size: 13px;
    color: #555;
}

.backup-item-actions {
    display: flex;
    gap: 10px;
}

.backup-item-actions .button {
    padding: 6px 12px;
    font-size: 12px;
    height: auto;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Backup Information Cards */
.backup-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.info-card {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
}

.info-card h4 {
    margin: 0 0 15px 0;
    color: #0073aa;
    font-size: 16px;
}

.info-card p {
    margin: 0 0 10px 0;
    color: #555;
    font-size: 14px;
    line-height: 1.5;
}

.info-card ul {
    margin: 10px 0 0 0;
    padding-left: 20px;
}

.info-card li {
    margin-bottom: 5px;
    color: #555;
    font-size: 13px;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .backup-section {
        padding: 20px;
    }

    .backup-options {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .backup-controls {
        flex-direction: column;
    }

    .backup-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .backup-item-actions {
        flex-direction: column;
    }

    .backup-actions {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Responsive Design for AI */
@media (max-width: 768px) {
    .ai-suggestions {
        padding: 10px;
    }

    .ai-suggestions-modal {
        width: 95%;
        max-height: 85vh;
    }

    .suggestion-actions {
        flex-direction: column;
    }

    .ai-suggestions-actions {
        flex-direction: column;
    }
}

/* Content status indicators */
.content-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: 10px;
}

.content-status.loaded {
    background: #d4edda;
    color: #155724;
}

.content-status.modified {
    background: #fff3cd;
    color: #856404;
}

.content-status.saved {
    background: #cce5ff;
    color: #004085;
}

/* Global Settings Specific Styles */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.benefit-item {
    background: white;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.benefit-item h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #0073aa;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Tab Navigation Enhancement */
.nav-tab-wrapper .nav-tab {
    font-size: 13px;
    padding: 8px 12px;
}

.nav-tab-wrapper .nav-tab:hover {
    background-color: #f0f0f0;
}

/* Enhanced Design System Styles */

/* Typography Preview */
.typography-preview {
    background: white;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
}

.preview-heading {
    margin: 0 0 10px 0;
    color: #333;
    transition: all 0.3s ease;
}

.preview-body {
    margin: 0;
    color: #666;
    transition: all 0.3s ease;
}

/* Font Family Classes */
.font-modern {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.font-classic .preview-heading {
    font-family: 'Playfair Display', Georgia, serif;
}
.font-classic .preview-body {
    font-family: 'Source Sans Pro', Arial, sans-serif;
}

.font-tech .preview-heading {
    font-family: 'Space Grotesk', 'Inter', sans-serif;
}
.font-tech .preview-body {
    font-family: 'DM Sans', 'Inter', sans-serif;
}

.font-friendly .preview-heading {
    font-family: 'Poppins', 'Inter', sans-serif;
}
.font-friendly .preview-body {
    font-family: 'Open Sans', Arial, sans-serif;
}

/* Heading Sizes */
.heading-small .preview-heading { font-size: 24px; }
.heading-medium .preview-heading { font-size: 32px; }
.heading-large .preview-heading { font-size: 42px; }

/* Body Sizes */
.body-14 .preview-body { font-size: 14px; }
.body-16 .preview-body { font-size: 16px; }
.body-18 .preview-body { font-size: 18px; }

/* Button Shadow Effects */
.shadow-none { box-shadow: none; }
.shadow-subtle { box-shadow: 0 1px 2px rgba(0,0,0,0.05); }
.shadow-medium { box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
.shadow-strong { box-shadow: 0 10px 15px rgba(0,0,0,0.1); }

/* Button Hover Effects */
.hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.hover-scale {
    transition: transform 0.2s ease;
}
.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}
.hover-glow:hover {
    box-shadow: 0 0 20px rgba(22,92,156,0.4);
}

.hover-slide {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}
.hover-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}
.hover-slide:hover::before {
    left: 100%;
}

/* Animation Preview */
.animation-preview {
    background: #f5f5f5;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 20px;
}

.preview-hero {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-hero h2 {
    margin: 0 0 10px 0;
    color: #333;
}

.preview-hero p {
    margin: 0 0 20px 0;
    color: #666;
}

.preview-image-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

.preview-image {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.replay-animation-btn {
    background: #0073aa;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    margin: 0 auto;
    transition: background 0.2s ease;
}

.replay-animation-btn:hover {
    background: #005a87;
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-10deg) scale(0.9);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Animation Classes */
.animate-fadeInUp { animation: fadeInUp 0.6s ease forwards; }
.animate-fadeInLeft { animation: fadeInLeft 0.6s ease forwards; }
.animate-scaleIn { animation: scaleIn 0.6s ease forwards; }
.animate-slideInRight { animation: slideInRight 0.6s ease forwards; }
.animate-fadeIn { animation: fadeIn 0.6s ease forwards; }
.animate-rotateIn { animation: rotateIn 0.6s ease forwards; }
.animate-pulse { animation: pulse 2s ease-in-out infinite; }

/* Animation Speed Modifiers */
.animate-fast { animation-duration: 0.3s !important; }
.animate-normal { animation-duration: 0.6s !important; }
.animate-slow { animation-duration: 1s !important; }

/* Animation Delay Modifiers */
.animate-delay-short { animation-delay: 0.2s !important; }
.animate-delay-medium { animation-delay: 0.5s !important; }
.animate-delay-long { animation-delay: 1s !important; }

/* Responsive Design for Service Pages */
@media (max-width: 768px) {
    .page-controls {
        flex-direction: column;
    }

    .page-controls .button {
        width: 100%;
        justify-content: center;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .nav-tab-wrapper {
        overflow-x: auto;
        white-space: nowrap;
    }

    .nav-tab-wrapper .nav-tab {
        display: inline-block;
        min-width: 120px;
        text-align: center;
    }

    .preview-hero {
        padding: 20px;
    }

    .preview-image {
        font-size: 36px;
    }

    .typography-preview {
        padding: 15px;
    }
}

/* Upload Progress and Results Styles */
.upload-progress {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
}

.progress-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.upload-results {
    background: #f0f8ff;
    border: 1px solid #0073aa;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.upload-results h4 {
    margin: 0 0 10px 0;
    color: #0073aa;
    font-size: 16px;
}

.upload-results .success {
    color: #46b450;
    font-weight: 600;
    margin: 5px 0;
}

.upload-results .info {
    color: #666;
    margin: 5px 0;
    font-size: 14px;
}

.webp-results {
    background: #e8f5e8;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.size-comparison {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

.size-comparison span {
    background: white;
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #ddd;
}

/* Fix Button Icon Alignment */
.button {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    vertical-align: middle !important;
    line-height: 1.4 !important;
}

.button .dashicons {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 18px !important;
    height: 18px !important;
    font-size: 18px !important;
    line-height: 1 !important;
    vertical-align: middle !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
}

/* Editor controls specific styling */
.editor-controls .button,
.page-controls .button {
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: 4px !important;
}

.editor-controls {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 20px;
}

.page-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

/* Ensure proper button text alignment */
.button-text {
    display: inline-block;
    vertical-align: middle;
    line-height: 1.4;
}

/* Management Sections */
.management-section {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    margin-bottom: 20px;
    border-radius: 4px;
}

.section-actions {
    margin-top: 15px;
}

.section-actions .button {
    margin-right: 10px;
}

.results-container {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Form Styles */
.generator-form-section {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
}

/* Loading Overlay */
#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .block-content {
        grid-template-columns: 1fr;
    }

    .preview-buttons {
        flex-direction: column;
    }

    .editor-controls .button {
        margin-bottom: 10px;
    }
}

.color-preview {
    border-radius: 3px;
    vertical-align: middle;
}

#logo-preview img {
    max-width: 200px;
    max-height: 100px;
    border: 1px solid #ddd;
    padding: 5px;
    background: #fff;
}

.form-table th {
    width: 200px;
}

.button-large {
    height: auto !important;
    line-height: 1.4 !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
}

.button .dashicons {
    margin-right: 5px;
    margin-top: -2px;
}

#generation-results {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.success-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 12px;
    border-radius: 4px;
}

/* Logo Management Toolbar */
.logo-management-toolbar {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 5px;
}

.logo-management-toolbar .button {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.logo-management-toolbar .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}
    margin: 10px 0;
}

.error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 12px;
    border-radius: 4px;
    margin: 10px 0;
}
</style>

<script>
jQuery(document).ready(function($) {
    // ========================================
    // 🚀 INITIALIZATION CONSOLE LOGGING
    // ========================================
    console.log('🎉 [ADMIN] RepairLift WP Customizer Admin loaded successfully!');
    console.log('🔧 [ADMIN] jQuery version:', $.fn.jquery);
    console.log('🌐 [ADMIN] AJAX URL:', website_generator_ajax.ajax_url);
    console.log('🔑 [ADMIN] Nonce configured:', website_generator_ajax.nonce ? 'YES' : 'NO');
    
    // Validate AJAX configuration
    if (!website_generator_ajax || !website_generator_ajax.ajax_url || !website_generator_ajax.nonce) {
        console.error('❌ [ADMIN] AJAX configuration missing or incomplete!');
        console.log('📊 [ADMIN] Available AJAX object:', website_generator_ajax);
    } else {
        console.log('✅ [ADMIN] AJAX configuration validated successfully');
    }

    // Initialize AI Images functionality
    console.log('🖼️ [AI-IMAGES] Initializing AI image generation system...');
    
    // Check for AI Images tab
    const aiImagesTab = $('#ai-images-tab');
    if (aiImagesTab.length) {
        console.log('✅ [AI-IMAGES] AI Images tab found, functionality available');
        
        // Initialize current hero image loading
        console.log('🖼️ [AI-IMAGES] Setting up hero image loading...');
        loadCurrentHeroImage();
    } else {
        console.log('⚠️ [AI-IMAGES] AI Images tab not found');
    }

    // ========================================
    // END INITIALIZATION
    // ========================================

    // Update temperature display
    $('#claude_temperature').on('input', function() {
        $('.temperature-value').text($(this).val());
    });

    // Handle Clear API Key
    $('#clear-api-key').on('click', function() {
        if (!confirm('Are you sure you want to clear the Claude API configuration? This will remove your API key and reset all settings.')) {
            return;
        }

        const $btn = $(this);
        const originalText = $btn.text();

        // Show loading state
        $btn.prop('disabled', true).text('Clearing...');

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'clear_ai_config',
                nonce: website_generator_ajax.nonce,
                clear_stats: false // Don't clear usage statistics by default
            },
            success: function(response) {
                if (response.success) {
                    showNotice('✅ AI configuration cleared successfully!', 'success');

                    // Clear form fields
                    $('#claude_api_key').val('');
                    $('#claude_model').val('claude-3-5-haiku-20241022');
                    $('#claude_temperature').val('0.7');
                    $('.temperature-value').text('0.7');
                    $('#claude_max_tokens').val('1000');

                    // Hide the API key status
                    $('.api-key-status').fadeOut();

                    // Reload page after a short delay to refresh the form
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showNotice('❌ Failed to clear configuration: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('Network error while clearing configuration.', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Handle Clear All Configuration (alternative button)
    $('#clear-all-config').on('click', function() {
        $('#clear-api-key').click(); // Reuse the same functionality
    });

    // Handle AI Prompt Configuration Form Submission
    $('#ai-prompt-config-form').on('submit', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const originalText = $submitBtn.text();

        // Show loading state
        $submitBtn.prop('disabled', true).text('Saving...');

        const promptData = {
            action: 'save_ai_prompt',
            nonce: website_generator_ajax.nonce,
            ai_default_prompt: $('#ai_default_prompt').val()
        };

        // Debug log
        console.log('Saving AI prompt (length):', promptData.ai_default_prompt.length);

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: promptData,
            success: function(response) {
                if (response.success) {
                    showNotice('✅ AI prompt saved successfully!', 'success');
                } else {
                    // Handle different types of error responses
                    let errorMessage = 'Unknown error';
                    if (typeof response.data === 'string') {
                        errorMessage = response.data;
                    } else if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    } else if (response.data && typeof response.data === 'object') {
                        errorMessage = JSON.stringify(response.data);
                    }
                    showNotice('❌ Error saving prompt: ' + errorMessage, 'error');
                }
            },
            error: function() {
                showNotice('Network error. Please try again.', 'error');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Handle Reset Default Prompt
    $('#reset-default-prompt').on('click', function() {
        if (!confirm('Are you sure you want to reset the prompt to default? This will overwrite your current prompt.')) {
            return;
        }

        const defaultPrompt = `Create exactly 3 different versions of the content below. Use this EXACT format:

**VERSION 1 - CONTEXT-AWARE CONTENT:**
[Rewrite the content to be more professional and trustworthy while maintaining the same message. Focus on building customer confidence and emphasizing benefits like fast service and quality repairs.]

**VERSION 2 - SEO-OPTIMIZED CONTENT:**
[Rewrite the content to include relevant keywords for device repair searches. Include terms like "phone repair," "computer repair," "screen replacement," "battery repair," "same day service," etc.]

**VERSION 3 - GEO-OPTIMIZED CONTENT:**
[Rewrite the content to be comprehensive and authoritative for AI search engines. Include specific details and complete information that AI models can easily reference and quote.]

**Original Content:**
{ORIGINAL_CONTENT}

**Important:**
- Keep the same length as the original
- Maintain any business details exactly
- Use the EXACT format above with the VERSION headers`;

        $('#ai_default_prompt').val(defaultPrompt);
        showNotice('✅ Prompt reset to default. Remember to save your changes.', 'success');
    });

    // Handle Test AI Prompt
    $('#test-ai-prompt').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.text();

        // Check if API key is configured
        const apiKey = $('#claude_api_key').val();
        if (!apiKey) {
            showNotice('❌ Please configure your Claude API key first.', 'error');
            return;
        }

        // Show loading state
        $btn.prop('disabled', true).text('Testing...');

        const testData = {
            action: 'test_ai_prompt',
            nonce: website_generator_ajax.nonce,
            ai_default_prompt: $('#ai_default_prompt').val(),
            test_content: 'We fix broken phones and tablets. Our expert technicians can repair cracked screens, water damage, and battery issues. Contact us today for a free quote!'
        };

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: testData,
            success: function(response) {
                if (response.success) {
                    showNotice('✅ Prompt test successful! Check console for generated content.', 'success');
                    console.log('AI Generated Content:', response.data.generated_content);

                    // Show generated content in a modal or alert
                    alert('AI Generated Content:\n\n' + response.data.generated_content);
                } else {
                    let errorMessage = 'Unknown error';
                    if (typeof response.data === 'string') {
                        errorMessage = response.data;
                    } else if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    }
                    showNotice('❌ Prompt test failed: ' + errorMessage, 'error');
                }
            },
            error: function() {
                showNotice('Network error during prompt test.', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Handle AI API Configuration Form Submission
    $('#ai-api-config-form').on('submit', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const originalText = $submitBtn.text();

        // Validate required fields
        const apiKey = $('#claude_api_key').val().trim();
        if (!apiKey) {
            showNotice('❌ Please enter your Claude API key.', 'error');
            return;
        }

        // Show loading state
        $submitBtn.prop('disabled', true).text('Saving...');

        // Collect form data
        const formData = {
            action: 'save_ai_config',
            nonce: website_generator_ajax.nonce,
            config: {
                api_key: apiKey,
                model: $('#claude_model').val(),
                max_tokens: parseInt($('#claude_max_tokens').val()) || 1000,
                temperature: parseFloat($('#claude_temperature').val()) || 0.7
            }
        };

        // Debug log
        console.log('Sending AI config:', formData.config);

        // Submit via AJAX
        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showNotice('✅ AI configuration saved successfully!', 'success');

                    // Show what was updated
                    if (response.data && response.data.message) {
                        console.log('Configuration update details:', response.data.message);
                    }
                } else {
                    // Handle different types of error responses
                    let errorMessage = 'Unknown error';
                    if (typeof response.data === 'string') {
                        errorMessage = response.data;
                    } else if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    } else if (response.data && typeof response.data === 'object') {
                        errorMessage = JSON.stringify(response.data);
                    }
                    showNotice('❌ Error saving configuration: ' + errorMessage, 'error');
                }
            },
            error: function() {
                showNotice('Network error. Please try again.', 'error');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Handle Test API Connection
    $('#test-api-connection').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.text();

        // Check if API key is provided
        const apiKey = $('#claude_api_key').val();
        if (!apiKey) {
            showNotice('Please enter your Claude API key first.', 'warning');
            return;
        }

        // Show loading state
        $btn.prop('disabled', true).text('Testing...');

        // Debug log
        console.log('Testing connection with:', {
            api_key: apiKey.substring(0, 10) + '...',
            model: $('#claude_model').val()
        });

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'test_ai_connection',
                nonce: website_generator_ajax.nonce,
                claude_api_key: apiKey,
                claude_model: $('#claude_model').val()
            },
            success: function(response) {
                if (response.success) {
                    let successMessage = '✅ Connection successful! Claude API is working properly.';

                    // Add model info if available
                    if (response.data && response.data.model) {
                        successMessage += ` (Model: ${response.data.model})`;
                    }

                    // Add response preview if available
                    if (response.data && response.data.response) {
                        console.log('Claude response:', response.data.response);
                    }

                    showNotice(successMessage, 'success');
                } else {
                    // Handle different types of error responses (same as config saving)
                    let errorMessage = 'Unknown error';
                    if (typeof response.data === 'string') {
                        errorMessage = response.data;
                    } else if (response.data && response.data.error) {
                        errorMessage = response.data.error;
                    } else if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    } else if (response.data && typeof response.data === 'object') {
                        errorMessage = JSON.stringify(response.data);
                    }
                    showNotice('❌ Connection failed: ' + errorMessage, 'error');
                }
            },
            error: function() {
                showNotice('Network error during connection test.', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Universal show notice function
    function showNotice(message, type = 'info') {
        // Remove existing notices
        $('.backup-notice, .ai-config-notice').remove();

        // Create notice
        const noticeClass = type === 'error' ? 'notice-error' : type === 'warning' ? 'notice-warning' : 'notice-success';
        const notice = $(`
            <div class="notice ${noticeClass} backup-notice is-dismissible" style="margin: 15px 0; padding: 12px;">
                <p style="margin: 0;">${message}</p>
                <button type="button" class="notice-dismiss">
                    <span class="screen-reader-text">Dismiss this notice.</span>
                </button>
            </div>
        `);

        // Insert notice at the top of the backup section or after AI form
        if ($('.backup-section').length > 0) {
            $('.backup-section').first().prepend(notice);
        } else {
            $('#ai-api-config-form').after(notice);
        }

        // Make dismissible
        notice.find('.notice-dismiss').on('click', function() {
            notice.remove();
        });

        // Auto-remove success notices
        if (type === 'success') {
            setTimeout(function() {
                notice.fadeOut();
            }, 5000);
        }

        // Scroll to notice
        notice.get(0).scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Handle Clear Backup Form
    $('#clear-backup-form').on('click', function() {
        $('#backup_description').val('');
        showNotice('Form cleared successfully.', 'success');
    });

    // Handle Refresh Backups
    $('#refresh-backups').on('click', function() {
        $('#load-backups').click();
    });

    // Handle Manual Backup Creation
    $('#create-manual-backup').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.html();

        $btn.prop('disabled', true).html('<span class="dashicons dashicons-update-alt"></span> Creating Backup...');

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'create_backup',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('✅ Manual backup created successfully! ID: ' + response.data.backup_id, 'success');

                    // Refresh backup list if it's visible
                    if ($('#backup-list-container').is(':visible')) {
                        $('#load-backups').click();
                    }
                } else {
                    showNotice('❌ Failed to create backup: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('❌ Network error creating backup', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).html(originalText);
            }
        });
    });

    // Debug Tools

    $('#reset-to-defaults').on('click', function() {
        if (!confirm('Are you sure you want to reset ALL settings to defaults? This cannot be undone!')) {
            return;
        }

        const $btn = $(this);
        const originalText = $btn.text();

        $btn.prop('disabled', true).text('Resetting...');

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'revert_design_changes',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('✅ All settings reset to defaults successfully!', 'success');
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showNotice('❌ Failed to reset: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showNotice('❌ Network error resetting settings', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    $('#restore-default-content').on('click', function() {
        if (!confirm('Are you sure you want to restore all default content and text? This will reset all custom text to defaults.')) {
            return;
        }

        const $btn = $(this);
        const originalText = $btn.text();

        $btn.prop('disabled', true).text('Restoring...');

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'restore_default_content',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('✅ Default content restored successfully!', 'success');
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showNotice('❌ Failed to restore default content: ' + response.data.message, 'error');
                }
            },
            error: function() {
                showNotice('❌ Network error restoring default content', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Handle Restore Default Text buttons
    $(document).on('click', '.restore-default-text-btn', function() {
        const $btn = $(this);
        const fieldId = $btn.data('field');
        const defaultText = $btn.data('default');
        const $field = $('#' + fieldId);

        if (!$field.length) {
            console.error('Field not found:', fieldId);
            return;
        }

        // Confirm action
        if (!confirm('Restore this field to its default text? This will overwrite any current content.')) {
            return;
        }

        // Store current value for undo
        const currentValue = $field.val();

        // Restore default text
        $field.val(defaultText);

        // Trigger change event to update preview
        $field.trigger('input').trigger('change');

        // Show success feedback
        const originalBtnText = $btn.html();
        $btn.html('<span class="dashicons dashicons-yes"></span> Restored');
        $btn.css('background', '#00a32a').css('color', '#fff');

        // Reset button appearance after 2 seconds
        setTimeout(function() {
            $btn.html(originalBtnText);
            $btn.css('background', '').css('color', '');
        }, 2000);

        // Update block status to indicate change
        const $block = $field.closest('.visual-block');
        if ($block.length) {
            const $statusIndicator = $block.find('.status-indicator');
            $statusIndicator.attr('data-status', 'modified').text('Modified');
        }

        console.log('Restored default text for field:', fieldId, 'Default:', defaultText);
    });

    // Handle Create Backup
    $('#create-backup').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.text();

        // Show loading state
        $btn.prop('disabled', true).text('Creating Backup...');

        const backupData = {
            action: 'create_backup',
            nonce: website_generator_ajax.nonce,
            description: $('#backup_description').val() || 'Manual backup created on ' + new Date().toLocaleString()
        };

        // Debug log
        console.log('Creating backup with description:', backupData.description);

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: backupData,
            success: function(response) {
                console.log('Backup creation response:', response);
                if (response.success) {
                    let successMessage = '✅ Backup created successfully!';

                    // Add backup ID if available
                    if (response.data && response.data.backup_id) {
                        successMessage += ' ID: ' + response.data.backup_id;
                    }

                    showNotice(successMessage, 'success');

                    // Clear form
                    $('#backup_description').val('');
                    // Reload backup list if visible
                    if ($('#backup-list-container').is(':visible')) {
                        $('#load-backups').click();
                    }
                } else {
                    // Handle different types of error responses (same as AI config)
                    let errorMessage = 'Unknown error';
                    if (typeof response.data === 'string') {
                        errorMessage = response.data;
                    } else if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    } else if (response.data && typeof response.data === 'object') {
                        errorMessage = JSON.stringify(response.data);
                    }
                    showNotice('❌ Backup failed: ' + errorMessage, 'error');
                }
            },
            error: function() {
                showNotice('Network error during backup creation.', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Handle Load Backups
    $('#load-backups').on('click', function() {
        const $btn = $(this);
        const $container = $('#backup-list-container');
        const originalText = $btn.text();

        // Show loading state
        $btn.prop('disabled', true).text('Loading...');

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_all_backups',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                console.log('Get backups response:', response);
                if (response.success && response.data && response.data.backups) {
                    console.log('Found backups:', response.data.backups);
                    displayBackupList(response.data.backups);
                    $container.show();
                    $('#refresh-backups').show();
                } else {
                    console.log('No backups found or error');
                    showNotice('No backups found or error loading backups.', 'warning');
                }
            },
            error: function() {
                showNotice('Network error loading backups.', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Display backup list
    function displayBackupList(backups) {
        const $container = $('#backup-list-container');

        if (backups.length === 0) {
            $container.html('<p>No backups found.</p>');
            return;
        }

        let html = '<div class="backup-list">';

        backups.forEach(function(backup) {
            const date = new Date(backup.created_at).toLocaleString();
            const size = backup.size_bytes ? formatBytes(backup.size_bytes) : 'Unknown';

            html += `
                <div class="backup-item" data-backup-id="${backup.backup_id}">
                    <div class="backup-item-header">
                        <div class="backup-item-title">
                            ${backup.description || 'Plugin Backup'}
                            <span class="backup-type">(Plugin Data)</span>
                        </div>
                        <div class="backup-item-date">${date}</div>
                    </div>
                    <div class="backup-item-info">
                        <p><strong>ID:</strong> ${backup.backup_id || backupId}</p>
                        <p><strong>Version:</strong> ${backup.version || '2.0.0'}</p>
                        ${backup.description ? `<p><strong>Description:</strong> ${backup.description}</p>` : ''}
                    </div>
                    <div class="backup-item-actions">
                        <button type="button" class="button button-primary restore-backup" data-backup-id="${backup.backup_id}">
                            <span class="dashicons dashicons-undo"></span> Restore
                        </button>
                        <button type="button" class="button button-secondary delete-backup" data-backup-id="${backup.backup_id}">
                            <span class="dashicons dashicons-trash"></span> Delete
                        </button>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        $container.html(html);

        // Bind restore and delete events
        bindBackupActions();
    }

    // Bind backup action events
    function bindBackupActions() {
        // Restore backup
        $('.restore-backup').on('click', function() {
            const backupId = $(this).data('backup-id');

            if (!confirm('Are you sure you want to restore this backup? This will overwrite current settings.')) {
                return;
            }

            const $btn = $(this);
            const originalText = $btn.text();
            $btn.prop('disabled', true).text('Restoring...');

            // Debug log
            console.log('Restoring backup with ID:', backupId);

            $.ajax({
                url: website_generator_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'restore_backup',
                    nonce: website_generator_ajax.nonce,
                    backup_id: backupId
                },
                success: function(response) {
                    if (response.success) {
                        let successMessage = '✅ Backup restored successfully!';

                        // Add additional info if available
                        if (response.data && response.data.message) {
                            console.log('Restore details:', response.data.message);
                        }

                        showNotice(successMessage, 'success');

                        // Reload the page after a short delay
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        // Handle different types of error responses (same as AI config)
                        let errorMessage = 'Unknown error';
                        if (typeof response.data === 'string') {
                            errorMessage = response.data;
                        } else if (response.data && response.data.message) {
                            errorMessage = response.data.message;
                        } else if (response.data && typeof response.data === 'object') {
                            errorMessage = JSON.stringify(response.data);
                        }
                        showNotice('❌ Restore failed: ' + errorMessage, 'error');
                    }
                },
                error: function() {
                    showNotice('Network error during restore.', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).text(originalText);
                }
            });
        });

        // Delete backup
        $('.delete-backup').on('click', function() {
            const backupId = $(this).data('backup-id');

            if (!confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
                return;
            }

            const $btn = $(this);
            const $item = $btn.closest('.backup-item');
            const originalText = $btn.text();
            $btn.prop('disabled', true).text('Deleting...');

            $.ajax({
                url: website_generator_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'delete_backup',
                    nonce: website_generator_ajax.nonce,
                    backup_id: backupId
                },
                success: function(response) {
                    if (response.success) {
                        $item.fadeOut(function() {
                            $item.remove();
                        });

                        let successMessage = '✅ Backup deleted successfully!';

                        // Add additional info if available
                        if (response.data && response.data.message) {
                            console.log('Delete details:', response.data.message);
                        }

                        showNotice(successMessage, 'success');
                    } else {
                        // Handle different types of error responses (same as AI config)
                        let errorMessage = 'Unknown error';
                        if (typeof response.data === 'string') {
                            errorMessage = response.data;
                        } else if (response.data && response.data.message) {
                            errorMessage = response.data.message;
                        } else if (response.data && typeof response.data === 'object') {
                            errorMessage = JSON.stringify(response.data);
                        }
                        showNotice('❌ Delete failed: ' + errorMessage, 'error');
                    }
                },
                error: function() {
                    showNotice('Network error during deletion.', 'error');
                },
                complete: function() {
                    $btn.prop('disabled', false).text(originalText);
                }
            });
        });
    }

    // Format bytes helper function
    function formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }
    // Handle Gemini API Configuration Form Submission
    $('#gemini-api-config-form').on('submit', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $submitBtn = $form.find('button[type="submit"]');
        const originalText = $submitBtn.text();

        // Validate required fields
        const apiKey = $('#gemini_api_key').val().trim();
        if (!apiKey) {
            showNotice('❌ Please enter your Gemini API key.', 'error');
            return;
        }

        // Show loading state
        $submitBtn.prop('disabled', true).text('Saving...');

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'save_gemini_api_key',
                nonce: website_generator_ajax.nonce,
                gemini_api_key: apiKey
            },
            success: function(response) {
                if (response.success) {
                    showNotice('✅ Gemini API key saved successfully!', 'success');
                    
                    // Show API key status
                    if (!$('.api-key-status').length) {
                        $('.api-key-container').append(`
                            <div class="api-key-status">
                                <span class="api-key-indicator">
                                    <span class="dashicons dashicons-yes-alt"></span>
                                    API Key Configured
                                </span>
                            </div>
                        `);
                    }
                } else {
                    let errorMessage = 'Unknown error';
                    if (typeof response.data === 'string') {
                        errorMessage = response.data;
                    } else if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    }
                    showNotice('❌ Error saving API key: ' + errorMessage, 'error');
                }
            },
            error: function() {
                showNotice('Network error. Please try again.', 'error');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Handle Test Gemini Connection
    $('#test-gemini-connection').on('click', function() {
        const $btn = $(this);
        const originalText = $btn.text();

        // Check if API key is provided
        const apiKey = $('#gemini_api_key').val();
        if (!apiKey) {
            showNotice('Please enter your Gemini API key first.', 'warning');
            return;
        }

        // Show loading state
        $btn.prop('disabled', true).text('Testing...');

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'test_gemini_connection',
                nonce: website_generator_ajax.nonce,
                gemini_api_key: apiKey
            },
            success: function(response) {
                if (response.success) {
                    let successMessage = '✅ Connection successful! Gemini API is working properly.';
                    if (response.data && response.data.model) {
                        successMessage += ` (Model: ${response.data.model})`;
                    }
                    showNotice(successMessage, 'success');
                } else {
                    let errorMessage = 'Unknown error';
                    if (typeof response.data === 'string') {
                        errorMessage = response.data;
                    } else if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    }
                    showNotice('❌ Connection failed: ' + errorMessage, 'error');
                }
            },
            error: function() {
                showNotice('Network error during connection test.', 'error');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // ===========================================
    // 🖼️ PROMPT STUDIO & IMAGE GENERATION LOGIC
    // ===========================================
    console.log('🎨 [PROMPT-STUDIO] Initializing Prompt Studio...');

    // 1. Define the Master Prompt Template
    const masterPromptTemplate = `Create a hyper-realistic, professional studio product photograph of a specific smartphone model for an e-commerce website. The image must be of exceptional quality, suitable for a hero section banner.

**Subject & Appearance:**
The subject is **{{composition}}** in the color **{{device_color}}**. Its appearance must be an exact, faithful reproduction of the real-world device in every detail, including its precise dimensions, button placement, camera module design, and materials (e.g., matte glass back, polished aluminum frame). The phone's screen should be **{{screen_state}}**. Crucially, the image **{{logo_visibility}}**; all brand names, logos, and identifying marks must be completely absent from the device's body and software interface, creating a "de-branded" or "generic" yet instantly recognizable version of the phone.

**Composition & Lighting:**
The phone is positioned at a **{{angle}}**. The composition must be clean, elegant, and focused entirely on the product. Use a professional studio lighting setup, specifically **{{lighting_style}}**, to create soft, flattering highlights and deep, natural-looking **{{shadow_intensity}}** shadows that ground the object in the scene and enhance its form and texture.

**Background & Environment:**
The background must be a perfectly clean, minimalist, and pure white **(#FFFFFF) {{background_style}}** background. It is critical that the background is uniformly white without any gradients or textures, while the product's shadows remain realistic and soft.

**Photographic Style:**
The image is captured using a **{{lens_type}}**, renowned for its use in high-end product photography to produce sharp, distortion-free images with beautiful depth of field. The overall aesthetic is one of sophisticated, modern, and premium product presentation. The color palette must be accurate and vibrant. The final image should be ultra-high resolution, photorealistic, and indistinguishable from a real commercial photograph.`;

    // 2. Create a Prompt Update Function
    function updateLivePrompt() {
        console.log('🔄 [PROMPT-STUDIO] Updating live prompt...');
        let livePrompt = masterPromptTemplate;
        const replacements = {
            composition: $('#composition').val().replace('{{device_model}}', $('#device_model').val() || 'device'),
            device_model: $('#device_model').val() || 'device',
            device_color: $('#device_color').val() || 'default',
            screen_state: $('#screen_state').val().replace(/_/g, ' '),
            logo_visibility: $('#logo_visibility').val().replace(/_/g, ' '),
            angle: $('#angle').val().replace(/_/g, ' '),
            lighting_style: $('#lighting_style').val().replace(/_/g, ' '),
            shadow_intensity: $('#shadow_intensity').val().replace(/_/g, ' '),
            background_style: $('#background_style').val().replace(/_/g, ' '),
            lens_type: $('#lens_type').val().replace(/_/g, ' ')
        };

        for (const placeholder in replacements) {
            livePrompt = livePrompt.replace(new RegExp(`{{${placeholder}}}`, 'g'), replacements[placeholder]);
        }

        $('#live-prompt-preview').val(livePrompt);
        console.log('📝 [PROMPT-STUDIO] New prompt:', livePrompt);
    }

    // 3. Bind Event Handlers & Dynamic Color Logic
    const deviceColorMap = {
        "iPhone 15 Pro": ["Natural Titanium", "Blue Titanium", "White Titanium", "Black Titanium"],
        "iPhone 15": ["Blue", "Pink", "Yellow", "Green", "Black"],
        "Samsung Galaxy S24": ["Onyx Black", "Marble Gray", "Cobalt Violet", "Amber Yellow"],
        "Google Pixel 8": ["Obsidian", "Hazel", "Rose"]
    };

    $('#device_model').on('input', function() {
        const model = $(this).val();
        const $colorDropdown = $('#device_color');
        const currentColors = deviceColorMap[model];

        $colorDropdown.empty().append('<option value="">Default</option>');

        if (currentColors) {
            currentColors.forEach(function(color) {
                $colorDropdown.append(`<option value="${color}">${color}</option>`);
            });
        }
        updateLivePrompt();
    });

    const promptControls = '#prompt-studio-form input, #prompt-studio-form select';
    $(promptControls).on('change', function() {
        updateLivePrompt();
    });

    // Initial call to populate the prompt on page load
    updateLivePrompt();
    console.log('✅ [PROMPT-STUDIO] Initial prompt generated.');

    // 4. Implement "Copy Prompt" Functionality
    $('#copy-prompt-btn').on('click', function() {
        const promptText = $('#live-prompt-preview').val();
        navigator.clipboard.writeText(promptText).then(function() {
            showNotice('✅ Prompt copied to clipboard!', 'success');
            console.log('📋 [PROMPT-STUDIO] Prompt copied.');
        }, function(err) {
            showNotice('❌ Could not copy prompt: ' + err, 'error');
            console.error('📋 [PROMPT-STUDIO] Failed to copy prompt:', err);
        });
    });

    // 5. Update the "Generate Image" Logic
    $('#generate-image-btn').on('click', function(e) {
        e.preventDefault();

        const $submitBtn = $(this);
        const originalText = $submitBtn.html();

        console.log('🚀 [AI-IMAGES] Starting image generation process from Prompt Studio...');

        // Validate API key
        const apiKey = $('#gemini_api_key').val();
        if (!apiKey) {
            console.log('❌ [AI-IMAGES] No API key configured');
            showNotice('❌ Please configure your Gemini API key first.', 'error');
            return;
        }
        console.log('✅ [AI-IMAGES] API key is configured.');

        // Show loading state
        $submitBtn.prop('disabled', true).html('<span class="dashicons dashicons-update spinning"></span> Generating...');

        // Get the generated prompt from the live preview
        const generatedPrompt = $('#live-prompt-preview').val();
        if (!generatedPrompt) {
            showNotice('❌ Cannot generate image with an empty prompt.', 'error');
            $submitBtn.prop('disabled', false).html(originalText);
            return;
        }

        const formData = {
            action: 'generate_hero_image',
            nonce: website_generator_ajax.nonce,
            prompt: generatedPrompt,
            device_type: $('#device_model').val() || 'device', // Using device_model as a proxy for type
            device_model: $('#device_model').val() || 'device',
            lighting_style: $('#lighting_style').val()
        };

        console.log('📤 [AI-IMAGES] Sending generation request with prompt:', generatedPrompt);
        console.log('📤 [AI-IMAGES] AJAX URL:', website_generator_ajax.ajax_url);
        console.log('📤 [AI-IMAGES] Nonce:', website_generator_ajax.nonce);

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: formData,
            timeout: 60000, // 60 second timeout for image generation
            beforeSend: function(xhr) {
                console.log('🌐 [AI-IMAGES] Sending AJAX request...');
            },
            success: function(response) {
                console.log('📥 [AI-IMAGES] Received response:', response);
                
                if (response.success && response.data) {
                    const newImage = response.data;
                    console.log('✅ [AI-IMAGES] Generation successful!');
                    console.log('📊 [AI-IMAGES] Generated image data:', newImage);
                    showNotice('✅ Image generated successfully!', 'success');
                    
                    // Show generated images section if hidden
                    $('#generated-images-section').show();
                    $('#hero-replacement-section').show();
                    
                    // Remove the "No images" placeholder if it exists
                    $('#generated-images-gallery').find('p').remove();

                    // Prepend the new image to the gallery
                    const newImageHtml = `
                        <div class="image-item" data-attachment-id="${newImage.attachment_id}">
                            <div class="image-preview">
                                <img src="${newImage.thumbnail_url || newImage.url}" alt="Generated ${newImage.device_type} image" loading="lazy">
                            </div>
                            <div class="image-info">
                                <h4>${(newImage.device_type || 'N/A').replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                                <p><strong>Style:</strong> ${(newImage.style || 'N/A').replace(/_/g, ' ')}</p>
                                <p><strong>Created:</strong> ${new Date(newImage.generated_at * 1000).toLocaleString()}</p>
                                <p><strong>Size:</strong> ${formatBytes(newImage.file_size)}</p>
                            </div>
                            <div class="image-actions">
                                <button type="button" class="button button-primary select-image" data-image='${JSON.stringify(newImage)}'>
                                    <span class="dashicons dashicons-yes"></span> Select for Hero
                                </button>
                                <button type="button" class="button button-secondary preview-image" data-url="${newImage.url}">
                                    <span class="dashicons dashicons-visibility"></span> Preview
                                </button>
                                <button type="button" class="button button-secondary delete-image" data-attachment-id="${newImage.attachment_id}">
                                    <span class="dashicons dashicons-trash"></span> Delete
                                </button>
                            </div>
                        </div>
                    `;
                    $('#generated-images-gallery .image-grid').prepend(newImageHtml);
                    bindImageActions(); // Re-bind events for the new item
                } else {
                    console.log('❌ [AI-IMAGES] Generation failed:', response);
                    let errorMessage = 'Image generation failed';
                    if (typeof response.data === 'string') {
                        errorMessage = response.data;
                    } else if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    }
                    console.log('❌ [AI-IMAGES] Error message:', errorMessage);
                    showNotice('❌ ' + errorMessage, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.log('❌ [AI-IMAGES] AJAX error - Status:', status, 'Error:', error);
                console.log('❌ [AI-IMAGES] Response Text:', xhr.responseText);
                console.log('❌ [AI-IMAGES] Status Code:', xhr.status);
                
                if (status === 'timeout') {
                    console.log('⏱️ [AI-IMAGES] Request timed out after 60 seconds');
                    showNotice('❌ Request timed out. Image generation may take longer than expected.', 'error');
                } else {
                    console.log('🌐 [AI-IMAGES] Network error occurred');
                    showNotice('❌ Network error during image generation.', 'error');
                }
            },
            complete: function() {
                console.log('🏁 [AI-IMAGES] AJAX request completed');
                $submitBtn.prop('disabled', false).html('<span class="dashicons dashicons-format-image"></span> Generate Image');
            }
        });
    });

    function loadGeneratedImages() {
        const $gallery = $('#generated-images-gallery');
        const $refreshBtn = $('#refresh-generated-images');
        const originalText = $refreshBtn.html();

        console.log('🔄 [AI-IMAGES] Loading generated images...');
        $refreshBtn.prop('disabled', true).html('<span class="dashicons dashicons-update spinning"></span> Refreshing...');
        $gallery.html('<p>Loading images...</p>');

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_generated_images',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                console.log('📥 [AI-IMAGES] Images response received:', response);
                
                if (response.success) {
                    console.log('✅ [AI-IMAGES] Found ' + (response.data ? response.data.length : 0) + ' images');
                    displayGeneratedImages(response.data);
                } else {
                    console.log('❌ [AI-IMAGES] Failed to load images:', response);
                    $gallery.html('<p>Failed to load images.</p>');
                    showNotice('❌ Failed to load images.', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.log('❌ [AI-IMAGES] Error loading images - Status:', status, 'Error:', error);
                $gallery.html('<p>Network error loading images.</p>');
                showNotice('Network error loading images.', 'error');
            },
            complete: function() {
                console.log('🏁 [AI-IMAGES] Load images request completed');
                $refreshBtn.prop('disabled', false).html(originalText);
            }
        });
    }

    // Handle Refresh Generated Images
    $('#refresh-generated-images').on('click', function() {
        loadGeneratedImages();
    });

 // Automatically load images when the tab is shown
 $('a[data-tab="ai-images"]').on('click', function() {
  setTimeout(function() {
   if ($('#ai-images-tab').is(':visible')) {
    loadGeneratedImages();
   }
  }, 100);
 });

 // Also load if it's the active tab on page load (e.g., after refresh)
 if ($('#ai-images-tab').hasClass('active')) {
  loadGeneratedImages();
 }

    // Display generated images in gallery
    function displayGeneratedImages(images) {
        const $gallery = $('#generated-images-gallery');
        
        console.log('🖼️ [AI-IMAGES] Displaying images in gallery...');
        console.log('🖼️ [AI-IMAGES] Images to display:', images);
        
        if (!images || images.length === 0) {
            console.log('📭 [AI-IMAGES] No images to display');
            $gallery.html('<p>No generated images found. Create your first AI image above!</p>');
            return;
        }

        console.log('🎨 [AI-IMAGES] Building gallery HTML for ' + images.length + ' images');
        let html = '<div class="image-grid">';
        
        images.forEach(function(image, index) {
            console.log('🖼️ [AI-IMAGES] Processing image ' + (index + 1) + ':', image);
            
            // Validate image data
            if (!image.url || !image.filename) {
                console.log('⚠️ [AI-IMAGES] Invalid image data at index ' + index + ':', image);
                return;
            }
            
            html += `
                <div class="image-item" data-attachment-id="${image.attachment_id}">
                    <div class="image-preview">
                        <img src="${image.thumbnail_url || image.url}" alt="Generated ${image.device_type} image" loading="lazy"
                             onerror="console.log('❌ [AI-IMAGES] Failed to load image: ${image.thumbnail_url || image.url}');"
                             onload="console.log('✅ [AI-IMAGES] Successfully loaded image: ${image.thumbnail_url || image.url}');">
                    </div>
                    <div class="image-info">
                        <h4>${image.device_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                        <p><strong>Style:</strong> ${image.style.replace(/_/g, ' ')}</p>
                        <p><strong>Created:</strong> ${new Date(image.created_date).toLocaleString()}</p>
                        <p><strong>Size:</strong> ${formatBytes(image.file_size)}</p>
                    </div>
                    <div class="image-actions">
                        <button type="button" class="button button-primary select-image" data-image='${JSON.stringify(image)}'>
                            <span class="dashicons dashicons-yes"></span> Select for Hero
                        </button>
                        <button type="button" class="button button-secondary preview-image" data-url="${image.url}">
                            <span class="dashicons dashicons-visibility"></span> Preview
                        </button>
                        <button type="button" class="button button-secondary delete-image" data-attachment-id="${image.attachment_id}">
                            <span class="dashicons dashicons-trash"></span> Delete
                        </button>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        console.log('🎨 [AI-IMAGES] Setting gallery HTML...');
        $gallery.html(html);
        
        console.log('🔗 [AI-IMAGES] Binding image action events...');
        // Bind image action events
        bindImageActions();
        
        console.log('✅ [AI-IMAGES] Gallery display completed');
    }

    // Bind image gallery action events
    function bindImageActions() {
        // Select image for hero
        $('.select-image').on('click', function() {
            const imageData = JSON.parse($(this).attr('data-image'));
            selectImageForHero(imageData);
        });

        // Preview image
        $('.preview-image').on('click', function() {
            const imageUrl = $(this).data('url');
            showImagePreview(imageUrl);
        });

        // Delete image
        $('.delete-image').on('click', function() {
            const attachmentId = $(this).data('attachment-id');
            deleteGeneratedImage(attachmentId);
        });
    }

    // Select image for hero replacement
    function selectImageForHero(imageData) {
        // Store selected image data
        window.selectedHeroImage = imageData;
        
        // Update UI
        $('.image-item').removeClass('selected');
        $(`.image-item[data-filename="${imageData.filename}"]`).addClass('selected');
        
        // Enable hero replacement buttons
        $('#preview-hero-replacement, #apply-hero-replacement').prop('disabled', false);
        
        showNotice(`✅ Selected "${imageData.device_type}" image for hero replacement.`, 'success');
    }

    // Show image preview modal
    function showImagePreview(imageUrl) {
        const modal = $(`
            <div class="image-preview-modal">
 <div class="preview-content">
  <button class="close-preview">&times;</button>
  <img src="${imageUrl}" alt="Image Preview">
 </div>
            </div>
        `);

        $('body').append(modal);

        // Close on click outside or close button
        modal.on('click', function(e) {
            if (e.target === this || $(e.target).hasClass('close-preview')) {
                modal.remove();
            }
        });
    }

    // Delete generated image
    function deleteGeneratedImage(attachmentId) {
        console.log('🗑️ [AI-IMAGES] Attempting to delete image with attachment ID:', attachmentId);
        
        if (!confirm('Are you sure you want to delete this generated image?')) {
            console.log('❌ [AI-IMAGES] Delete cancelled by user');
            return;
        }

        console.log('📤 [AI-IMAGES] Sending delete request...');
        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'delete_generated_image',
                nonce: website_generator_ajax.nonce,
                attachment_id: attachmentId
            },
            beforeSend: function() {
                console.log('🌐 [AI-IMAGES] Deleting image via AJAX...');
            },
            success: function(response) {
                console.log('📥 [AI-IMAGES] Delete response:', response);
                
                if (response.success) {
                    console.log('✅ [AI-IMAGES] Image deleted successfully');
                    showNotice('✅ Image deleted successfully!', 'success');
                    $(`.image-item[data-attachment-id="${attachmentId}"]`).fadeOut(function() {
                        $(this).remove();
                        console.log('🗑️ [AI-IMAGES] Image element removed from DOM');
                    });
                } else {
                    console.log('❌ [AI-IMAGES] Failed to delete image:', response);
                    showNotice('❌ Failed to delete image.', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.log('❌ [AI-IMAGES] Delete error - Status:', status, 'Error:', error);
                console.log('❌ [AI-IMAGES] Response Text:', xhr.responseText);
                showNotice('Network error during image deletion.', 'error');
            },
            complete: function() {
                console.log('🏁 [AI-IMAGES] Delete request completed');
            }
        });
    }

    // Utility function to format bytes
    function formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }

    // Note: showNotice function is defined above in the backup section

    // Load current hero image functionality
    function loadCurrentHeroImage() {
        console.log('🖼️ [AI-IMAGES] Loading current hero image...');
        
        // Check if we're on the AI Images tab
        const currentTab = $('.nav-tab-active').data('tab');
        if (currentTab !== 'ai-images') {
            console.log('⏭️ [AI-IMAGES] Not on AI Images tab, skipping hero image load');
            return;
        }
        
        const $container = $('#current-hero-display');
        if (!$container.length) {
            console.log('❌ [AI-IMAGES] Current hero display container not found');
            return;
        }
        
        console.log('📤 [AI-IMAGES] Requesting current hero image data...');
        $container.html('<p>Loading current hero image...</p>');
        
        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_current_hero_image',
                nonce: website_generator_ajax.nonce
            },
            beforeSend: function() {
                console.log('🌐 [AI-IMAGES] Sending hero image request...');
            },
            success: function(response) {
                console.log('📥 [AI-IMAGES] Hero image response:', response);
                
                if (response.success && response.data) {
                    console.log('✅ [AI-IMAGES] Current hero image loaded successfully');
                    
                    const heroData = response.data;
                    let heroHtml = '<div class="current-hero-image">';
                    
                    if (heroData.image_url) {
                        heroHtml += `
                            <img src="${heroData.image_url}" alt="Current Hero Image"
                                 style="max-width: 100%; height: auto; border-radius: 4px; border: 1px solid #ddd;"
                                 onload="console.log('✅ [AI-IMAGES] Hero image loaded: ${heroData.image_url}');"
                                 onerror="console.log('❌ [AI-IMAGES] Failed to load hero image: ${heroData.image_url}');">
                        `;
                        
                        if (heroData.image_title) {
                            heroHtml += `<p><strong>Title:</strong> ${heroData.image_title}</p>`;
                        }
                        if (heroData.image_size) {
                            heroHtml += `<p><strong>Size:</strong> ${heroData.image_size}</p>`;
                        }
                        if (heroData.image_dimensions) {
                            heroHtml += `<p><strong>Dimensions:</strong> ${heroData.image_dimensions}</p>`;
                        }
                    } else {
                        console.log('⚠️ [AI-IMAGES] No hero image currently set');
                        heroHtml += '<p>No hero image currently set</p>';
                    }
                    
                    heroHtml += '</div>';
                    $container.html(heroHtml);
                    
                } else {
                    console.log('❌ [AI-IMAGES] Failed to load hero image:', response);
                    $container.html('<p>Unable to load current hero image</p>');
                }
            },
            error: function(xhr, status, error) {
                console.log('❌ [AI-IMAGES] Hero image request error - Status:', status, 'Error:', error);
                console.log('❌ [AI-IMAGES] Response Text:', xhr.responseText);
                $container.html('<p>Error loading current hero image</p>');
            },
            complete: function() {
                console.log('🏁 [AI-IMAGES] Hero image request completed');
            }
        });
    }

    // Add CSS styles for AI Images functionality
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .spinning { animation: spin 1s linear infinite; }
            @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
            
            .ai-images-container {
                max-width: 1200px;
                margin: 0 auto;
            }
            
            .ai-images-header {
                background: #fff;
                padding: 20px;
                border: 1px solid #ccd0d4;
                margin-bottom: 20px;
                border-radius: 4px;
            }
            
            .ai-images-header h2 {
                margin: 0 0 10px 0;
                color: #23282d;
            }
            
            .ai-images-header p {
                margin: 0;
                color: #666;
                font-size: 14px;
            }
            
            .generation-actions {
                padding-top: 20px;
            }
            
            .gallery-controls {
                display: flex;
                gap: 10px;
                margin-bottom: 20px;
            }
            
            .image-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 20px;
            }
            
            .image-item {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                background: #fff;
                transition: all 0.3s ease;
            }
            
            .image-item:hover {
                border-color: #0073aa;
                box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
            }
            
            .image-item.selected {
                border-color: #0073aa;
                background: #f0f8ff;
                box-shadow: 0 2px 8px rgba(0, 115, 170, 0.2);
            }
            
            .image-preview img {
                width: 100%;
                height: 200px;
                object-fit: cover;
                border-radius: 4px;
                border: 1px solid #ddd;
            }
            
            .image-info {
                margin: 15px 0;
            }
            
            .image-info h4 {
                margin: 0 0 10px 0;
                color: #0073aa;
                font-size: 16px;
            }
            
            .image-info p {
                margin: 5px 0;
                font-size: 13px;
                color: #666;
            }
            
            .image-actions {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            }
            
            .image-actions .button {
                font-size: 12px;
                padding: 6px 12px;
                height: auto;
                line-height: 1.2;
            }
            
            .hero-replacement-container {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-top: 15px;
            }
            
            .current-hero-preview {
                background: #f9f9f9;
                padding: 20px;
                border-radius: 4px;
                border: 1px solid #ddd;
            }
            
            .current-hero-preview h4 {
                margin: 0 0 15px 0;
                color: #23282d;
            }
            
            .replacement-actions {
                display: flex;
                flex-direction: column;
                gap: 10px;
                justify-content: center;
            }

.image-preview-modal {
 position: fixed;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
 background: rgba(0, 0, 0, 0.8);
 z-index: 999999;
 display: flex;
 align-items: center;
 justify-content: center;
 padding: 20px;
 box-sizing: border-box;
}
.preview-content {
 position: relative;
 max-width: 90%;
 max-height: 90%;
 background: white;
 border-radius: 8px;
 overflow: hidden;
}
.close-preview {
 position: absolute;
 top: 10px;
 right: 10px;
 z-index: 10;
 background: rgba(0, 0, 0, 0.7);
 color: white;
 border: none;
 width: 30px;
 height: 30px;
 border-radius: 50%;
 cursor: pointer;
 line-height: 30px;
 text-align: center;
 font-size: 20px;
}
.preview-content img {
 display: block;
 max-width: 100%;
 height: auto;
}
            
            @media (max-width: 768px) {
                .hero-replacement-container {
                    grid-template-columns: 1fr;
                }
                .image-grid {
                    grid-template-columns: 1fr;
                }
                .image-actions {
                    flex-direction: column;
                }
            }

            /* Backup Management Styles */
            .backup-section {
                margin-bottom: 30px;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background: #fff;
            }

            .manual-backup-controls {
                margin-bottom: 15px;
            }

            .backup-list {
                margin-top: 15px;
            }

            .backup-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px;
                border: 1px solid #ddd;
                border-radius: 5px;
                margin-bottom: 10px;
                background: #f9f9f9;
            }

            .backup-info {
                flex: 1;
                font-size: 13px;
                line-height: 1.4;
            }

            .backup-actions {
                margin-left: 15px;
            }

            .backup-controls {
                margin-bottom: 15px;
            }

            .backup-controls button {
                margin-right: 10px;
            }

            /* Restore Default Text Button Styles */
            .input-with-actions, .textarea-with-actions {
                display: flex;
                align-items: flex-start;
                gap: 10px;
            }

            .input-with-actions .block-input, .textarea-with-actions .block-input {
                flex: 1;
            }

            .restore-default-text-btn {
                background: #f0f0f1;
                border: 1px solid #c3c4c7;
                color: #2c3338;
                padding: 6px 12px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 12px;
                white-space: nowrap;
                display: flex;
                align-items: center;
                gap: 4px;
                transition: all 0.2s ease;
                min-height: 32px;
            }

            .restore-default-text-btn:hover {
                background: #e0e0e0;
                border-color: #8c8f94;
            }

            .restore-default-text-btn:active {
                background: #d0d0d0;
            }

            .restore-default-text-btn .dashicons {
                font-size: 14px;
                width: 14px;
                height: 14px;
            }

            .textarea-with-actions {
                align-items: flex-start;
            }

            .textarea-with-actions .restore-default-text-btn {
                margin-top: 2px;
            }
        `)
        .appendTo('head');

    // Single-Page Business Info Form JavaScript
    $(document).ready(function() {
        // Initialize business info form
        initBusinessInfoForm();

        // Load existing business info
        loadBusinessInfo();

        // Update completion status on form changes
        $('#business-info-form').on('input change', function() {
            updateCompletionStatus();
        });

        // Save business info
        $('#save-business-info').on('click', function() {
            saveBusinessInfo();
        });

        // Preview AI content
        $('#preview-ai-content').on('click', function() {
            previewAIContent();
        });

        // Clear business info
        $('#clear-business-info').on('click', function() {
            clearBusinessInfo();
        });
    });

    function initBusinessInfoForm() {
        // Add validation to required fields
        $('#business-info-form input[required], #business-info-form select[required]').on('blur', function() {
            validateField($(this));
        });

        // Format phone number input
        $('#phone_number').on('input', function() {
            formatPhoneNumber($(this));
        });

        // Limit state input to 2 characters
        $('#state').on('input', function() {
            $(this).val($(this).val().toUpperCase().slice(0, 2));
        });

        // Validate device selection
        $('input[name="devices_repaired[]"]').on('change', function() {
            validateDeviceSelection();
        });
    }

    function loadBusinessInfo() {
        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_business_info',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    populateForm(response.data);
                    updateCompletionStatus();
                }
            },
            error: function() {
                console.log('Error loading business info');
            }
        });
    }

    function populateForm(data) {
        // Populate text inputs
        Object.keys(data).forEach(function(key) {
            const field = $('#' + key);
            if (field.length && field.is('input, textarea, select')) {
                if (key === 'devices_repaired' && Array.isArray(data[key])) {
                    // Handle checkbox array
                    data[key].forEach(function(value) {
                        $('input[name="devices_repaired[]"][value="' + value + '"]').prop('checked', true);
                    });
                } else {
                    field.val(data[key]);
                }
            }
        });
    }

    function updateCompletionStatus() {
        const totalFields = $('#business-info-form input, #business-info-form select, #business-info-form textarea').length;
        const requiredFields = $('#business-info-form input[required], #business-info-form select[required]').length;
        let filledFields = 0;
        let filledRequired = 0;

        $('#business-info-form input, #business-info-form select, #business-info-form textarea').each(function() {
            const $field = $(this);
            if ($field.attr('type') === 'checkbox') {
                if ($field.is(':checked')) filledFields++;
            } else if ($field.val() && $field.val().trim() !== '') {
                filledFields++;
                if ($field.is('[required]')) filledRequired++;
            }
        });

        // Check device selection separately
        if ($('input[name="devices_repaired[]"]:checked').length > 0) {
            filledRequired++;
        }

        const completionPercentage = Math.round((filledFields / totalFields) * 100);
        const requiredComplete = filledRequired >= requiredFields;

        $('#completion-text').text(completionPercentage + '% Complete' + (requiredComplete ? ' ✓' : ''));
        $('#completion-fill').css('width', completionPercentage + '%');

        // Change color based on completion
        if (requiredComplete) {
            $('#completion-fill').css('background', '#4CAF50');
        } else {
            $('#completion-fill').css('background', '#ff9800');
        }
    }

    function validateField($field) {
        const value = $field.val().trim();
        const isRequired = $field.is('[required]');

        $field.removeClass('error');
        $field.next('.error-message').remove();

        if (isRequired && !value) {
            $field.addClass('error');
            $field.after('<div class="error-message" style="color: #d63638; font-size: 12px; margin-top: 4px;">This field is required</div>');
            return false;
        }

        return true;
    }

    function formatPhoneNumber($field) {
        let value = $field.val().replace(/\D/g, '');
        if (value.length >= 6) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        } else if (value.length >= 3) {
            value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
        }
        $field.val(value);
    }

    function validateDeviceSelection() {
        const checkedDevices = $('input[name="devices_repaired[]"]:checked').length;
        const container = $('#devices_repaired');

        container.find('.error-message').remove();

        if (checkedDevices === 0) {
            container.after('<div class="error-message" style="color: #d63638; font-size: 12px; margin-top: 4px;">Please select at least one device type</div>');
            return false;
        }

        return true;
    }

    function saveBusinessInfo() {
        // Validate required fields
        let isValid = true;
        $('#business-info-form input[required], #business-info-form select[required]').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });

        if (!validateDeviceSelection()) {
            isValid = false;
        }

        if (!isValid) {
            showNotice('Please fill in all required fields', 'error');
            return;
        }

        // Collect form data
        const formData = new FormData($('#business-info-form')[0]);
        formData.append('action', 'save_business_info');
        formData.append('nonce', website_generator_ajax.nonce);

        // Show loading state
        const $saveBtn = $('#save-business-info');
        const originalText = $saveBtn.html();
        $saveBtn.html('<span class="dashicons dashicons-update-alt"></span> Saving...').prop('disabled', true);

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotice('✅ Business information saved successfully!', 'success');
                    updateCompletionStatus();
                } else {
                    showNotice('❌ Error saving business information: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('❌ Error saving business information. Please try again.', 'error');
            },
            complete: function() {
                $saveBtn.html(originalText).prop('disabled', false);
            }
        });
    }

    function previewAIContent() {
        showNotice('🔄 Generating AI content preview...', 'info');

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'preview_ai_content',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showAIPreviewModal(response.data.preview_samples);
                } else {
                    showNotice('❌ Error generating AI content: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('❌ Error generating AI content. Please try again.', 'error');
            }
        });
    }

    function showAIPreviewModal(previewSamples) {
        let modalHtml = `
            <div id="ai-preview-modal" class="ai-preview-modal">
                <div class="ai-preview-content">
                    <div class="ai-preview-header">
                        <h3>🔍 AI Content Preview</h3>
                        <button type="button" class="ai-preview-close">&times;</button>
                    </div>
                    <div class="ai-preview-body">
                        <p><strong>Preview of AI-generated content using your business information:</strong></p>
        `;

        Object.keys(previewSamples).forEach(function(key) {
            const sample = previewSamples[key];
            modalHtml += `
                <div class="preview-comparison">
                    <h4>${sample.label}</h4>
                    <div class="comparison-grid">
                        <div class="before-content">
                            <label>Current:</label>
                            <div class="content-box current">${sample.current}</div>
                        </div>
                        <div class="after-content">
                            <label>AI Generated:</label>
                            <div class="content-box preview">${sample.preview}</div>
                        </div>
                    </div>
                </div>
            `;
        });

        modalHtml += `
                        <div class="preview-note">
                            <p><em>This is a preview only. To apply these changes, use the AI text regeneration feature in the Homepage tab.</em></p>
                        </div>
                    </div>
                    <div class="ai-preview-footer">
                        <button type="button" class="button button-secondary ai-preview-close">Close Preview</button>
                        <button type="button" class="button button-primary" onclick="switchToHomepageTab()">Go to Homepage</button>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);

        // Close modal events
        $('#ai-preview-modal .ai-preview-close').on('click', function() {
            $('#ai-preview-modal').remove();
        });

        // Close on background click
        $('#ai-preview-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).remove();
            }
        });
    }

    function switchToHomepageTab() {
        $('#ai-preview-modal').remove();
        $('.nav-tab[data-tab="homepage"]').click();
        showNotice('💡 Use the AI text regeneration buttons to apply changes to your website.', 'info');
    }

    function clearBusinessInfo() {
        if (!confirm('⚠️ Are you sure you want to clear all business information?\n\nThis action cannot be undone. All your entered business data will be permanently deleted.')) {
            return;
        }

        const $clearBtn = $('#clear-business-info');
        const originalText = $clearBtn.html();
        $clearBtn.html('<span class="dashicons dashicons-update-alt"></span> Clearing...').prop('disabled', true);

        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'clear_business_info',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('✅ All business information cleared successfully!', 'success');
                    // Clear the form
                    $('#business-info-form')[0].reset();
                    $('input[name="devices_repaired[]"]').prop('checked', false);
                    updateCompletionStatus();
                } else {
                    showNotice('❌ Error clearing business information: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('❌ Error clearing business information. Please try again.', 'error');
            },
            complete: function() {
                $clearBtn.html(originalText).prop('disabled', false);
            }
        });
    }

    // ========================================
    // 🎯 SECTION-SPECIFIC APPLY/PREVIEW FUNCTIONALITY
    // ========================================

    // Section Apply Changes
    $('.section-apply-btn').on('click', function() {
        const section = $(this).data('section');
        applySectionChanges(section, $(this));
    });

    // Section Preview
    $('.section-preview-btn').on('click', function() {
        const section = $(this).data('section');
        previewSectionChanges(section, $(this));
    });

    function applySectionChanges(section, $button) {
        console.log(`🎯 [SECTION] Applying changes for section: ${section}`);

        // Get section data
        const sectionData = getSectionData(section);
        if (!sectionData) {
            showNotice(`❌ No changes detected in ${section} section`, 'error');
            return;
        }

        // Show loading state
        const originalText = $button.html();
        $button.html('<span class="dashicons dashicons-update-alt"></span> Applying...').prop('disabled', true);

        // Apply changes via AJAX
        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'apply_section_changes',
                nonce: website_generator_ajax.nonce,
                section: section,
                section_data: sectionData
            },
            success: function(response) {
                if (response.success) {
                    showNotice(`✅ ${section} section updated successfully!`, 'success');
                    updateSectionStatus(section, 'applied');
                } else {
                    showNotice(`❌ Error updating ${section} section: ` + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice(`❌ Error updating ${section} section. Please try again.`, 'error');
            },
            complete: function() {
                $button.html(originalText).prop('disabled', false);
            }
        });
    }

    function previewSectionChanges(section, $button) {
        console.log(`👁️ [SECTION] Previewing changes for section: ${section}`);

        // Get section data
        const sectionData = getSectionData(section);
        if (!sectionData) {
            showNotice(`❌ No changes detected in ${section} section`, 'error');
            return;
        }

        // Show loading state
        const originalText = $button.html();
        $button.html('<span class="dashicons dashicons-update-alt"></span> Previewing...').prop('disabled', true);

        // Generate preview via AJAX
        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'preview_section_changes',
                nonce: website_generator_ajax.nonce,
                section: section,
                section_data: sectionData
            },
            success: function(response) {
                if (response.success) {
                    showSectionPreviewModal(section, response.data);
                } else {
                    showNotice(`❌ Error previewing ${section} section: ` + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice(`❌ Error previewing ${section} section. Please try again.`, 'error');
            },
            complete: function() {
                $button.html(originalText).prop('disabled', false);
            }
        });
    }

    function getSectionData(section) {
        const $block = $(`.visual-block[data-block="${section}"]`);
        const data = {};

        // Get all inputs, textareas, and selects in the section
        $block.find('input, textarea, select').each(function() {
            const $field = $(this);
            const name = $field.attr('name') || $field.attr('id');

            if (name) {
                if ($field.attr('type') === 'checkbox') {
                    data[name] = $field.is(':checked');
                } else if ($field.attr('type') === 'file') {
                    // Handle file uploads separately
                    if ($field[0].files && $field[0].files[0]) {
                        data[name] = $field[0].files[0];
                    }
                } else {
                    data[name] = $field.val();
                }
            }
        });

        return Object.keys(data).length > 0 ? data : null;
    }

    function updateSectionStatus(section, status) {
        const $statusIndicator = $(`.visual-block[data-block="${section}"] .status-indicator`);

        switch(status) {
            case 'applied':
                $statusIndicator.attr('data-status', 'applied').text('Applied');
                break;
            case 'modified':
                $statusIndicator.attr('data-status', 'modified').text('Modified');
                break;
            case 'unchanged':
                $statusIndicator.attr('data-status', 'unchanged').text('Unchanged');
                break;
        }
    }

    function showSectionPreviewModal(section, previewData) {
        let modalHtml = `
            <div id="section-preview-modal" class="section-preview-modal">
                <div class="section-preview-content">
                    <div class="section-preview-header">
                        <h3>🔍 ${section.charAt(0).toUpperCase() + section.slice(1)} Section Preview</h3>
                        <button type="button" class="section-preview-close">&times;</button>
                    </div>
                    <div class="section-preview-body">
                        <p><strong>Preview of changes to the ${section} section:</strong></p>
                        <div class="preview-content">
                            ${previewData.preview_html || 'Preview content will appear here'}
                        </div>
                    </div>
                    <div class="section-preview-footer">
                        <button type="button" class="button button-secondary section-preview-close">Close Preview</button>
                        <button type="button" class="button button-primary" onclick="applySectionFromPreview('${section}')">Apply These Changes</button>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);

        // Close modal events
        $('#section-preview-modal .section-preview-close').on('click', function() {
            $('#section-preview-modal').remove();
        });

        // Close on background click
        $('#section-preview-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).remove();
            }
        });
    }

    window.applySectionFromPreview = function(section) {
        $('#section-preview-modal').remove();
        $(`.section-apply-btn[data-section="${section}"]`).click();
    };

    // Track changes in sections
    $('.visual-block input, .visual-block textarea, .visual-block select').on('input change', function() {
        const $block = $(this).closest('.visual-block');
        const section = $block.data('block');
        updateSectionStatus(section, 'modified');
    });
});
</script>
