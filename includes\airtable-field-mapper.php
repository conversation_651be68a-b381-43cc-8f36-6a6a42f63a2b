<?php
/**
 * Airtable Field Mapper
 * 
 * Maps Airtable form fields to WordPress plugin fields
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class AirtableFieldMapper {
    
    private $business_info_fields;
    private $visual_blocks_fields;
    private $design_system_fields;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_field_mappings();
    }
    
    /**
     * Initialize field mappings
     */
    private function init_field_mappings() {
        // Business information fields (19 fields)
        $this->business_info_fields = array(
            'business_name',
            'city',
            'state',
            'devices_repaired',
            'specializations',
            'brands_supported',
            'years_experience',
            'warranty_offered',
            'turnaround_time',
            'service_area',
            'additional_services',
            'phone',
            'email',
            'website',
            'facebook',
            'instagram',
            'twitter',
            'google_business',
            'key_benefits'
        );
        
        // Visual blocks content fields - CORRECTED to match actual theme field names
        $this->visual_blocks_fields = array(
            // Hero Section - matches actual theme fields
            'hero_title',           // Main hero heading
            'hero_tagline',         // Hero tagline/subtitle
            'hero_description',     // Hero description text

            // Call-to-Action Section - matches actual theme fields
            'cta_title',           // CTA heading
            'cta_description',     // CTA description

            // Benefits Section - matches actual theme fields
            'benefit1_title',
            'benefit1_description',
            'benefit2_title',
            'benefit2_description',
            'benefit3_title',
            'benefit3_description',
            'benefit4_title',
            'benefit4_description',

            // Contact Information - matches actual theme fields
            'phone_number',        // Business phone
            'email',              // Business email
            'address',            // Business address

            // Footer
            'copyright_text'
        );
        
        // Design system fields (4 fields)
        $this->design_system_fields = array(
            'primary_color',
            'secondary_color',
            'accent_color',
            'button_text_color'
        );
    }
    
    /**
     * Map Airtable data to WordPress plugin structure
     */
    public function map_airtable_to_wordpress($airtable_data) {
        $mapped_data = array(
            'business_info' => array(),
            'visual_blocks' => array(),
            'design_system' => array(),
            'logo' => null
        );
        
        // Map business information fields
        foreach ($this->business_info_fields as $field) {
            if (isset($airtable_data[$field])) {
                $mapped_data['business_info'][$field] = $this->transform_field_value($field, $airtable_data[$field]);
            }
        }
        
        // Map visual blocks fields
        foreach ($this->visual_blocks_fields as $field) {
            if (isset($airtable_data[$field])) {
                $mapped_data['visual_blocks'][$field] = $this->transform_field_value($field, $airtable_data[$field]);
            }
        }
        
        // Map design system fields
        foreach ($this->design_system_fields as $field) {
            if (isset($airtable_data[$field])) {
                $mapped_data['design_system'][$field] = $this->transform_field_value($field, $airtable_data[$field]);
            }
        }
        
        // Handle logo upload
        if (isset($airtable_data['logo_upload']) && !empty($airtable_data['logo_upload'])) {
            $mapped_data['logo'] = $this->process_logo_data($airtable_data['logo_upload']);
        }
        
        return $mapped_data;
    }
    
    /**
     * Transform field value based on field type
     */
    private function transform_field_value($field_name, $value) {
        // Handle array fields (multiple select in Airtable)
        if (in_array($field_name, array('devices_repaired', 'specializations', 'brands_supported', 'additional_services'))) {
            return $this->transform_array_field($value);
        }
        
        // Handle color fields
        if (in_array($field_name, $this->design_system_fields)) {
            return $this->transform_color_field($value);
        }
        
        // Handle phone fields
        if (in_array($field_name, array('phone', 'contact_phone'))) {
            return $this->transform_phone_field($value);
        }
        
        // Handle email fields
        if (in_array($field_name, array('email', 'contact_email'))) {
            return $this->transform_email_field($value);
        }
        
        // Handle URL fields
        if (in_array($field_name, array('website', 'facebook', 'instagram', 'twitter', 'google_business'))) {
            return $this->transform_url_field($value);
        }
        
        // Handle numeric fields
        if ($field_name === 'years_experience') {
            return $this->transform_numeric_field($value);
        }
        
        // Default: return as string
        return sanitize_text_field($value);
    }
    
    /**
     * Transform array field (multiple select)
     */
    private function transform_array_field($value) {
        if (is_array($value)) {
            return array_map('sanitize_text_field', $value);
        } elseif (is_string($value)) {
            // Handle comma-separated values
            return array_map('trim', array_map('sanitize_text_field', explode(',', $value)));
        }
        return array();
    }
    
    /**
     * Transform color field
     */
    private function transform_color_field($value) {
        $color = sanitize_text_field($value);
        
        // Add # if missing
        if (strpos($color, '#') !== 0) {
            $color = '#' . $color;
        }
        
        // Validate hex format
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $color)) {
            // Return default colors based on field
            $defaults = array(
                'primary_color' => '#165C9C',
                'secondary_color' => '#111111',
                'accent_color' => '#FFFFFF',
                'button_text_color' => '#FFFFFF'
            );
            return $defaults[$field_name] ?? '#000000';
        }
        
        return $color;
    }
    
    /**
     * Transform phone field
     */
    private function transform_phone_field($value) {
        // Remove all non-numeric characters except + and common separators
        $phone = preg_replace('/[^0-9+\s\-\(\)]/', '', $value);
        return sanitize_text_field($phone);
    }
    
    /**
     * Transform email field
     */
    private function transform_email_field($value) {
        return sanitize_email($value);
    }
    
    /**
     * Transform URL field
     */
    private function transform_url_field($value) {
        // Add http:// if no protocol specified
        if (!empty($value) && !preg_match('/^https?:\/\//', $value)) {
            $value = 'http://' . $value;
        }
        return esc_url_raw($value);
    }
    
    /**
     * Transform numeric field
     */
    private function transform_numeric_field($value) {
        return intval($value);
    }
    
    /**
     * Process logo data from Airtable
     */
    private function process_logo_data($logo_data) {
        if (is_array($logo_data) && !empty($logo_data)) {
            // Airtable attachment format
            $attachment = $logo_data[0]; // Get first attachment
            
            return array(
                'url' => $attachment['url'] ?? '',
                'filename' => $attachment['filename'] ?? '',
                'type' => $attachment['type'] ?? '',
                'size' => $attachment['size'] ?? 0
            );
        }
        
        return null;
    }
    
    /**
     * Get field mapping configuration
     */
    public function get_field_mappings() {
        return array(
            'business_info' => $this->business_info_fields,
            'visual_blocks' => $this->visual_blocks_fields,
            'design_system' => $this->design_system_fields
        );
    }
    
    /**
     * Validate mapped data structure
     */
    public function validate_mapped_data($mapped_data) {
        $errors = array();
        
        // Check required structure
        $required_keys = array('business_info', 'visual_blocks', 'design_system');
        foreach ($required_keys as $key) {
            if (!isset($mapped_data[$key]) || !is_array($mapped_data[$key])) {
                $errors[] = "Missing or invalid {$key} section";
            }
        }
        
        // Validate business info required fields
        if (isset($mapped_data['business_info'])) {
            $required_business_fields = array('business_name');
            foreach ($required_business_fields as $field) {
                if (empty($mapped_data['business_info'][$field])) {
                    $errors[] = "Required business field missing: {$field}";
                }
            }
        }
        
        // Validate color fields
        if (isset($mapped_data['design_system'])) {
            foreach ($mapped_data['design_system'] as $field => $value) {
                if (in_array($field, $this->design_system_fields)) {
                    if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $value)) {
                        $errors[] = "Invalid color format for {$field}: {$value}";
                    }
                }
            }
        }
        
        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }
    
    /**
     * Get WordPress field names for Airtable field
     */
    public function get_wordpress_field_name($airtable_field) {
        // Direct mapping for most fields
        $field_mappings = array(
            // Business info mappings
            'Business Name' => 'business_name',
            'City' => 'city',
            'State' => 'state',
            'Devices Repaired' => 'devices_repaired',
            'Specializations' => 'specializations',
            'Brands Supported' => 'brands_supported',
            'Years Experience' => 'years_experience',
            'Warranty Offered' => 'warranty_offered',
            'Turnaround Time' => 'turnaround_time',
            'Service Area' => 'service_area',
            'Additional Services' => 'additional_services',
            'Phone' => 'phone',
            'Email' => 'email',
            'Website' => 'website',
            'Facebook' => 'facebook',
            'Instagram' => 'instagram',
            'Twitter' => 'twitter',
            'Google Business' => 'google_business',
            'Key Benefits' => 'key_benefits',
            
            // Visual blocks mappings - Direct field name mapping (no conversion)
            'hero_title' => 'hero_title',
            'hero_tagline' => 'hero_tagline',
            'Hero Description' => 'hero_description',
            'CTA Heading' => 'cta_title',
            'CTA Description' => 'cta_description',
            'Benefit 1 Title' => 'benefit1_title',
            'Benefit 1 Description' => 'benefit1_description',
            'Benefit 2 Title' => 'benefit2_title',
            'Benefit 2 Description' => 'benefit2_description',
            'Benefit 3 Title' => 'benefit3_title',
            'Benefit 3 Description' => 'benefit3_description',
            'Benefit 4 Title' => 'benefit4_title',
            'Benefit 4 Description' => 'benefit4_description',
            'Contact Phone' => 'phone_number',
            'Contact Email' => 'email',
            'Contact Address' => 'address',
            'Copyright Text' => 'copyright_text',
            
            // Design system mappings
            'Primary Color' => 'primary_color',
            'Secondary Color' => 'secondary_color',
            'Accent Color' => 'accent_color',
            'Button Text Color' => 'button_text_color',
            
            // Logo mapping
            'Logo Upload' => 'logo_upload'
        );
        
        return $field_mappings[$airtable_field] ?? strtolower(str_replace(' ', '_', $airtable_field));
    }
}
