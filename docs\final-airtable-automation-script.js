// Get input variables (configured in the automation input section)
let inputConfig = input.config();

console.log('🔍 Full input config:', JSON.stringify(inputConfig, null, 2));

// Method 1: Use individual input variables (RECOMMENDED) - STANDARDIZED FIELD NAMES
let heroTitle = inputConfig.hero_title || '';
let heroTagline = inputConfig.hero_tagline || '';
let siteIdentifier = inputConfig.siteIdentifier || 'example-site';

console.log('📝 Individual inputs (standardized field names):');
console.log('  Hero Title:', heroTitle);
console.log('  Hero Tagline:', heroTagline);
console.log('  Site Identifier:', siteIdentifier);

// Method 2: Fallback to record field access if individual inputs not configured
if (!heroTitle || !heroTagline) {
    console.log('🔄 Trying record field access...');
    
    let record = inputConfig.record;
    
    if (record && record.getCellValue) {
        try {
            heroTitle = heroTitle || record.getCellValue('hero_title') || '';
            heroTagline = heroTagline || record.getCellValue('hero_tagline') || '';
            siteIdentifier = siteIdentifier || record.getCellValue('Site Identifier') || 'example-site';
            
            console.log('✅ From getCellValue (standardized field names):');
            console.log('  Hero Title:', heroTitle);
            console.log('  Hero Tagline:', heroTagline);
            console.log('  Site Identifier:', siteIdentifier);
        } catch (error) {
            console.log('⚠️ getCellValue failed:', error.message);
        }
    }
}

// Method 3: Ultimate fallback with validation
if (!heroTitle || heroTitle.trim() === '') {
    heroTitle = "Test from Airtable - " + new Date().toLocaleTimeString();
    console.log('⚠️ Using fallback hero title');
}

if (!heroTagline || heroTagline.trim() === '') {
    heroTagline = "Smartphones | Tablets | Computers | & More";
    console.log('⚠️ Using fallback hero tagline');
}

// Clean the values (remove any concatenation artifacts)
heroTitle = heroTitle.replace(/example-site.*$/, '').trim();
heroTagline = heroTagline.replace(/^.*example-site/, '').trim();

console.log('🧹 Cleaned values:');
console.log('  Hero Title:', heroTitle);
console.log('  Hero Tagline:', heroTagline);

// Webhook configuration - REAL VALUES
const apiKey = 'airtable_6E9LuKzcRGyp2t97lDcQZFLHrCmXAkPp';
const webhookUrl = 'https://template-plugin-testing.repairlift.site/wp-admin/admin-ajax.php?action=airtable_webhook';

// Create payload with standardized field names
let payload = {
    api_key: apiKey,
    site_identifier: siteIdentifier,
    hero_title: heroTitle,      // Standardized field name
    hero_tagline: heroTagline   // Standardized field name
};

console.log('🚀 Final payload:', JSON.stringify(payload, null, 2));

// Send webhook
try {
    let response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
    });

    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    let result = await response.json();
    console.log('📨 Webhook response:', result);

    if (result.success) {
        console.log('✅ Hero title updated successfully to:', heroTitle);
        console.log('✅ Hero tagline updated successfully to:', heroTagline);
    } else {
        console.log('❌ Update failed:', result.data);
    }
} catch (error) {
    console.log('❌ Webhook error:', error.message);
}
