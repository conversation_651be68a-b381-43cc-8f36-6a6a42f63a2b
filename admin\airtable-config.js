/**
 * Airtable Configuration JavaScript
 * 
 * Handles the admin interface for Airtable integration
 */

jQuery(document).ready(function($) {
    
    // Initialize Airtable configuration
    initAirtableConfig();
    
    /**
     * Initialize Airtable configuration interface
     */
    function initAirtableConfig() {
        // Load webhook logs on page load
        loadWebhookLogs();
        
        // Bind event handlers
        bindEventHandlers();
        
        // Initialize visibility toggles
        initVisibilityToggles();
    }
    
    /**
     * Bind event handlers
     */
    function bindEventHandlers() {
        // Save configuration
        $('#airtable-config-form').on('submit', handleSaveConfig);
        
        // Test webhook
        $('#test-webhook').on('click', handleTestWebhook);
        
        // Generate API key
        $('#generate-api-key').on('click', generateApiKey);
        
        // Copy webhook URL
        $('.copy-webhook-url').on('click', copyWebhookUrl);
        
        // Refresh logs
        $('#refresh-logs').on('click', loadWebhookLogs);
        
        // Clear logs
        $('#clear-logs').on('click', clearWebhookLogs);

        // Run test suite
        $('#run-test-suite').on('click', runTestSuite);

        // Database diagnostic
        $('#check-database-data').on('click', checkDatabaseData);

        // Test block manager
        $('#test-block-manager').on('click', testBlockManager);

        // Toggle visibility buttons
        $('.toggle-visibility').on('click', toggleFieldVisibility);
    }
    
    /**
     * Initialize visibility toggles
     */
    function initVisibilityToggles() {
        $('.toggle-visibility').each(function() {
            const $button = $(this);
            const targetId = $button.data('target');
            const $target = $('#' + targetId);
            
            if ($target.attr('type') === 'password') {
                $button.find('.dashicons').removeClass('dashicons-hidden').addClass('dashicons-visibility');
            }
        });
    }
    
    /**
     * Handle save configuration
     */
    function handleSaveConfig(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitBtn = $('#save-airtable-config');
        const originalText = $submitBtn.html();
        
        // Show loading state
        $submitBtn.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Saving...');
        
        // Prepare form data
        const formData = {
            action: 'save_airtable_config',
            airtable_config_nonce: $('input[name="airtable_config_nonce"]').val(),
            airtable_integration_enabled: $('#airtable_integration_enabled').is(':checked') ? 1 : 0,
            airtable_api_key: $('#airtable_api_key').val(),
            airtable_webhook_secret: $('#airtable_webhook_secret').val(),
            airtable_site_identifier: $('#airtable_site_identifier').val(),
            airtable_rate_limit: $('#airtable_rate_limit').val()
        };
        
        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showNotice('Configuration saved successfully!', 'success');
                    updateStatusIndicators();
                } else {
                    showNotice('Error: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('Network error. Please try again.', 'error');
            },
            complete: function() {
                // Restore button state
                $submitBtn.prop('disabled', false).html(originalText);
            }
        });
    }
    
    /**
     * Handle test webhook
     */
    function handleTestWebhook() {
        const $button = $('#test-webhook');
        const originalText = $button.html();
        
        // Show loading state
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Testing...');
        
        // Send test request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'test_airtable_webhook',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('Webhook test successful!', 'success');
                    loadWebhookLogs(); // Refresh logs to show test entry
                } else {
                    showNotice('Webhook test failed: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('Network error during webhook test.', 'error');
            },
            complete: function() {
                // Restore button state
                $button.prop('disabled', false).html(originalText);
            }
        });
    }
    
    /**
     * Generate random API key
     */
    function generateApiKey() {
        // Generate a random UUID-like string
        const apiKey = 'airtable_' + generateRandomString(32);
        $('#airtable_api_key').val(apiKey);
        showNotice('Random API key generated. Remember to save your configuration.', 'info');
    }
    
    /**
     * Generate random string
     */
    function generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    /**
     * Copy webhook URL to clipboard
     */
    function copyWebhookUrl() {
        const $button = $(this);
        const url = $button.data('url');
        
        // Create temporary input to copy from
        const $temp = $('<input>');
        $('body').append($temp);
        $temp.val(url).select();
        document.execCommand('copy');
        $temp.remove();
        
        // Show feedback
        const originalText = $button.html();
        $button.html('<span class="dashicons dashicons-yes"></span> Copied!');
        
        setTimeout(function() {
            $button.html(originalText);
        }, 2000);
    }
    
    /**
     * Toggle field visibility
     */
    function toggleFieldVisibility() {
        const $button = $(this);
        const targetId = $button.data('target');
        const $target = $('#' + targetId);
        const $icon = $button.find('.dashicons');
        
        if ($target.attr('type') === 'password') {
            $target.attr('type', 'text');
            $icon.removeClass('dashicons-visibility').addClass('dashicons-hidden');
        } else {
            $target.attr('type', 'password');
            $icon.removeClass('dashicons-hidden').addClass('dashicons-visibility');
        }
    }
    
    /**
     * Load webhook logs
     */
    function loadWebhookLogs() {
        const $container = $('#webhook-logs-content');
        
        $container.html('<p>Loading webhook logs...</p>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_airtable_logs',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayWebhookLogs(response.data.logs);
                } else {
                    $container.html('<p>Error loading logs: ' + (response.data || 'Unknown error') + '</p>');
                }
            },
            error: function() {
                $container.html('<p>Network error loading logs.</p>');
            }
        });
    }
    
    /**
     * Display webhook logs
     */
    function displayWebhookLogs(logs) {
        const $container = $('#webhook-logs-content');
        
        if (!logs || logs.length === 0) {
            $container.html('<p>No webhook activity recorded yet.</p>');
            return;
        }
        
        let html = '<div class="webhook-logs-list">';
        
        logs.forEach(function(log) {
            const timestamp = new Date(log.timestamp).toLocaleString();
            const status = log.status || 'info';
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'info';
            
            html += '<div class="log-entry log-' + statusClass + '">';
            html += '<div class="log-header">';
            html += '<span class="log-timestamp">' + timestamp + '</span>';
            html += '<span class="log-status status-' + statusClass + '">' + status.toUpperCase() + '</span>';
            html += '</div>';
            
            if (log.message) {
                html += '<div class="log-message">' + escapeHtml(log.message) + '</div>';
            }
            
            if (log.ip) {
                html += '<div class="log-ip">IP: ' + escapeHtml(log.ip) + '</div>';
            }
            
            html += '</div>';
        });
        
        html += '</div>';
        
        $container.html(html);
    }
    
    /**
     * Clear webhook logs
     */
    function clearWebhookLogs() {
        if (!confirm('Are you sure you want to clear all webhook logs?')) {
            return;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'clear_airtable_logs',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('Webhook logs cleared successfully.', 'success');
                    loadWebhookLogs(); // Refresh logs display
                } else {
                    showNotice('Error clearing logs: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('Network error clearing logs.', 'error');
            }
        });
    }

    /**
     * Run comprehensive test suite
     */
    function runTestSuite() {
        const $button = $('#run-test-suite');
        const $container = $('#test-results-content');
        const originalText = $button.html();

        // Show loading state
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Running Tests...');
        $container.html('<p>Running comprehensive integration tests...</p>');

        // Send test request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'run_airtable_test_suite',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayTestResults(response.data);
                    showNotice('Test suite completed successfully!', 'success');
                } else {
                    $container.html('<p class="error">Test suite failed: ' + (response.data || 'Unknown error') + '</p>');
                    showNotice('Test suite failed: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                $container.html('<p class="error">Network error during test execution.</p>');
                showNotice('Network error during test execution.', 'error');
            },
            complete: function() {
                // Restore button state
                $button.prop('disabled', false).html(originalText);
            }
        });
    }

    /**
     * Display test results
     */
    function displayTestResults(results) {
        const $container = $('#test-results-content');

        let html = '<div class="test-results">';

        // Overall summary
        html += '<div class="test-summary test-' + results.overall_status + '">';
        html += '<h4>Test Summary</h4>';
        html += '<p>' + results.summary + '</p>';
        html += '<div class="test-stats">';
        html += '<span class="stat">Success Rate: ' + results.success_rate + '%</span>';
        html += '<span class="stat">Tests Run: ' + results.tests_run + '</span>';
        html += '<span class="stat">Passed: ' + results.tests_passed + '</span>';
        html += '<span class="stat">Failed: ' + results.tests_failed + '</span>';
        html += '</div>';
        html += '</div>';

        // Test categories
        for (const category in results.test_results) {
            const categoryResults = results.test_results[category];
            html += '<div class="test-category">';
            html += '<h4>' + categoryResults.category + ' Tests</h4>';

            categoryResults.tests.forEach(function(test) {
                const statusClass = test.status === 'pass' ? 'success' : test.status === 'warning' ? 'warning' : 'error';
                const statusIcon = test.status === 'pass' ? '✓' : test.status === 'warning' ? '⚠' : '✗';

                html += '<div class="test-item test-' + statusClass + '">';
                html += '<div class="test-header">';
                html += '<span class="test-icon">' + statusIcon + '</span>';
                html += '<span class="test-name">' + escapeHtml(test.name) + '</span>';
                html += '<span class="test-status">' + test.status.toUpperCase() + '</span>';
                html += '</div>';
                html += '<div class="test-message">' + escapeHtml(test.message) + '</div>';
                html += '</div>';
            });

            html += '</div>';
        }

        // Recommendations
        if (results.recommendations && results.recommendations.length > 0) {
            html += '<div class="test-recommendations">';
            html += '<h4>Recommendations</h4>';
            html += '<ul>';
            results.recommendations.forEach(function(recommendation) {
                const isCritical = recommendation.startsWith('CRITICAL:');
                const recClass = isCritical ? 'critical' : 'normal';
                html += '<li class="recommendation ' + recClass + '">' + escapeHtml(recommendation) + '</li>';
            });
            html += '</ul>';
            html += '</div>';
        }

        html += '</div>';

        $container.html(html);
    }
    
    /**
     * Update status indicators
     */
    function updateStatusIndicators() {
        // This would update the status indicators based on current configuration
        // Implementation depends on the specific UI requirements
        location.reload(); // Simple approach - reload page to show updated status
    }
    
    /**
     * Show notice message
     */
    function showNotice(message, type) {
        // Remove existing notices
        $('.airtable-notice').remove();
        
        // Create notice element
        const noticeClass = 'notice notice-' + type + ' airtable-notice';
        const $notice = $('<div class="' + noticeClass + '"><p>' + escapeHtml(message) + '</p></div>');
        
        // Insert notice
        $('.airtable-config-container').prepend($notice);
        
        // Auto-hide success notices
        if (type === 'success') {
            setTimeout(function() {
                $notice.fadeOut();
            }, 3000);
        }
        
        // Scroll to top to show notice
        $('html, body').animate({ scrollTop: 0 }, 300);
    }
    
    /**
     * Check database data
     */
    function checkDatabaseData() {
        const $button = $('#check-database-data');
        const $results = $('#database-diagnostic-results');
        const originalText = $button.html();

        // Update button state
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Checking...');

        // Show results container
        $results.show().html('<p>Checking database values...</p>');

        // Send diagnostic request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'check_airtable_database_data',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayDatabaseDiagnostic(response.data);
                } else {
                    $results.html('<p style="color: red;">Diagnostic failed: ' + (response.data || 'Unknown error') + '</p>');
                }
            },
            error: function() {
                $results.html('<p style="color: red;">Network error during diagnostic.</p>');
            },
            complete: function() {
                // Restore button state
                $button.prop('disabled', false).html(originalText);
            }
        });
    }

    /**
     * Display database diagnostic results
     */
    function displayDatabaseDiagnostic(data) {
        const $results = $('#database-diagnostic-results');

        let html = '<h4>Current Database Values:</h4>';

        // Custom Texts
        html += '<div style="margin-bottom: 20px;">';
        html += '<h5>Custom Texts (website_generator_custom_texts):</h5>';
        if (data.custom_texts && Object.keys(data.custom_texts).length > 0) {
            html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ccc; max-height: 200px; overflow-y: auto;">';
            html += JSON.stringify(data.custom_texts, null, 2);
            html += '</pre>';

            if (data.custom_texts.hero_heading) {
                html += '<p style="color: green;"><strong>✓ Hero Heading Found:</strong> ' + escapeHtml(data.custom_texts.hero_heading) + '</p>';
            } else {
                html += '<p style="color: red;"><strong>✗ Hero Heading NOT Found</strong></p>';
            }
        } else {
            html += '<p style="color: orange;">No custom texts found in database</p>';
        }
        html += '</div>';

        // Business Info
        html += '<div style="margin-bottom: 20px;">';
        html += '<h5>Business Info (website_generator_business_info):</h5>';
        if (data.business_info && Object.keys(data.business_info).length > 0) {
            html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ccc; max-height: 200px; overflow-y: auto;">';
            html += JSON.stringify(data.business_info, null, 2);
            html += '</pre>';
        } else {
            html += '<p style="color: orange;">No business info found in database</p>';
        }
        html += '</div>';

        // Design Settings
        html += '<div style="margin-bottom: 20px;">';
        html += '<h5>Design Settings (website_generator_design_settings):</h5>';
        if (data.design_settings && Object.keys(data.design_settings).length > 0) {
            html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ccc; max-height: 200px; overflow-y: auto;">';
            html += JSON.stringify(data.design_settings, null, 2);
            html += '</pre>';
        } else {
            html += '<p style="color: orange;">No design settings found in database</p>';
        }
        html += '</div>';

        // Recent Webhook Logs
        html += '<div style="margin-bottom: 20px;">';
        html += '<h5>Recent Webhook Logs (last 3):</h5>';
        if (data.webhook_logs && data.webhook_logs.length > 0) {
            html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ccc; max-height: 200px; overflow-y: auto;">';
            html += JSON.stringify(data.webhook_logs, null, 2);
            html += '</pre>';
        } else {
            html += '<p style="color: orange;">No webhook logs found</p>';
        }
        html += '</div>';

        // Homepage Content Structure
        html += '<div style="margin-bottom: 20px;">';
        html += '<h5>Homepage Content Structure:</h5>';
        if (data.homepage_id) {
            html += '<p><strong>Homepage ID:</strong> ' + data.homepage_id + '</p>';
            html += '<p><strong>Homepage Title:</strong> ' + (data.homepage_title || 'No title') + '</p>';
            html += '<p><strong>Content Length:</strong> ' + (data.homepage_content_length || 0) + ' characters</p>';

            if (data.homepage_content_preview) {
                html += '<h6>Homepage Content Structure (first 2000 chars):</h6>';
                html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ccc; max-height: 400px; overflow-y: auto; font-size: 10px; white-space: pre-wrap;">';
                html += escapeHtml(data.homepage_content_preview);
                if (data.homepage_content.length > 2000) {
                    html += '\n\n... (truncated, total length: ' + data.homepage_content.length + ' chars)';
                }
                html += '</pre>';

                // Look for hero title patterns
                html += '<h6>Text Pattern Analysis:</h6>';
                html += '<div style="background: #f0f0f0; padding: 10px; border: 1px solid #ccc;">';

                if (data.homepage_content.includes('Premier Device Repair in Anytown, FL')) {
                    html += '<p style="color: green;">✓ Found original text: "Premier Device Repair in Anytown, FL"</p>';
                } else {
                    html += '<p style="color: red;">✗ Original text "Premier Device Repair in Anytown, FL" NOT found</p>';
                }

                if (data.homepage_content.includes('Premier Device Repair in Medellin, Colombia')) {
                    html += '<p style="color: green;">✓ Found updated text: "Premier Device Repair in Medellin, Colombia"</p>';
                } else {
                    html += '<p style="color: red;">✗ Updated text "Premier Device Repair in Medellin, Colombia" NOT found</p>';
                }

                if (data.homepage_content.includes('<h1')) {
                    html += '<p style="color: blue;">ℹ Found H1 tags in content</p>';
                } else {
                    html += '<p style="color: orange;">⚠ No H1 tags found in content</p>';
                }

                html += '</div>';
            } else {
                html += '<p style="color: orange;">No homepage content found</p>';
            }
        } else {
            html += '<p style="color: red;">No homepage set (page_on_front option not configured)</p>';
        }
        html += '</div>';

        $results.html(html);
    }

    /**
     * Escape HTML for safe display
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Test the ContentBlockManager
     */
    function testBlockManager() {
        const $button = $('#test-block-manager');
        const $results = $('#database-diagnostic-results');
        const originalText = $button.html();

        // Get test heading from user
        const testHeading = prompt('Enter test heading:', 'Test Hero Heading from Block Manager - ' + new Date().toLocaleTimeString());
        if (!testHeading) {
            return;
        }

        // Update button state
        $button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Testing...');

        // Show results container
        $results.show().html('<p>Testing ContentBlockManager...</p>');

        // Send test request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'test_content_block_manager',
                test_heading: testHeading,
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayBlockManagerResults(response.data, testHeading);
                } else {
                    $results.html('<p style="color: red;">Test failed: ' + (response.data || 'Unknown error') + '</p>');
                }
            },
            error: function() {
                $results.html('<p style="color: red;">AJAX request failed</p>');
            },
            complete: function() {
                // Reset button
                $button.prop('disabled', false).html(originalText);
            }
        });
    }

    /**
     * Display block manager test results
     */
    function displayBlockManagerResults(data, testHeading) {
        const $results = $('#database-diagnostic-results');

        let html = '<h4>🧪 ContentBlockManager Test Results:</h4>';
        html += '<p><strong>Test Heading:</strong> ' + escapeHtml(testHeading) + '</p>';

        // Update result
        if (data.update_result) {
            if (data.update_result.success) {
                html += '<p style="color: green;">✅ <strong>Block Update:</strong> ' + data.update_result.message + '</p>';
                html += '<p><strong>Post ID:</strong> ' + (data.update_result.post_id || 'N/A') + '</p>';
                html += '<p><strong>Content Length Change:</strong> ' + (data.update_result.old_content_length || 'N/A') + ' → ' + (data.update_result.new_content_length || 'N/A') + ' characters</p>';
                if (data.update_result.verification_heading) {
                    html += '<p><strong>Verification - Current Heading:</strong> "' + escapeHtml(data.update_result.verification_heading) + '"</p>';
                }
            } else {
                html += '<p style="color: red;">❌ <strong>Block Update Failed:</strong> ' + data.update_result.message + '</p>';
            }
        }

        // Structure analysis
        if (data.structure_analysis) {
            html += '<h5>🔍 Block Structure Analysis:</h5>';

            if (data.structure_analysis.all_headings_found) {
                html += '<h6>All Heading Blocks Found:</h6>';
                html += '<div style="background: #fff; padding: 10px; border: 1px solid #ccc; margin: 10px 0;">';

                data.structure_analysis.all_headings_found.forEach(function(heading, index) {
                    html += '<div style="border-bottom: 1px solid #eee; padding: 5px 0;">';
                    html += '<strong>Heading ' + (index + 1) + ':</strong> ';
                    html += '<span style="color: ' + (heading.is_hero ? 'green' : 'blue') + ';">' + escapeHtml(heading.content) + '</span>';
                    html += ' (Level: ' + heading.heading_level + ', Depth: ' + heading.level + ')';
                    if (heading.is_hero) {
                        html += ' <span style="color: green; font-weight: bold;">← HERO DETECTED</span>';
                    }
                    html += '<br><small>Classes: ' + escapeHtml(heading.classes) + '</small>';
                    html += '</div>';
                });

                html += '</div>';
            }

            if (data.structure_analysis.raw_content_preview) {
                html += '<h6>Raw Content Preview:</h6>';
                html += '<pre style="background: #fff; padding: 10px; border: 1px solid #ccc; max-height: 200px; overflow-y: auto; font-size: 10px;">';
                html += escapeHtml(data.structure_analysis.raw_content_preview);
                html += '</pre>';
            }
        }

        html += '<p style="margin-top: 15px;"><strong>Next Step:</strong> Check your website to see if the heading changed!</p>';
        html += '<p><a href="' + window.location.origin + '" target="_blank" class="button button-secondary">🌐 View Website</a></p>';

        $results.html(html);
    }

});

// CSS for spinning animation
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .webhook-logs-list {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .log-entry {
        padding: 10px;
        margin-bottom: 5px;
        border-left: 4px solid #ddd;
        background: #f9f9f9;
    }
    
    .log-entry.log-success {
        border-left-color: #46b450;
    }
    
    .log-entry.log-error {
        border-left-color: #dc3232;
    }
    
    .log-entry.log-info {
        border-left-color: #0073aa;
    }
    
    .log-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .log-timestamp {
        font-size: 12px;
        color: #666;
    }
    
    .log-status {
        font-size: 11px;
        font-weight: bold;
        padding: 2px 6px;
        border-radius: 3px;
        background: #ddd;
        color: #333;
    }
    
    .status-success {
        background: #46b450;
        color: white;
    }
    
    .status-error {
        background: #dc3232;
        color: white;
    }
    
    .status-info {
        background: #0073aa;
        color: white;
    }
    
    .log-message {
        font-size: 13px;
        margin-bottom: 3px;
    }
    
    .log-ip {
        font-size: 11px;
        color: #666;
    }
`;
document.head.appendChild(style);
