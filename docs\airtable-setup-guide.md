# RepairLift WP Customizer - Airtable Integration Setup Guide

## Overview
This guide provides step-by-step instructions for setting up Airtable integration with the RepairLift WP Customizer plugin, enabling automated website content population through external form submissions.

## Prerequisites
- WordPress site with RepairLift WP Customizer plugin installed and activated
- Airtable account (free or paid)
- Admin access to both WordPress and Airtable
- Basic understanding of webhooks and API keys

## Table of Contents
1. [Airtable Base Setup](#1-airtable-base-setup)
2. [WordPress Plugin Configuration](#2-wordpress-plugin-configuration)
3. [Airtable Form Creation](#3-airtable-form-creation)
4. [Webhook Configuration](#4-webhook-configuration)
5. [Testing the Integration](#5-testing-the-integration)
6. [Multi-Site Deployment](#6-multi-site-deployment)
7. [Troubleshooting](#7-troubleshooting)

---

## 1. Airtable Base Setup

### Step 1.1: Create New Airtable Base
1. Log into your Airtable account
2. Click "Create a base" or use the "+" button
3. Choose "Start from scratch"
4. Name your base: "RepairLift Website Content"

### Step 1.2: Create Website Content Submissions Table
1. Rename the default table to "Website Content Submissions"
2. Add the following fields in order:

#### Core System Fields
- **Submission ID** (Auto Number) - Primary field
- **Site Identifier** (Single Line Text) - Required
- **Submission Date** (Date) - Auto-populated
- **Status** (Single Select) - Options: Pending, Processed, Failed, Rejected
- **Processing Notes** (Long Text) - For error messages or notes

#### Business Information Fields (19 fields)
- **Business Name** (Single Line Text) - Required
- **City** (Single Line Text) - Required
- **State** (Single Line Text) - Required
- **Devices Repaired** (Multiple Select) - Options: iPhone, Android, Tablet, Computer, Apple Watch, Game Console
- **Specializations** (Multiple Select) - Options: Screen Repair, Battery Replacement, Water Damage, Data Recovery, Software Issues
- **Brands Supported** (Multiple Select) - Options: Apple, Samsung, Google, LG, Motorola, OnePlus, Huawei
- **Years Experience** (Number)
- **Warranty Offered** (Single Select) - Options: 30 days, 90 days, 6 months, 1 year, Lifetime
- **Turnaround Time** (Single Select) - Options: Same day, 24 hours, 2-3 days, 1 week
- **Service Area** (Long Text)
- **Additional Services** (Multiple Select) - Options: Device Sales, Buyback Program, Accessories, Cases
- **Phone** (Phone Number)
- **Email** (Email)
- **Website** (URL)
- **Facebook** (URL)
- **Instagram** (URL)
- **Twitter** (URL)
- **Google Business** (URL)
- **Key Benefits** (Long Text)

#### Visual Blocks Content Fields (27 fields)

##### Hero Section (4 fields)
- **Hero Heading** (Single Line Text, Max 100 chars)
- **Hero Tagline** (Single Line Text, Max 80 chars)
- **Hero Button 1** (Single Line Text, Max 30 chars)
- **Hero Button 2** (Single Line Text, Max 30 chars)

##### One-Stop Shop Section (3 fields)
- **One-Stop Heading** (Single Line Text, Max 80 chars)
- **One-Stop Description** (Long Text, Max 300 chars)
- **One-Stop Button** (Single Line Text, Max 30 chars)

##### Buy Devices Section (3 fields)
- **Buy Heading** (Single Line Text, Max 80 chars)
- **Buy Description** (Long Text, Max 300 chars)
- **Buy Button** (Single Line Text, Max 30 chars)

##### Sell Devices Section (3 fields)
- **Sell Heading** (Single Line Text, Max 80 chars)
- **Sell Description** (Long Text, Max 300 chars)
- **Sell Button** (Single Line Text, Max 30 chars)

##### Call-to-Action Section (3 fields)
- **CTA Heading** (Single Line Text, Max 80 chars)
- **CTA Description** (Long Text, Max 300 chars)
- **CTA Button** (Single Line Text, Max 30 chars)

##### Contact Information (3 fields)
- **Contact Phone** (Phone Number)
- **Contact Email** (Email)
- **Contact Address** (Long Text)

##### Design System (4 fields)
- **Primary Color** (Single Line Text) - Hex color codes only
- **Secondary Color** (Single Line Text) - Hex color codes only
- **Accent Color** (Single Line Text) - Hex color codes only
- **Button Text Color** (Single Line Text) - Hex color codes only

##### Logo & Branding (1 field)
- **Logo Upload** (Attachment) - Accept PNG, JPG, SVG, WebP

### Step 1.3: Create Site Configurations Table
1. Add a new table called "Site Configurations"
2. Add these fields:
   - **Site ID** (Single Line Text) - Primary field
   - **Site Name** (Single Line Text)
   - **WordPress URL** (URL)
   - **Webhook Endpoint** (URL)
   - **API Key** (Single Line Text)
   - **Status** (Single Select) - Options: Active, Inactive, Testing
   - **Created Date** (Date)
   - **Last Updated** (Date)
   - **Notes** (Long Text)

### Step 1.4: Configure Field Validation
1. Set required fields: Business Name, City, State, Site Identifier
2. Add character limits to text fields as specified above
3. Configure color fields to accept only hex format (#RRGGBB)
4. Set file size limits for logo uploads (max 5MB)

---

## 2. WordPress Plugin Configuration

### Step 2.1: Access Airtable Integration Settings
1. Log into WordPress admin
2. Navigate to RepairLift WP Customizer
3. Click the "📋 Airtable Integration" tab

### Step 2.2: Configure Integration Settings
1. **Enable Integration**: Toggle the switch to "Enabled"
2. **API Key**: Generate a secure API key
   - Click "Generate Random API Key" for automatic generation
   - Or create your own (minimum 32 characters recommended)
   - Copy this key - you'll need it for Airtable webhook configuration
3. **Site Identifier**: Enter a unique identifier for this WordPress site
   - Format: lowercase letters, numbers, and hyphens only
   - Example: "repair-shop-miami" or "techfix-downtown"
4. **Webhook Secret** (Optional but recommended): Enter a secret for signature validation
5. **Rate Limit**: Set to 60 requests per hour (recommended for production)

### Step 2.3: Save Configuration
1. Click "Save Configuration"
2. Verify all status indicators show "Configured" or "Enabled"
3. Copy the webhook URL displayed in the "Webhook Information" section

---

## 3. Airtable Form Creation

### Step 3.1: Create Form
1. In your Airtable base, click "Create" → "Form"
2. Select "Website Content Submissions" table
3. Name the form: "Website Content Submission Form"

### Step 3.2: Configure Form Sections
Organize fields into logical sections:

1. **Site Selection**
   - Add Site Identifier field
   - Make it required
   - Add description: "Enter the unique identifier for your WordPress site"

2. **Business Information**
   - Add all 19 business info fields
   - Make Business Name, City, State required
   - Group related fields together

3. **Website Content**
   - Add all visual blocks fields (27 fields)
   - Group by section (Hero, One-Stop, Buy, Sell, CTA, Contact)
   - Add helpful descriptions for character limits

4. **Design & Branding**
   - Add design system fields (4 color fields)
   - Add logo upload field
   - Include color format instructions (#RRGGBB)

### Step 3.3: Form Customization
1. Add welcome message explaining the form purpose
2. Include character limit reminders for text fields
3. Add helpful tooltips for technical fields
4. Configure thank you message
5. Set form to allow multiple submissions

---

## 4. Webhook Configuration

### Step 4.1: Create Airtable Automation
1. In Airtable, go to "Automations" tab
2. Click "Create automation"
3. Name it: "WordPress Website Update"

### Step 4.2: Configure Trigger
1. Choose trigger: "When record created"
2. Select table: "Website Content Submissions"
3. Add condition: "Status" is "Pending" (optional)

### Step 4.3: Configure Webhook Action
1. Add action: "Send webhook"
2. Enter webhook URL from WordPress (Step 2.3)
3. Set method: POST
4. Set content type: application/json
5. Configure headers:
   ```
   X-API-Key: [Your API Key from Step 2.2]
   Content-Type: application/json
   ```

### Step 4.4: Configure Webhook Payload
Create JSON payload with all form fields:
```json
{
  "site_identifier": "{{Site Identifier}}",
  "business_name": "{{Business Name}}",
  "city": "{{City}}",
  "state": "{{State}}",
  "devices_repaired": "{{Devices Repaired}}",
  "specializations": "{{Specializations}}",
  "brands_supported": "{{Brands Supported}}",
  "years_experience": "{{Years Experience}}",
  "warranty_offered": "{{Warranty Offered}}",
  "turnaround_time": "{{Turnaround Time}}",
  "service_area": "{{Service Area}}",
  "additional_services": "{{Additional Services}}",
  "phone": "{{Phone}}",
  "email": "{{Email}}",
  "website": "{{Website}}",
  "facebook": "{{Facebook}}",
  "instagram": "{{Instagram}}",
  "twitter": "{{Twitter}}",
  "google_business": "{{Google Business}}",
  "key_benefits": "{{Key Benefits}}",
  "hero_heading": "{{Hero Heading}}",
  "hero_tagline": "{{Hero Tagline}}",
  "hero_button1": "{{Hero Button 1}}",
  "hero_button2": "{{Hero Button 2}}",
  "onestop_heading": "{{One-Stop Heading}}",
  "onestop_description": "{{One-Stop Description}}",
  "onestop_button": "{{One-Stop Button}}",
  "buy_heading": "{{Buy Heading}}",
  "buy_description": "{{Buy Description}}",
  "buy_button": "{{Buy Button}}",
  "sell_heading": "{{Sell Heading}}",
  "sell_description": "{{Sell Description}}",
  "sell_button": "{{Sell Button}}",
  "cta_heading": "{{CTA Heading}}",
  "cta_description": "{{CTA Description}}",
  "cta_button": "{{CTA Button}}",
  "contact_phone": "{{Contact Phone}}",
  "contact_email": "{{Contact Email}}",
  "contact_address": "{{Contact Address}}",
  "primary_color": "{{Primary Color}}",
  "secondary_color": "{{Secondary Color}}",
  "accent_color": "{{Accent Color}}",
  "button_text_color": "{{Button Text Color}}",
  "logo_upload": "{{Logo Upload}}"
}
```

### Step 4.5: Test Automation
1. Save the automation
2. Turn it on
3. Test with a sample form submission

---

## 5. Testing the Integration

### Step 5.1: WordPress Webhook Test
1. In WordPress Airtable Integration settings
2. Click "Test Webhook" button
3. Verify success message appears
4. Check webhook logs for test entry

### Step 5.2: End-to-End Test
1. Fill out Airtable form with test data
2. Submit the form
3. Check Airtable automation run history
4. Verify WordPress content was updated
5. Check WordPress webhook logs

### Step 5.3: Validation Tests
1. Test with missing required fields
2. Test with invalid color codes
3. Test with oversized text content
4. Test with invalid site identifier
5. Verify error handling and logging

---

## 6. Multi-Site Deployment

### Step 6.1: Site Configuration Table Setup
1. Add each WordPress site to "Site Configurations" table
2. Include unique Site ID, webhook URL, and API key for each
3. Document which site identifier corresponds to which website

### Step 6.2: Form Modification for Multi-Site
1. Convert Site Identifier field to dropdown
2. Populate options from Site Configurations table
3. Use Airtable's linked records or lookup fields

### Step 6.3: Automation Updates
1. Modify webhook automation to use dynamic URLs
2. Use lookup fields to get correct webhook URL and API key
3. Test each site configuration individually

---

## 7. Troubleshooting

### Common Issues and Solutions

#### Webhook Not Receiving Data
- Verify webhook URL is correct and accessible
- Check API key matches between Airtable and WordPress
- Ensure WordPress site allows external POST requests
- Check server firewall settings

#### Authentication Failures
- Verify API key is correctly configured in both systems
- Check for extra spaces or characters in API key
- Ensure webhook headers are properly set

#### Data Not Updating
- Check field mapping between Airtable and WordPress
- Verify required fields are provided
- Check WordPress error logs for processing errors
- Review webhook logs in WordPress admin

#### Character Limit Issues
- Verify text fields don't exceed specified limits
- Check for special characters causing encoding issues
- Ensure color codes are in proper hex format

#### Rate Limiting
- Reduce submission frequency if hitting rate limits
- Increase rate limit in WordPress settings if needed
- Check for multiple automations triggering simultaneously

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Log Locations
- WordPress webhook logs: Admin → RepairLift → Airtable Integration → Recent Webhook Activity
- WordPress error logs: `/wp-content/debug.log`
- Airtable automation logs: Automations tab → Run history

---

## Security Best Practices

1. **Use Strong API Keys**: Generate random, long API keys (32+ characters)
2. **Enable Webhook Signatures**: Configure webhook secrets for signature validation
3. **Limit Rate Limits**: Set appropriate rate limits to prevent abuse
4. **Regular Key Rotation**: Rotate API keys periodically
5. **Monitor Logs**: Regularly review webhook and security logs
6. **Backup Before Changes**: Ensure backup system is working properly

---

## Support and Maintenance

### Regular Maintenance Tasks
1. Review webhook logs monthly
2. Clean up old backups (automated after 30 days)
3. Monitor rate limit usage
4. Update API keys quarterly
5. Test integration after WordPress/plugin updates

### Getting Help
- Check webhook logs for error details
- Review Airtable automation run history
- Enable WordPress debug logging
- Contact support with specific error messages and log entries

---

This completes the comprehensive setup guide for Airtable integration with RepairLift WP Customizer.
