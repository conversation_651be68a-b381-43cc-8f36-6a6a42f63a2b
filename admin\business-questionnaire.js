/**
 * Business Questionnaire JavaScript
 * 
 * Handles the step-by-step questionnaire functionality for collecting
 * device repair store business information.
 */

jQuery(document).ready(function($) {
    let currentStep = 1;
    const totalSteps = 6;
    
    // Initialize questionnaire
    initializeQuestionnaire();
    
    function initializeQuestionnaire() {
        updateProgressIndicator();
        setupEventListeners();
        loadSavedData();
    }
    
    function setupEventListeners() {
        // Next step button
        $('#next-step').on('click', function() {
            if (validateCurrentStep()) {
                nextStep();
            }
        });
        
        // Previous step button
        $('#prev-step').on('click', function() {
            previousStep();
        });
        
        // Form submission
        $('#business-questionnaire-form').on('submit', function(e) {
            e.preventDefault();
            submitQuestionnaire();
        });
        
        // Auto-save on input change
        $('.questionnaire-form input, .questionnaire-form select, .questionnaire-form textarea').on('change', function() {
            autoSaveData();
        });
        
        // Checkbox change events
        $('.questionnaire-form input[type="checkbox"]').on('change', function() {
            autoSaveData();
            updateProgressIndicator();
        });
    }
    
    function nextStep() {
        if (currentStep < totalSteps) {
            // Hide current step
            $(`.form-step[data-step="${currentStep}"]`).removeClass('active');
            
            // Show next step
            currentStep++;
            $(`.form-step[data-step="${currentStep}"]`).addClass('active');
            
            updateProgressIndicator();
            updateNavigationButtons();
            
            // Scroll to top
            $('.business-questionnaire-container').get(0).scrollIntoView({ 
                behavior: 'smooth' 
            });
        }
    }
    
    function previousStep() {
        if (currentStep > 1) {
            // Hide current step
            $(`.form-step[data-step="${currentStep}"]`).removeClass('active');
            
            // Show previous step
            currentStep--;
            $(`.form-step[data-step="${currentStep}"]`).addClass('active');
            
            updateProgressIndicator();
            updateNavigationButtons();
            
            // Scroll to top
            $('.business-questionnaire-container').get(0).scrollIntoView({ 
                behavior: 'smooth' 
            });
        }
    }
    
    function updateProgressIndicator() {
        const progressPercentage = (currentStep / totalSteps) * 100;
        $('#progress-fill').css('width', progressPercentage + '%');
        $('#progress-text').text(`Step ${currentStep} of ${totalSteps}`);
    }
    
    function updateNavigationButtons() {
        // Previous button
        if (currentStep === 1) {
            $('#prev-step').hide();
        } else {
            $('#prev-step').show();
        }
        
        // Next/Submit button
        if (currentStep === totalSteps) {
            $('#next-step').hide();
            $('#submit-questionnaire').show();
        } else {
            $('#next-step').show();
            $('#submit-questionnaire').hide();
        }
    }
    
    function validateCurrentStep() {
        const currentStepElement = $(`.form-step[data-step="${currentStep}"]`);
        const requiredFields = currentStepElement.find('input[required], select[required]');
        let isValid = true;
        
        // Remove previous error styling
        currentStepElement.find('.error').removeClass('error');
        
        // Validate required fields
        requiredFields.each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('error');
                isValid = false;
            }
        });
        
        // Step-specific validations
        if (currentStep === 1) {
            // Basic business information validation
            const phone = $('#phone').val();
            const email = $('#email').val();
            
            if (phone && !isValidPhone(phone)) {
                $('#phone').addClass('error');
                isValid = false;
            }
            
            if (email && !isValidEmail(email)) {
                $('#email').addClass('error');
                isValid = false;
            }
        } else if (currentStep === 2) {
            // Services validation - at least one service must be selected
            const selectedServices = currentStepElement.find('input[name="services[]"]:checked');
            if (selectedServices.length === 0) {
                showNotice('Please select at least one device repair service.', 'warning');
                isValid = false;
            }
        } else if (currentStep === 3) {
            // Characteristics validation - at least one characteristic
            const selectedCharacteristics = currentStepElement.find('input[name="characteristics[]"]:checked');
            if (selectedCharacteristics.length === 0) {
                showNotice('Please select at least one characteristic that makes your store special.', 'warning');
                isValid = false;
            }
        }
        
        if (!isValid) {
            showNotice('Please fill in all required fields correctly.', 'error');
        }
        
        return isValid;
    }
    
    function isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
        return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
    }
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function autoSaveData() {
        const formData = getFormData();
        localStorage.setItem('business_questionnaire_data', JSON.stringify(formData));
    }
    
    function loadSavedData() {
        const savedData = localStorage.getItem('business_questionnaire_data');
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                populateForm(data);
            } catch (e) {
                console.error('Error loading saved data:', e);
            }
        }
    }
    
    function getFormData() {
        const formData = {};
        
        // Get all form inputs
        $('#business-questionnaire-form input, #business-questionnaire-form select, #business-questionnaire-form textarea').each(function() {
            const $field = $(this);
            const name = $field.attr('name');
            const type = $field.attr('type');
            
            if (name) {
                if (type === 'checkbox') {
                    if (!formData[name]) {
                        formData[name] = [];
                    }
                    if ($field.is(':checked')) {
                        formData[name].push($field.val());
                    }
                } else if (type === 'radio') {
                    if ($field.is(':checked')) {
                        formData[name] = $field.val();
                    }
                } else {
                    formData[name] = $field.val();
                }
            }
        });
        
        return formData;
    }
    
    function populateForm(data) {
        Object.keys(data).forEach(function(name) {
            const value = data[name];
            const $field = $(`[name="${name}"]`);
            
            if ($field.length) {
                if ($field.attr('type') === 'checkbox') {
                    $field.prop('checked', false); // Reset all checkboxes first
                    if (Array.isArray(value)) {
                        value.forEach(function(val) {
                            $(`[name="${name}"][value="${val}"]`).prop('checked', true);
                        });
                    }
                } else if ($field.attr('type') === 'radio') {
                    $(`[name="${name}"][value="${value}"]`).prop('checked', true);
                } else {
                    $field.val(value);
                }
            }
        });
    }
    
    function submitQuestionnaire() {
        if (!validateCurrentStep()) {
            return;
        }
        
        const formData = getFormData();
        
        // Show loading state
        $('#submit-questionnaire').prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Saving...');
        
        // Submit to server
        $.ajax({
            url: website_generator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'save_business_questionnaire',
                nonce: website_generator_ajax.nonce,
                questionnaire_data: formData
            },
            success: function(response) {
                if (response.success) {
                    showNotice('Business information saved successfully! AI can now generate personalized content.', 'success');
                    
                    // Clear saved data
                    localStorage.removeItem('business_questionnaire_data');
                    
                    // Redirect to homepage tab or show completion message
                    setTimeout(function() {
                        $('.nav-tab[data-tab="homepage"]').click();
                    }, 2000);
                } else {
                    showNotice('Error saving business information: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('Network error. Please try again.', 'error');
            },
            complete: function() {
                $('#submit-questionnaire').prop('disabled', false).html('<span class="dashicons dashicons-yes"></span> Complete Setup');
            }
        });
    }
    
    function showNotice(message, type) {
        // Remove existing notices
        $('.questionnaire-notice').remove();
        
        // Create notice
        const noticeClass = type === 'error' ? 'notice-error' : type === 'warning' ? 'notice-warning' : 'notice-success';
        const notice = $(`
            <div class="notice ${noticeClass} questionnaire-notice" style="margin: 20px 0; padding: 12px; border-left: 4px solid; border-radius: 4px;">
                <p style="margin: 0;">${message}</p>
            </div>
        `);
        
        // Insert notice at the top of current step
        $(`.form-step[data-step="${currentStep}"] .step-header`).after(notice);
        
        // Auto-remove success notices
        if (type === 'success') {
            setTimeout(function() {
                notice.fadeOut();
            }, 5000);
        }
        
        // Scroll to notice
        notice.get(0).scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    
    // Add error styling for validation
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .error {
                border-color: #d63638 !important;
                box-shadow: 0 0 0 3px rgba(214, 54, 56, 0.1) !important;
            }
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `)
        .appendTo('head');
});
