// ✅ UPDATED AIRTABLE AUTOMATION SCRIPT - STANDARDIZED FIELD NAMES
// This script uses the new standardized field names (hero_title, hero_tagline)
// No more field mapping complexity - direct 1:1 relationship

// Get the record that triggered this automation
let inputConfig = input.config();
let record = inputConfig.record;

console.log('🔍 Record object type:', typeof record);
console.log('🔍 Record methods available:', Object.getOwnPropertyNames(record));

// Initialize variables with standardized field names
let heroTitle = '';
let heroTagline = '';
let siteIdentifier = 'example-site';

// Extract field values using multiple access methods
if (record) {
    // Method 1: getCellValue (preferred)
    if (typeof record.getCellValue === 'function') {
        heroTitle = record.getCellValue('hero_title') || '';
        heroTagline = record.getCellValue('hero_tagline') || '';
        siteIdentifier = record.getCellValue('Site Identifier') || 'example-site';
    }
    // Method 2: Direct field access (fallback)
    else if (record.fields) {
        heroTitle = record.fields['hero_title'] || '';
        heroTagline = record.fields['hero_tagline'] || '';
        siteIdentifier = record.fields['Site Identifier'] || 'example-site';
    }
}

console.log('✅ Extracted values:');
console.log('  Hero Title:', heroTitle);
console.log('  Hero Tagline:', heroTagline);
console.log('  Site Identifier:', siteIdentifier);

// Webhook configuration
const webhookUrl = 'https://your-wordpress-site.com/wp-json/website-generator/v1/webhook';
const apiKey = 'your-api-key-here';

// Build payload with standardized field names (no mapping needed)
const payload = {
    api_key: apiKey,
    site_identifier: siteIdentifier,
    hero_title: heroTitle,        // Direct field name - no conversion
    hero_tagline: heroTagline     // Direct field name - no conversion
};

console.log('📤 Sending payload:', JSON.stringify(payload, null, 2));

// Send webhook request
try {
    const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
    });

    const responseText = await response.text();
    console.log('📥 Response status:', response.status);
    console.log('📥 Response text:', responseText);

    if (response.ok) {
        console.log('✅ Webhook sent successfully!');
        console.log('🎯 Updated fields:');
        console.log('   - hero_title:', heroTitle);
        console.log('   - hero_tagline:', heroTagline);
    } else {
        console.log('❌ Webhook failed with status:', response.status);
        console.log('❌ Error response:', responseText);
    }
} catch (error) {
    console.log('❌ Error sending webhook:', error.message);
}

// ✅ BENEFITS OF STANDARDIZED APPROACH:
// 1. No field mapping complexity
// 2. Direct 1:1 relationship: Airtable → Plugin → Website
// 3. Easier to maintain and debug
// 4. Scales better for new fields
// 5. Eliminates mapping errors
