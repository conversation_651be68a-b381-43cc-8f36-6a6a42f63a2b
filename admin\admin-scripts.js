jQuery(document).ready(function($) {
    console.log('Website Generator Admin Scripts Loaded');
    console.log('AJAX Object:', ajax_object);

    // Tab Navigation
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();

        // Remove active class from all tabs and content
        $('.nav-tab').removeClass('nav-tab-active');
        $('.tab-content').removeClass('active');

        // Add active class to clicked tab
        $(this).addClass('nav-tab-active');

        // Show corresponding content
        var tabId = $(this).data('tab') + '-tab';
        $('#' + tabId).addClass('active');
    });

    // Visual Block Editor Functions
    function initializeVisualEditor() {
        // Load current content into visual blocks
        $('#load-current-content').on('click', function() {
            loadCurrentContent();
        });

        // Real-time preview updates for visual blocks
        $('.block-input').on('input', function() {
            updateBlockPreview($(this));
            updateBlockStatus($(this).closest('.visual-block'), 'modified');
        });

        // Apply all changes (Safe Mode)
        $('#apply-all-changes').on('click', function() {
            applyAllChanges();
        });

        // Revert all changes
        $('#revert-all-changes').on('click', function() {
            if (confirm('Are you sure you want to revert all design changes? This will restore the original website appearance.')) {
                revertAllChanges();
            }
        });

        // Preview all changes
        $('#preview-all-changes').on('click', function() {
            previewAllChanges();
        });
    }

    function loadCurrentContent() {
        showLoading();

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'get_current_content',
                nonce: ajax_object.nonce
            },
            success: function(response) {
                console.log('🔄 AJAX Response received:', response);
                console.log('📦 Response data:', response.data);

                if (response.success) {
                    console.log('✅ AJAX Success - calling populateVisualBlocks with:', response.data);
                    populateVisualBlocks(response.data);
                    showMessage('Current content loaded successfully', 'success');
                } else {
                    console.log('❌ AJAX Error:', response.data);
                    showMessage('Failed to load content: ' + response.data, 'error');
                }
            },
            error: function() {
                showMessage('Error loading content', 'error');
            },
            complete: function() {
                hideLoading();
            }
        });
    }

    function populateVisualBlocks(data) {
        // Populate hero section
        if (data.hero) {
            $('#hero_heading').val(data.hero.heading || '');
            $('#hero_tagline').val(data.hero.tagline || '');
            $('#hero_button1').val(data.hero.button1 || '');
            $('#hero_button2').val(data.hero.button2 || '');
            updateBlockPreview($('#hero_heading'));
        }

        // Populate other sections
        if (data.onestop) {
            $('#onestop_heading').val(data.onestop.heading || '');
            $('#onestop_description').val(data.onestop.description || '');
            $('#onestop_button').val(data.onestop.button || '');
            updateBlockPreview($('#onestop_heading'));
        }

        // Populate design system
        if (data.design) {
            console.log('🎨 Design system data received:', data.design);
            console.log('🔵 Primary color:', data.design.primary_color);
            console.log('🔴 Secondary color:', data.design.secondary_color);
            console.log('⚪ Accent color:', data.design.accent_color);
            console.log('📝 Button text color:', data.design.button_text_color);

            $('#primary_color').val(data.design.primary_color || '#165C9C');
            $('#secondary_color').val(data.design.secondary_color || '#111111');
            $('#accent_color').val(data.design.accent_color || '#FFFFFF');
            $('#button_text_color').val(data.design.button_text_color || '#FFFFFF');
            $('#button_style').val(data.design.button_style || 'pill');
            $('#button_size').val(data.design.button_size || 'medium');

            // Update color value displays
            $('.color-value').each(function() {
                var input = $(this).siblings('input[type="color"]');
                if (input.length) {
                    $(this).text(input.val());
                }
            });

            updateDesignPreview();
            console.log('✅ Design system populated and preview updated');
        } else {
            console.log('❌ No design system data received');
        }

        // Populate contact info
        if (data.contact) {
            $('#contact_phone').val(data.contact.phone || '');
            $('#contact_email').val(data.contact.email || '');
            $('#contact_address').val(data.contact.address || '');
            updateBlockPreview($('#contact_phone'));
        }

        // Populate logo info
        if (data.logo && data.logo.has_logo) {
            populateLogoInfo(data.logo);
        }

        updateAllBlockStatuses('unchanged');

        // Colors are now detected server-side and included in the response
        console.log('✅ Content loaded with accurate colors from server-side detection');
        console.log('🕐 Content loaded at:', new Date().toLocaleTimeString());
    }

    function detectActualWebsiteColors() {
        console.log('Starting color detection...');

        // Open the website in a new window/iframe to detect colors
        var websiteUrl = window.location.origin;
        console.log('Website URL:', websiteUrl);

        // Create a hidden iframe to load the website
        var iframe = $('<iframe>')
            .attr('src', websiteUrl)
            .css({
                'position': 'absolute',
                'left': '-9999px',
                'width': '1px',
                'height': '1px',
                'visibility': 'hidden'
            })
            .appendTo('body');

        iframe.on('load', function() {
            console.log('Iframe loaded successfully');
            try {
                var iframeDoc = iframe[0].contentDocument || iframe[0].contentWindow.document;
                console.log('Iframe document accessed:', !!iframeDoc);

                // Find buttons and get their computed styles
                var buttons = $(iframeDoc).find('a[href*="repair"], a[href*="quote"], a[href*="location"], a[href*="buy"], a[href*="sell"]');
                console.log('Found buttons:', buttons.length);

                var detectedColors = {
                    primary: '#165C9C',
                    secondary: '#6AA7E0',
                    tertiary: '#F7F7F7',
                    button_text: '#FFFFFF'
                };

                if (buttons.length > 0) {
                    var firstButton = buttons.first()[0];
                    var computedStyle = iframe[0].contentWindow.getComputedStyle(firstButton);

                    console.log('Button background color:', computedStyle.backgroundColor);
                    console.log('Button text color:', computedStyle.color);

                    detectedColors.primary = rgbToHex(computedStyle.backgroundColor);
                    detectedColors.button_text = rgbToHex(computedStyle.color);
                }

                console.log('Detected colors:', detectedColors);

                // Send detected colors to backend
                $.ajax({
                    url: ajax_object.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'detect_website_colors',
                        nonce: ajax_object.nonce,
                        primary_color: detectedColors.primary,
                        secondary_color: detectedColors.secondary,
                        tertiary_color: detectedColors.tertiary,
                        button_text_color: detectedColors.button_text
                    },
                    success: function(response) {
                        if (response.success) {
                            console.log('Colors detected successfully:', response.data.colors);
                            // Update the design system with detected colors
                            updateDesignSystemWithDetectedColors(response.data.colors);
                        }
                    }
                });

            } catch (e) {
                console.log('Could not access iframe content due to CORS policy. Using fallback colors.', e);

                // Since iframe approach failed, let's use our known correct colors
                var detectedColors = {
                    primary: '#165C9C',
                    secondary: '#6AA7E0',
                    tertiary: '#F7F7F7',
                    button_text: '#FFFFFF'
                };

                // Send the fallback colors to backend
                $.ajax({
                    url: ajax_object.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'detect_website_colors',
                        nonce: ajax_object.nonce,
                        primary_color: detectedColors.primary,
                        secondary_color: detectedColors.secondary,
                        tertiary_color: detectedColors.tertiary,
                        button_text_color: detectedColors.button_text
                    },
                    success: function(response) {
                        if (response.success) {
                            console.log('Fallback colors sent successfully:', response.data.colors);
                        }
                    }
                });
            }

            // Remove the iframe
            iframe.remove();
        });
    }

    function updateDesignSystemWithDetectedColors(colors) {
        $('#primary_color').val(colors.primary);
        $('#secondary_color').val(colors.secondary);
        $('#accent_color').val(colors.tertiary);
        $('#button_text_color').val(colors.button_text);

        // Update color value displays
        $('#primary_color').siblings('.color-value').text(colors.primary);
        $('#secondary_color').siblings('.color-value').text(colors.secondary);
        $('#accent_color').siblings('.color-value').text(colors.tertiary);
        $('#button_text_color').siblings('.color-value').text(colors.button_text);

        updateDesignPreview();
    }

    function rgbToHex(rgb) {
        if (rgb.startsWith('#')) return rgb;

        var result = rgb.match(/\d+/g);
        if (!result || result.length < 3) return '#000000';

        return '#' + result.slice(0, 3).map(function(x) {
            var hex = parseInt(x).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        }).join('');
    }

    function updateBlockPreview(input) {
        var previewId = input.data('preview');
        var value = input.val();

        if (previewId && value) {
            if (previewId.includes('color')) {
                updateColorPreview(previewId, value);
            } else if (previewId === 'button-style' || previewId === 'button-size') {
                updateButtonPreview();
            } else {
                $('#preview-' + previewId).text(value);
            }
        }
    }

    function updateColorPreview(previewId, color) {
        // Update color swatch
        $('#preview-' + previewId).css('background-color', color);

        // Update color value display
        var colorInput = $('#' + previewId.replace('-', '_'));
        colorInput.siblings('.color-value').text(color.toUpperCase());

        // Update button colors if primary, secondary, or text color changed
        if (previewId === 'primary-color') {
            $('.preview-btn.primary').css('background-color', color);
            updateAllButtonColors();
        } else if (previewId === 'secondary-color') {
            $('.preview-btn.secondary').css('background-color', color);
            updateAllButtonColors();
        } else if (previewId === 'button-text-color') {
            $('.preview-btn').css('color', color);
            updateAllButtonColors();
        }
    }

    function updateButtonPreview() {
        var style = $('#button_style').val();
        var size = $('#button_size').val();
        var shadow = $('#button_shadow').val();
        var hover = $('#button_hover').val();
        var primaryColor = $('#primary_color').val();
        var buttonTextColor = $('#button_text_color').val();

        // Remove existing classes
        $('.preview-btn').removeClass('pill rounded square small medium large shadow-none shadow-subtle shadow-medium shadow-strong hover-lift hover-scale hover-glow hover-slide');

        // Add new classes
        $('.preview-btn').addClass(style + ' ' + size + ' shadow-' + shadow + ' hover-' + hover);

        // Update button colors
        $('.preview-btn').css({
            'background-color': primaryColor,
            'color': buttonTextColor
        });
    }

    function updateTypographyPreview() {
        var fontPair = $('#font_pair').val();
        var headingSize = $('#heading_size').val();
        var bodySize = $('#body_size').val();

        // Remove existing typography classes
        $('.typography-preview').removeClass('font-modern font-classic font-tech font-friendly heading-small heading-medium heading-large body-14 body-16 body-18');

        // Add new typography classes
        $('.typography-preview').addClass('font-' + fontPair + ' heading-' + headingSize + ' body-' + bodySize);
    }

    function updateSpacingPreview() {
        var sectionSpacing = $('#section_spacing').val();
        var containerWidth = $('#container_width').val();

        // Update preview container classes
        $('.preview-container').removeClass('spacing-compact spacing-normal spacing-spacious width-narrow width-normal width-wide');
        $('.preview-container').addClass('spacing-' + sectionSpacing + ' width-' + containerWidth);
    }

    function updateAnimationPreview() {
        var heroAnimation = $('#hero_animation').val();
        var imageAnimation = $('#image_animation').val();
        var speed = $('#animation_speed').val();
        var delay = $('#animation_delay').val();

        // Store animation settings for replay
        window.currentAnimations = {
            hero: heroAnimation,
            image: imageAnimation,
            speed: speed,
            delay: delay
        };

        // Apply animations immediately
        applyAnimations();
    }

    function applyAnimations() {
        if (!window.currentAnimations) return;

        var settings = window.currentAnimations;
        var heroElement = $('#preview-hero-animation');
        var imageElement = $('#preview-image-animation');

        // Remove existing animation classes
        heroElement.removeClass('animate-fadeInUp animate-fadeInLeft animate-scaleIn animate-slideInRight animate-fast animate-normal animate-slow animate-delay-short animate-delay-medium animate-delay-long');
        imageElement.removeClass('animate-fadeIn animate-scaleIn animate-rotateIn animate-pulse animate-fast animate-normal animate-slow animate-delay-short animate-delay-medium animate-delay-long');

        // Apply hero animation
        if (settings.hero !== 'none') {
            heroElement.addClass('animate-' + settings.hero + ' animate-' + settings.speed);
            if (settings.delay !== 'none') {
                heroElement.addClass('animate-delay-' + settings.delay);
            }
        }

        // Apply image animation with slight delay
        if (settings.image !== 'none') {
            setTimeout(function() {
                imageElement.addClass('animate-' + settings.image + ' animate-' + settings.speed);
            }, settings.delay === 'none' ? 200 : 400);
        }
    }

    function replayAnimations() {
        // Reset animations by removing and re-adding classes
        var heroElement = $('#preview-hero-animation');
        var imageElement = $('#preview-image-animation');

        // Remove all animation classes
        heroElement.removeClass('animate-fadeInUp animate-fadeInLeft animate-scaleIn animate-slideInRight animate-fast animate-normal animate-slow animate-delay-short animate-delay-medium animate-delay-long');
        imageElement.removeClass('animate-fadeIn animate-scaleIn animate-rotateIn animate-pulse animate-fast animate-normal animate-slow animate-delay-short animate-delay-medium animate-delay-long');

        // Force reflow
        heroElement[0].offsetHeight;
        imageElement[0].offsetHeight;

        // Reapply animations
        setTimeout(function() {
            applyAnimations();
        }, 50);
    }

    function updateDesignPreview() {
        // Update all color previews
        updateColorPreview('primary-color', $('#primary_color').val());
        updateColorPreview('secondary-color', $('#secondary_color').val());
        updateColorPreview('accent-color', $('#accent_color').val());
        updateColorPreview('button-text-color', $('#button_text_color').val());

        // Update all design system previews
        updateButtonPreview();
        updateTypographyPreview();
        updateSpacingPreview();
        updateAnimationPreview();
    }

    function updateAllButtonColors() {
        var primaryColor = $('#primary_color').val();
        var secondaryColor = $('#secondary_color').val();
        var buttonTextColor = $('#button_text_color').val();

        // Update all primary buttons (background + text color)
        $('.preview-btn.primary').css({
            'background-color': primaryColor,
            'color': buttonTextColor
        });

        // Update all secondary buttons (background + text color)
        $('.preview-btn.secondary').css({
            'background-color': secondaryColor,
            'color': buttonTextColor
        });

        // Update buttons in other blocks
        $('.preview-buttons .preview-btn.primary').css({
            'background-color': primaryColor,
            'color': buttonTextColor
        });
        $('.preview-buttons .preview-btn.secondary').css({
            'background-color': secondaryColor,
            'color': buttonTextColor
        });
    }

    function updateBlockStatus(block, status) {
        block.find('.status-indicator').attr('data-status', status);

        var statusText = {
            'unchanged': 'Unchanged',
            'modified': 'Modified',
            'saved': 'Saved'
        };

        block.find('.status-indicator').text(statusText[status] || status);
    }

    function updateAllBlockStatuses(status) {
        $('.visual-block').each(function() {
            updateBlockStatus($(this), status);
        });
    }

    function applyAllChanges() {
        var blockData = collectBlockData();

        showLoading();

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'apply_visual_changes',
                nonce: ajax_object.nonce,
                blocks: blockData
            },
            success: function(response) {
                if (response.success) {
                    updateAllBlockStatuses('saved');
                    showMessage('All changes applied successfully!', 'success');
                } else {
                    showMessage('Failed to apply changes: ' + response.data, 'error');
                }
            },
            error: function() {
                showMessage('Error applying changes', 'error');
            },
            complete: function() {
                hideLoading();
            }
        });
    }

    function collectBlockData() {
        var data = {};

        // Collect hero data
        data.hero = {
            heading: $('#hero_heading').val(),
            tagline: $('#hero_tagline').val(),
            button1: $('#hero_button1').val(),
            button2: $('#hero_button2').val()
        };

        // Collect onestop data
        data.onestop = {
            heading: $('#onestop_heading').val(),
            description: $('#onestop_description').val(),
            button: $('#onestop_button').val()
        };

        // Collect contact data
        data.contact = {
            phone: $('#contact_phone').val(),
            email: $('#contact_email').val(),
            address: $('#contact_address').val()
        };

        // Collect design system data
        data.design = {
            // Colors
            primary_color: $('#primary_color').val(),
            secondary_color: $('#secondary_color').val(),
            accent_color: $('#accent_color').val(),

            // Buttons
            button_style: $('#button_style').val(),
            button_size: $('#button_size').val(),
            button_shadow: $('#button_shadow').val(),
            button_hover: $('#button_hover').val(),

            // Typography
            font_pair: $('#font_pair').val(),
            heading_size: $('#heading_size').val(),
            body_size: $('#body_size').val(),

            // Spacing
            section_spacing: $('#section_spacing').val(),
            container_width: $('#container_width').val(),

            // Animations
            hero_animation: $('#hero_animation').val(),
            image_animation: $('#image_animation').val(),
            animation_speed: $('#animation_speed').val(),
            animation_delay: $('#animation_delay').val()
        };

        return data;
    }

    // Logo Editor Functions
    function initializeLogoEditor() {
        var logoEditor = {
            canvas: null,
            ctx: null,
            originalImage: null,
            currentImage: null,
            currentFilter: 'none',
            adjustments: {
                brightness: 0,
                contrast: 0,
                saturation: 0,
                hue: 0
            }
        };

        // Logo upload handler
        $('#logo_upload').on('change', function(e) {
            var file = e.target.files[0];
            if (file) {
                handleLogoUpload(file, logoEditor);
            }
        });

        // Restore previous logo handler
        $('#restore-previous-logo').on('click', function() {
            if (confirm('Are you sure you want to restore the previous logo? This will replace the current logo.')) {
                restorePreviousLogo();
            }
        });

        // Remove current logo handler
        $('#remove-current-logo').on('click', function() {
            if (confirm('Are you sure you want to remove the current logo?')) {
                removeCurrentLogo();
            }
        });

        // Drag and drop
        var uploadContainer = $('.logo-upload-container');
        uploadContainer.on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('dragover');
        });

        uploadContainer.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
        });

        uploadContainer.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
            var files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                handleLogoUpload(files[0], logoEditor);
            }
        });

        // Editing tool buttons
        $('.edit-btn').on('click', function() {
            var tool = $(this).data('tool');
            $('.edit-btn').removeClass('active');
            $(this).addClass('active');
            $('.editing-panel').hide();
            $('#' + tool + '-panel').show();
        });

        // Size preset buttons
        $('.preset-btn').on('click', function() {
            var size = $(this).data('size').split(',');
            $('#logo_width').val(size[0]);
            $('#logo_height').val(size[1]);
        });

        // Adjustment sliders
        $('.adjustment-slider').on('input', function() {
            var value = $(this).val();
            var type = $(this).attr('id');
            $(this).siblings('.slider-value').text(type === 'hue' ? value + '°' : value);
            logoEditor.adjustments[type] = parseInt(value);
            applyAdjustments(logoEditor);
        });

        // Filter buttons
        $('.filter-btn').on('click', function() {
            $('.filter-btn').removeClass('active');
            $(this).addClass('active');
            logoEditor.currentFilter = $(this).data('filter');
            applyFilter(logoEditor);
        });

        // Apply buttons
        $('.apply-btn').on('click', function() {
            var action = $(this).data('action');
            handleApplyAction(action, logoEditor);
        });

        // Reset button
        $('.reset-btn').on('click', function() {
            resetAdjustments(logoEditor);
        });

        return logoEditor;
    }

    function handleLogoUpload(file, editor) {
        // Validate file
        if (!validateLogoFile(file)) {
            return;
        }

        // Show upload progress
        showUploadProgress('Processing logo upload...');

        // Use enhanced upload with WebP conversion
        uploadLogoEnhanced(file, editor);
    }

    function validateLogoFile(file) {
        if (!file.type.match(/^image\/(png|jpeg|jpg|svg\+xml|webp)$/)) {
            alert('Please upload a valid image file (PNG, JPG, SVG, WebP)');
            return false;
        }

        if (file.size > 10 * 1024 * 1024) { // Increased to 10MB limit for high-quality logos
            alert('File size must be less than 10MB');
            return false;
        }

        return true;
    }

    function uploadLogoEnhanced(file, editor) {
        // Check if ajax_object is available
        if (typeof ajax_object === 'undefined') {
            console.error('ajax_object is not defined');
            alert('AJAX configuration error. Please refresh the page and try again.');
            return;
        }

        // Create FormData for enhanced upload
        var formData = new FormData();
        formData.append('logo_upload', file);
        formData.append('action', 'upload_logo_enhanced');
        formData.append('nonce', ajax_object.nonce);

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    handleUploadSuccess(response.data, file, editor);
                } else {
                    alert('Upload failed: ' + response.data);
                }
            },
            error: function() {
                alert('Upload failed. Please try again.');
            },
            complete: function() {
                hideUploadProgress();
            }
        });
    }

    function handleUploadSuccess(data, file, editor) {
        // Create image for canvas setup
        var img = new Image();
        img.onload = function() {
            // Validate dimensions - more flexible requirements
            if (img.width < 32 || img.height < 32) {
                alert('Image must be at least 32x32 pixels for proper display');
                return;
            }

            // Check for reasonable maximum dimensions
            if (img.width > 5000 || img.height > 5000) {
                alert('Image dimensions are too large. Please use an image smaller than 5000x5000 pixels');
                return;
            }

            // Setup canvas
            setupCanvas(img, editor);

            // Update preview with WebP support
            updateLogoPreviewEnhanced(data);

            // Show conversion results
            showConversionResults(data);

            // Show editing tools
            $('#logo-editing-tools').show();

            // Mark as modified
            updateBlockStatus($('.visual-block[data-block="logo"]'), 'modified');
        };

        // Use WebP if available and supported, otherwise original
        if (supportsWebP() && data.webp) {
            img.src = data.webp.url;
        } else {
            img.src = data.url;
        }
    }

    function showUploadProgress(message) {
        var progressHtml = '<div id="upload-progress" class="upload-progress">';
        progressHtml += '<div class="progress-spinner"></div>';
        progressHtml += '<p>' + message + '</p>';
        progressHtml += '</div>';

        $('.logo-upload-container').append(progressHtml);
    }

    function hideUploadProgress() {
        $('#upload-progress').remove();
    }

    function showConversionResults(data) {
        var resultsHtml = '<div class="upload-results">';
        resultsHtml += '<h4>✅ Upload Successful</h4>';

        if (data.webp) {
            resultsHtml += '<div class="webp-results">';
            resultsHtml += '<p class="success">🎉 WebP conversion saved ' + data.webp.compression_saved + '% file size!</p>';
            resultsHtml += '<p class="info">📱 Modern browsers will load the optimized WebP version</p>';
            resultsHtml += '<p class="info">🔄 Older browsers will use the original as fallback</p>';
            resultsHtml += '<div class="size-comparison">';
            resultsHtml += '<span>Original: ' + formatFileSize(data.webp.original_size) + '</span>';
            resultsHtml += '<span>WebP: ' + formatFileSize(data.webp.file_size) + '</span>';
            resultsHtml += '</div>';
            resultsHtml += '</div>';
        } else {
            resultsHtml += '<p class="info">📁 Logo uploaded successfully</p>';
        }

        resultsHtml += '</div>';

        $('#upload-results').html(resultsHtml).show();

        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('#upload-results').fadeOut();
        }, 5000);
    }

    function updateLogoPreviewEnhanced(data) {
        var logoImg = $('#preview-logo');

        // Use WebP if supported, otherwise original
        if (supportsWebP() && data.webp) {
            logoImg.attr('src', data.webp.url);
            logoImg.attr('data-fallback', data.url);
        } else {
            logoImg.attr('src', data.url);
        }

        logoImg.show();
        $('.logo-placeholder').hide();

        // Update logo info
        $('#logo-filename').text(data.url.split('/').pop());
        $('#logo-dimensions').text(data.width + ' × ' + data.height + ' px');
    }

    // Browser WebP support detection
    function supportsWebP() {
        if (typeof window.webpSupport !== 'undefined') {
            return window.webpSupport;
        }

        var canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        window.webpSupport = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
        return window.webpSupport;
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function setupCanvas(img, editor) {
        editor.canvas = document.getElementById('logo-edit-canvas');
        editor.ctx = editor.canvas.getContext('2d');
        editor.originalImage = img;
        editor.currentImage = img;

        // Set canvas size
        var maxWidth = 400;
        var maxHeight = 300;
        var ratio = Math.min(maxWidth / img.width, maxHeight / img.height);

        editor.canvas.width = img.width * ratio;
        editor.canvas.height = img.height * ratio;

        // Draw image
        editor.ctx.drawImage(img, 0, 0, editor.canvas.width, editor.canvas.height);

        // Show canvas
        $(editor.canvas).show();
    }

    function updateLogoPreview(img, file) {
        // Update preview image
        $('#preview-logo').attr('src', img.src).show();
        $('.logo-placeholder').hide();

        // Update info
        $('#logo-filename').text(file.name);
        $('#logo-dimensions').text(img.width + ' × ' + img.height + ' px');
        $('#logo-filesize').text(formatFileSize(file.size));
    }

    function applyAdjustments(editor) {
        if (!editor.canvas || !editor.originalImage) return;

        var adj = editor.adjustments;
        var filters = [];

        if (adj.brightness !== 0) {
            filters.push('brightness(' + (100 + adj.brightness) + '%)');
        }
        if (adj.contrast !== 0) {
            filters.push('contrast(' + (100 + adj.contrast) + '%)');
        }
        if (adj.saturation !== 0) {
            filters.push('saturate(' + (100 + adj.saturation) + '%)');
        }
        if (adj.hue !== 0) {
            filters.push('hue-rotate(' + adj.hue + 'deg)');
        }

        editor.canvas.style.filter = filters.join(' ');
    }

    function applyFilter(editor) {
        if (!editor.canvas) return;

        var filterMap = {
            'none': '',
            'grayscale': 'grayscale(100%)',
            'sepia': 'sepia(100%)',
            'high-contrast': 'contrast(150%) brightness(110%)',
            'vintage': 'sepia(50%) contrast(120%) brightness(90%)',
            'modern': 'contrast(110%) saturate(120%) brightness(105%)'
        };

        editor.canvas.style.filter = filterMap[editor.currentFilter] || '';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function handleApplyAction(action, editor) {
        switch(action) {
            case 'apply-crop':
                // Implement cropping logic
                console.log('Apply crop');
                break;
            case 'apply-resize':
                // Implement resize logic
                console.log('Apply resize');
                break;
            case 'apply-adjustments':
                // Implement adjustment application
                console.log('Apply adjustments');
                break;
            case 'apply-filter':
                // Implement filter application
                console.log('Apply filter');
                break;
        }
    }

    function resetAdjustments(editor) {
        editor.adjustments = { brightness: 0, contrast: 0, saturation: 0, hue: 0 };
        $('.adjustment-slider').val(0);
        $('.slider-value').each(function() {
            var slider = $(this).siblings('.adjustment-slider');
            var type = slider.attr('id');
            $(this).text(type === 'hue' ? '0°' : '0');
        });
        if (editor.canvas) {
            editor.canvas.style.filter = '';
        }
    }

    function populateLogoInfo(logoData) {
        // Update preview image
        $('#preview-logo').attr('src', logoData.url).show();
        $('.logo-placeholder').hide();

        // Update info
        $('#logo-filename').text(logoData.filename);
        $('#logo-dimensions').text(logoData.width + ' × ' + logoData.height + ' px');
        $('#logo-filesize').text(formatFileSize(logoData.file_size));

        // Show editing tools if logo exists
        if (logoData.has_logo) {
            $('#logo-editing-tools').show();
        }
    }

    function restorePreviousLogo() {
        showLoading();

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'restore_previous_logo',
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage('Previous logo restored successfully!', 'success');
                    // Reload current content to update the logo display
                    loadCurrentContent();
                } else {
                    showMessage('Failed to restore previous logo: ' + response.data, 'error');
                }
            },
            error: function() {
                showMessage('Error restoring previous logo', 'error');
            },
            complete: function() {
                hideLoading();
            }
        });
    }

    function removeCurrentLogo() {
        showLoading();

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'remove_current_logo',
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.success) {
                    showMessage('Logo removed successfully!', 'success');
                    // Clear the logo preview
                    $('#preview-logo').hide();
                    $('.logo-placeholder').show();
                    $('#logo-filename').text('None');
                    $('#logo-dimensions').text('-');
                    $('#logo-filesize').text('-');
                    $('#logo-editing-tools').hide();
                    updateBlockStatus($('.visual-block[data-block="logo"]'), 'modified');
                } else {
                    showMessage('Failed to remove logo: ' + response.data, 'error');
                }
            },
            error: function() {
                showMessage('Error removing logo', 'error');
            },
            complete: function() {
                hideLoading();
            }
        });
    }

    function showLoading() {
        $('#loading-overlay').show();
    }

    function hideLoading() {
        $('#loading-overlay').hide();
    }

    function showMessage(message, type) {
        var alertClass = 'notice-' + (type === 'error' ? 'error' : 'success');
        var html = '<div class="notice ' + alertClass + ' is-dismissible"><p>' + message + '</p></div>';

        // Remove existing notices
        $('.notice').remove();

        // Add new notice
        $('#website-generator-container').prepend(html);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $('.notice').fadeOut();
        }, 5000);
    }

    // Initialize visual editor
    initializeVisualEditor();

    // Logo Upload and Editing
    initializeLogoEditor();

    // Design System Event Handlers
    $('#primary_color, #secondary_color, #accent_color').on('input change', function() {
        var previewId = $(this).attr('id').replace('_', '-');
        updateColorPreview(previewId, $(this).val());
        updateBlockStatus($(this).closest('.visual-block'), 'modified');
    });

    $('#button_text_color').on('input change', function() {
        updateButtonPreview();
        updateBlockStatus($(this).closest('.visual-block'), 'modified');
        // Update color value display
        $(this).siblings('.color-value').text($(this).val());
    });

    $('#button_style, #button_size, #button_shadow, #button_hover').on('change', function() {
        updateButtonPreview();
        updateBlockStatus($(this).closest('.visual-block'), 'modified');
    });

    // Typography controls
    $('#font_pair, #heading_size, #body_size').on('change', function() {
        updateTypographyPreview();
        updateBlockStatus($(this).closest('.visual-block'), 'modified');
    });

    // Spacing controls
    $('#section_spacing, #container_width').on('change', function() {
        updateSpacingPreview();
        updateBlockStatus($(this).closest('.visual-block'), 'modified');
    });

    // Animation controls
    $('#hero_animation, #image_animation, #animation_speed, #animation_delay').on('change', function() {
        updateAnimationPreview();
        updateBlockStatus($(this).closest('.visual-block'), 'modified');
    });

    // Replay animations button
    $('#replay-animations').on('click', function() {
        replayAnimations();
    });

    // Color preview updates
    $('#primary_color').on('change', function() {
        var color = $(this).val();
        $('#primary-preview').css('background-color', color);
        updatePreview();
    });
    
    $('#secondary_color').on('change', function() {
        var color = $(this).val();
        $('#secondary-preview').css('background-color', color);
        updatePreview();
    });
    
    // Logo preview
    $('#logo').on('change', function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#logo-preview').html('<img src="' + e.target.result + '" alt="Logo Preview">');
                updatePreview();
            };
            reader.readAsDataURL(file);
        }
    });
    
    // Real-time preview updates
    $('#company_name, #tagline, #about_text, #phone, #email, #address').on('input', function() {
        updatePreview();
    });
    
    $('#business_type').on('change', function() {
        updatePreview();
    });
    
    // Form submission
    $('#website-generator-form').on('submit', function(e) {
        e.preventDefault();
        
        var $button = $('#generate-website');
        var originalText = $button.html();
        
        // Show loading state
        $button.html('<span class="dashicons dashicons-update-alt"></span> Generating...').prop('disabled', true);
        
        var formData = new FormData(this);
        formData.append('action', 'generate_website');
        formData.append('nonce', ajax_object.nonce);
        
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showResults(response.data);
                    showSuccessMessage('Website generated successfully!');
                } else {
                    showErrorMessage('Error: ' + response.data);
                }
            },
            error: function(xhr, status, error) {
                showErrorMessage('AJAX Error: ' + error);
            },
            complete: function() {
                // Restore button
                $button.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Preview button
    $('#preview-changes').on('click', function() {
        updatePreview();
    });
    
    function updatePreview() {
        var companyName = $('#company_name').val() || 'Your Company';
        var tagline = $('#tagline').val() || 'Your Business Tagline';
        var businessType = $('#business_type').val();
        var primaryColor = $('#primary_color').val();
        var secondaryColor = $('#secondary_color').val();
        var aboutText = $('#about_text').val();
        var phone = $('#phone').val();
        var email = $('#email').val();
        var address = $('#address').val();
        
        var previewHtml = generatePreviewHTML(companyName, tagline, businessType, primaryColor, secondaryColor, aboutText, phone, email, address);
        $('#preview-container').html(previewHtml);
    }
    
    function generatePreviewHTML(companyName, tagline, businessType, primaryColor, secondaryColor, aboutText, phone, email, address) {
        var businessTypeText = getBusinessTypeText(businessType);
        
        var html = '<div style="font-family: Arial, sans-serif;">';
        
        // Header
        html += '<div style="background: ' + primaryColor + '; color: white; padding: 20px; text-align: center; margin-bottom: 20px;">';
        html += '<h2 style="margin: 0; font-size: 24px;">' + companyName + '</h2>';
        if (tagline) {
            html += '<p style="margin: 5px 0 0 0; opacity: 0.9;">' + tagline + '</p>';
        }
        html += '</div>';
        
        // Main content
        html += '<div style="padding: 0 20px;">';
        
        // Business type specific content
        if (businessType === 'repair') {
            html += '<h3 style="color: ' + primaryColor + ';">Premier Device Repair in Your Area</h3>';
            html += '<p>Smartphones | Tablets | Computers & More</p>';
        } else if (businessType === 'restaurant') {
            html += '<h3 style="color: ' + primaryColor + ';">Delicious Food, Great Service</h3>';
            html += '<p>Fresh ingredients, authentic flavors</p>';
        } else if (businessType === 'law') {
            html += '<h3 style="color: ' + primaryColor + ';">Professional Legal Services</h3>';
            html += '<p>Experienced attorneys you can trust</p>';
        } else {
            html += '<h3 style="color: ' + primaryColor + ';">Welcome to ' + companyName + '</h3>';
            html += '<p>' + businessTypeText + '</p>';
        }
        
        // Buttons
        html += '<div style="margin: 20px 0;">';
        html += '<button style="background: ' + primaryColor + '; color: white; border: none; padding: 12px 24px; margin-right: 10px; border-radius: 4px; cursor: pointer;">Get Started</button>';
        html += '<button style="background: transparent; color: ' + primaryColor + '; border: 2px solid ' + primaryColor + '; padding: 10px 22px; border-radius: 4px; cursor: pointer;">Learn More</button>';
        html += '</div>';
        
        // About section
        if (aboutText) {
            html += '<h4 style="color: ' + secondaryColor + ';">About Us</h4>';
            html += '<p>' + aboutText + '</p>';
        }
        
        // Contact section
        if (phone || email || address) {
            html += '<h4 style="color: ' + secondaryColor + ';">Contact Information</h4>';
            if (phone) html += '<p><strong>Phone:</strong> ' + phone + '</p>';
            if (email) html += '<p><strong>Email:</strong> ' + email + '</p>';
            if (address) html += '<p><strong>Address:</strong> ' + address.replace(/\n/g, '<br>') + '</p>';
        }
        
        html += '</div>';
        
        // Footer
        html += '<div style="background: ' + secondaryColor + '; color: white; padding: 20px; text-align: center; margin-top: 30px;">';
        html += '<p style="margin: 0;">&copy; 2025 ' + companyName + '. All rights reserved.</p>';
        html += '</div>';
        
        html += '</div>';
        
        return html;
    }
    
    function getBusinessTypeText(businessType) {
        switch (businessType) {
            case 'repair':
                return 'Professional device repair services';
            case 'restaurant':
                return 'Exceptional dining experience';
            case 'law':
                return 'Trusted legal representation';
            default:
                return 'Quality service you can trust';
        }
    }
    
    function showResults(data) {
        var html = '<div class="success-message">';
        html += '<h4>Website Generated Successfully!</h4>';
        html += '<ul>';
        
        if (data.colors) {
            html += '<li>✓ Colors updated: Primary (' + data.colors.primary + '), Secondary (' + data.colors.secondary + ')</li>';
        }
        
        if (data.logo) {
            html += '<li>✓ Logo uploaded and set</li>';
        }
        
        if (data.identity) {
            html += '<li>✓ Site title and tagline updated</li>';
        }
        
        if (data.content) {
            html += '<li>✓ Homepage content updated</li>';
        }
        
        html += '</ul>';
        html += '<p><strong>Next steps:</strong></p>';
        html += '<ul>';
        html += '<li>Visit your <a href="' + window.location.origin + '" target="_blank">website homepage</a> to see the changes</li>';
        html += '<li>Test the website on different devices</li>';
        html += '<li>Make any additional customizations needed</li>';
        html += '</ul>';
        html += '</div>';
        
        $('#results-content').html(html);
        $('#generation-results').show();
        
        // Scroll to results
        $('html, body').animate({
            scrollTop: $('#generation-results').offset().top
        }, 500);
    }
    
    function showSuccessMessage(message) {
        var html = '<div class="success-message">' + message + '</div>';
        $('#results-content').prepend(html);
    }
    
    function showErrorMessage(message) {
        var html = '<div class="error-message">' + message + '</div>';
        $('#results-content').html(html);
        $('#generation-results').show();
    }

    // Backup functionality
    $('#create-backup-btn').on('click', function() {
        var $button = $(this);
        var originalText = $button.html();

        $button.html('<span class="dashicons dashicons-update-alt"></span> Creating Backup...').prop('disabled', true);

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'create_backup',
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.status === 'success') {
                    alert('Backup created successfully!');
                    loadBackups(); // Refresh backup list
                } else {
                    alert('Error creating backup: ' + response.message);
                }
            },
            error: function() {
                alert('Error creating backup');
            },
            complete: function() {
                $button.html(originalText).prop('disabled', false);
            }
        });
    });

    $('#load-backups-btn').on('click', function() {
        loadBackups();
    });

    function loadBackups() {
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'get_all_backups', // Correct action for loading backups
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.success && response.data && response.data.backups) {
                    displaySimpleBackupList(response.data.backups);
                } else {
                    $('#backups-container').html('<p>No backups found.</p>');
                }
            },
            error: function() {
                $('#backups-container').html('<p>Error loading backups.</p>');
            }
        });
    }

    function displaySimpleBackupList(backups) {
        var html = '<h4>Available Backups:</h4>';

        if (backups.length === 0) {
            html += '<p>No backups found.</p>';
        } else {
            html += '<div class="backup-list">';

            Object.keys(backups).forEach(function(backupId) {
                var backup = backups[backupId];
                var timestamp = new Date(backup.timestamp).toLocaleString();

                html += '<div class="backup-item">';
                html += '<div class="backup-info">';
                html += '<strong>Backup ID:</strong> ' + backupId + '<br>';
                html += '<strong>Created:</strong> ' + timestamp + '<br>';
                html += '<strong>Type:</strong> ' + (backup.backup_type || 'Standard') + '<br>';
                if (backup.size) {
                    html += '<strong>Size:</strong> ' + formatBytes(backup.size) + '<br>';
                }
                html += '</div>';
                html += '<div class="backup-actions">';
                html += '<button type="button" class="button button-primary restore-simple-backup" data-backup-id="' + backupId + '">Restore</button>';
                html += '</div>';
                html += '</div>';
            });

            html += '</div>';
        }

        $('#backups-container').html(html);

        // Bind restore events
        $('.restore-simple-backup').on('click', function() {
            var backupId = $(this).data('backup-id');
            restoreSimpleBackup(backupId);
        });
    }

    function restoreSimpleBackup(backupId) {
        if (!confirm('Are you sure you want to restore this backup? This will overwrite current settings.')) {
            return;
        }

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'restore_backup',
                backup_id: backupId,
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.status === 'success') {
                    alert('Backup restored successfully!');
                    location.reload(); // Refresh page to show restored content
                } else {
                    alert('Error restoring backup: ' + response.message);
                }
            },
            error: function() {
                alert('Error restoring backup');
            }
        });
    }

    function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Text content editor
    $('#load-text-content-btn').on('click', function() {
        var $button = $(this);
        var originalText = $button.html();

        $button.html('<span class="dashicons dashicons-update-alt"></span> Loading...').prop('disabled', true);

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'get_text_content',
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayTextContent(response.data);
                } else {
                    alert('Error loading text content');
                }
            },
            error: function() {
                alert('Error loading text content');
            },
            complete: function() {
                $button.html(originalText).prop('disabled', false);
            }
        });
    });

    function displayTextContent(data) {
        var html = '<div class="text-content-editor">';
        html += '<h4>Edit Website Text Content</h4>';
        html += '<form id="text-content-form">';

        // Site identity
        if (data.site_identity) {
            html += '<h5>Site Identity</h5>';
            for (var key in data.site_identity) {
                var item = data.site_identity[key];
                html += '<div class="text-field" style="margin-bottom: 15px;">';
                html += '<label><strong>' + item.label + '</strong></label>';
                html += '<p style="font-size: 12px; color: #666;">Location: ' + item.location + '</p>';
                html += '<input type="text" name="' + item.key + '" value="' + item.current_value + '" style="width: 100%; padding: 5px;">';
                html += '</div>';
            }
        }

        // Custom texts
        if (data.custom_texts) {
            html += '<h5>Content Text</h5>';
            for (var key in data.custom_texts) {
                var item = data.custom_texts[key];
                html += '<div class="text-field" style="margin-bottom: 15px;">';
                html += '<label><strong>' + item.label + '</strong></label>';
                html += '<p style="font-size: 12px; color: #666;">Location: ' + item.location + '</p>';
                if (item.current_value.length > 100) {
                    html += '<textarea name="' + item.key + '" rows="3" style="width: 100%; padding: 5px;">' + item.current_value + '</textarea>';
                } else {
                    html += '<input type="text" name="' + item.key + '" value="' + item.current_value + '" style="width: 100%; padding: 5px;">';
                }
                html += '</div>';
            }
        }

        html += '<button type="submit" class="button button-primary">Update Text Content</button>';
        html += '</form>';
        html += '</div>';

        $('#text-content-container').html(html);

        // Handle form submission
        $('#text-content-form').on('submit', function(e) {
            e.preventDefault();
            updateTextContent($(this).serialize());
        });
    }

    function updateTextContent(formData) {
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'update_text_content',
                updates: formData,
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.status === 'success') {
                    alert('Text content updated successfully!');
                } else {
                    alert('Error updating text content');
                }
            },
            error: function() {
                alert('Error updating text content');
            }
        });
    }

    // Unused assets scanner
    $('#load-unused-assets-btn').on('click', function() {
        var $button = $(this);
        var originalText = $button.html();

        $button.html('<span class="dashicons dashicons-update-alt"></span> Scanning...').prop('disabled', true);

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'get_unused_assets',
                nonce: ajax_object.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayUnusedAssets(response.data);
                } else {
                    alert('Error scanning for unused assets');
                }
            },
            error: function() {
                alert('Error scanning for unused assets');
            },
            complete: function() {
                $button.html(originalText).prop('disabled', false);
            }
        });
    });

    function displayUnusedAssets(data) {
        var html = '<div class="unused-assets">';
        html += '<h4>Unused Assets Found</h4>';

        if (data.plugins && data.plugins.length > 0) {
            html += '<h5>Unused Plugins (' + data.plugins.length + ')</h5>';
            html += '<ul>';
            data.plugins.forEach(function(plugin) {
                html += '<li><strong>' + plugin.name + '</strong> - ' + plugin.description + '</li>';
            });
            html += '</ul>';
        }

        if (data.themes && data.themes.length > 0) {
            html += '<h5>Unused Themes (' + data.themes.length + ')</h5>';
            html += '<ul>';
            data.themes.forEach(function(theme) {
                html += '<li><strong>' + theme.name + '</strong> - ' + theme.description + '</li>';
            });
            html += '</ul>';
        }

        if ((!data.plugins || data.plugins.length === 0) && (!data.themes || data.themes.length === 0)) {
            html += '<p>No unused assets found!</p>';
        }

        html += '</div>';

        $('#unused-assets-container').html(html);
    }

    // Initialize preview on page load
    updatePreview();

    // Initialize form field indicators on page load
    function initializeFormFields() {
        console.log('Content loaded - form fields now show editable values instead of placeholders');

        // Add visual indicator that content is loaded
        $('.block-input').each(function() {
            if ($(this).val()) {
                $(this).addClass('has-content');
            }
        });

        // Show helpful message
        if ($('.block-input[value!=""]').length > 0) {
            console.log('✅ Form fields populated with current content - ready for editing!');
        }
    }

    // Initialize form field indicators
    initializeFormFields();

    // Load current content via AJAX on page load
    loadCurrentContent();

    // Add content change detection
    $('.block-input').on('input change', function() {
        if ($(this).val()) {
            $(this).addClass('has-content');
            $(this).removeClass('is-empty');
        } else {
            $(this).removeClass('has-content');
            $(this).addClass('is-empty');
        }

        // Update block status when content changes
        updateBlockStatus($(this).closest('.visual-block'), 'modified');
    });

    // Add visual feedback for content state
    $('.block-input').on('focus', function() {
        $(this).addClass('is-focused');
    }).on('blur', function() {
        $(this).removeClass('is-focused');
    });

    function revertAllChanges() {
        console.log('🔄 Reverting all design changes...');

        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'revert_design_changes',
                nonce: ajax_object.nonce
            },
            success: function(response) {
                console.log('✅ Revert response:', response);
                if (response.success) {
                    showMessage('All design changes reverted successfully! Please refresh your website to see the original appearance.', 'success');
                } else {
                    showMessage('Failed to revert changes: ' + response.data.message, 'error');
                }
            },
            error: function() {
                console.log('❌ Revert AJAX error');
                showMessage('Error reverting changes', 'error');
            }
        });
    }

});
