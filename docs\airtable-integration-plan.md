# RepairLift WP Customizer - Airtable Integration Plan

## Overview
This document outlines the comprehensive plan for integrating Airtable forms with the RepairLift WP Customizer plugin to automate website content population through external form submissions.

## 1. Airtable Base Structure Design

### Table 1: Website Content Submissions
This table will store all form submissions for website content updates.

#### Core Fields
- **Submission ID** (Auto Number) - Primary key
- **Site Identifier** (Single Line Text) - Unique identifier for each WordPress site
- **Submission Date** (Date) - When the form was submitted
- **Status** (Single Select) - Pending, Processed, Failed, Rejected
- **Webhook URL** (Single Line Text) - Target WordPress site webhook endpoint

#### Business Information Fields (19 fields)
Based on `class-business-info-collector.php`:

1. **business_name** (Single Line Text) - Required
2. **city** (Single Line Text) - Required  
3. **state** (Single Line Text) - Required
4. **devices_repaired** (Multiple Select) - iPhone, Android, Tablet, Computer, Apple Watch, Game Console
5. **specializations** (Multiple Select) - Screen Repair, Battery Replacement, Water Damage, etc.
6. **brands_supported** (Multiple Select) - Apple, Samsung, Google, etc.
7. **years_experience** (Number)
8. **warranty_offered** (Single Select) - 30 days, 90 days, 6 months, 1 year, lifetime
9. **turnaround_time** (Single Select) - Same day, 24 hours, 2-3 days, 1 week
10. **service_area** (Long Text)
11. **additional_services** (Multiple Select)
12. **phone** (Phone Number)
13. **email** (Email)
14. **website** (URL)
15. **facebook** (URL)
16. **instagram** (URL)
17. **twitter** (URL)
18. **google_business** (URL)
19. **key_benefits** (Long Text)

#### Visual Blocks Content Fields (27+ fields)

##### Hero Section (4 fields)
- **hero_heading** (Single Line Text, Max 100 chars)
- **hero_tagline** (Single Line Text, Max 80 chars)
- **hero_button1** (Single Line Text, Max 30 chars)
- **hero_button2** (Single Line Text, Max 30 chars)

##### One-Stop Shop Section (3 fields)
- **onestop_heading** (Single Line Text, Max 80 chars)
- **onestop_description** (Long Text, Max 300 chars)
- **onestop_button** (Single Line Text, Max 30 chars)

##### Buy Devices Section (3 fields)
- **buy_heading** (Single Line Text, Max 80 chars)
- **buy_description** (Long Text, Max 300 chars)
- **buy_button** (Single Line Text, Max 30 chars)

##### Sell Devices Section (3 fields)
- **sell_heading** (Single Line Text, Max 80 chars)
- **sell_description** (Long Text, Max 300 chars)
- **sell_button** (Single Line Text, Max 30 chars)

##### Call-to-Action Section (3 fields)
- **cta_heading** (Single Line Text, Max 80 chars)
- **cta_description** (Long Text, Max 300 chars)
- **cta_button** (Single Line Text, Max 30 chars)

##### Contact Information (3 fields)
- **contact_phone** (Phone Number)
- **contact_email** (Email)
- **contact_address** (Long Text)

##### Design System (4 fields)
- **primary_color** (Single Line Text) - Hex color code
- **secondary_color** (Single Line Text) - Hex color code
- **accent_color** (Single Line Text) - Hex color code
- **button_text_color** (Single Line Text) - Hex color code

##### Logo & Branding (1 field)
- **logo_upload** (Attachment) - Logo file upload

#### Processing Fields
- **Processing Notes** (Long Text) - Error messages or processing details
- **Last Processed** (Date) - When webhook was last attempted
- **WordPress Response** (Long Text) - Response from WordPress webhook

### Table 2: Site Configurations
This table manages multiple WordPress site configurations.

#### Fields
- **Site ID** (Single Line Text) - Primary key, matches Site Identifier in submissions
- **Site Name** (Single Line Text) - Friendly name for the site
- **WordPress URL** (URL) - Base URL of WordPress site
- **Webhook Endpoint** (URL) - Full webhook URL
- **API Key** (Single Line Text) - Authentication key for webhook
- **Status** (Single Select) - Active, Inactive, Testing
- **Created Date** (Date)
- **Last Updated** (Date)
- **Notes** (Long Text)

## 2. Airtable Form Configuration

### Form Sections
The Airtable form will be organized into logical sections:

1. **Site Selection** - Dropdown to select target WordPress site
2. **Business Information** - All 19 business info fields
3. **Hero Section Content** - 4 hero fields
4. **Service Sections** - One-Stop, Buy, Sell sections (9 fields total)
5. **Call-to-Action** - CTA section (3 fields)
6. **Contact Details** - Contact information (3 fields)
7. **Design Preferences** - Color scheme (4 fields)
8. **Logo Upload** - Logo file attachment

### Form Validation Rules
- Required fields: business_name, city, state, site_identifier
- Character limits enforced on all text fields
- Email and phone number format validation
- Color fields must be valid hex codes (#RRGGBB format)
- Logo files limited to PNG, JPG, SVG, WebP (max 5MB)

## 3. WordPress Plugin Modifications

### New Files to Create
1. `includes/airtable-integration.php` - Main integration class
2. `includes/airtable-webhook-handler.php` - Webhook receiver
3. `includes/airtable-field-mapper.php` - Field mapping logic
4. `admin/airtable-config.php` - Admin configuration interface

### Webhook Endpoint
- **URL Pattern**: `/wp-admin/admin-ajax.php?action=airtable_webhook`
- **Method**: POST
- **Authentication**: API key + WordPress nonce
- **Content-Type**: application/json

### Security Measures
- Webhook signature verification
- API key authentication
- WordPress capability checks
- Data sanitization and validation
- Rate limiting
- Automatic backup before changes

## 4. Implementation Phases

### Phase 1: Core Infrastructure
- Create webhook receiver endpoint
- Implement basic field mapping
- Add security validation
- Create admin configuration interface

### Phase 2: Content Processing
- Implement automatic content population
- Add backup integration
- Create error handling and logging
- Add status reporting

### Phase 3: Multi-Site Support
- Create site configuration management
- Implement scalable deployment system
- Add bulk operations support
- Create monitoring dashboard

### Phase 4: Testing & Documentation
- Comprehensive testing suite
- Setup documentation
- Deployment guides
- Troubleshooting resources

## Next Steps
1. Create Airtable base template
2. Implement webhook receiver endpoint
3. Build field mapping system
4. Add security and validation
5. Create admin interface
6. Test end-to-end integration
