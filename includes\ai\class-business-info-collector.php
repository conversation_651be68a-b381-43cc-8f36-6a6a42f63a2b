<?php

/**
 * Business Information Collector
 * 
 * Handles collection, validation, and storage of device repair store business information
 * 
 * @package WebsiteGenerator
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class BusinessInfoCollector {
    
    // Field Categories
    const CATEGORY_IDENTITY = 'identity';
    const CATEGORY_CONTACT = 'contact';
    const CATEGORY_SERVICES = 'services';
    const CATEGORY_CHARACTERISTICS = 'characteristics';
    const CATEGORY_ADDITIONAL = 'additional';
    
    // Field Types
    const FIELD_TEXT = 'text';
    const FIELD_TEXTAREA = 'textarea';
    const FIELD_SELECT = 'select';
    const FIELD_CHECKBOX = 'checkbox';
    const FIELD_NUMBER = 'number';
    const FIELD_EMAIL = 'email';
    const FIELD_PHONE = 'phone';
    
    private $business_info_fields;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->business_info_fields = $this->get_business_info_fields();
        $this->initialize_default_fields();
    }
    
    /**
     * Get device repair store specific business information fields
     */
    private function get_business_info_fields() {
        return array(
            // Core Business Identity
            'business_name' => array(
                'label' => 'Business Name',
                'description' => 'Your repair store name (e.g., "TechFix Pro", "Mobile Repair Plus")',
                'category' => self::CATEGORY_IDENTITY,
                'field_type' => self::FIELD_TEXT,
                'is_required' => true,
                'display_order' => 1,
                'placeholder' => 'TechFix Pro'
            ),
            'city' => array(
                'label' => 'City',
                'description' => 'City where your repair store is located',
                'category' => self::CATEGORY_CONTACT,
                'field_type' => self::FIELD_TEXT,
                'is_required' => true,
                'display_order' => 2,
                'placeholder' => 'Miami'
            ),
            'state' => array(
                'label' => 'State',
                'description' => 'State abbreviation (e.g., FL, CA, NY)',
                'category' => self::CATEGORY_CONTACT,
                'field_type' => self::FIELD_TEXT,
                'is_required' => true,
                'display_order' => 3,
                'placeholder' => 'FL'
            ),
            
            // Contact Information
            'phone_number' => array(
                'label' => 'Primary Phone Number',
                'description' => 'Main phone number for customer contact',
                'category' => self::CATEGORY_CONTACT,
                'field_type' => self::FIELD_PHONE,
                'is_required' => true,
                'display_order' => 4,
                'placeholder' => '(*************'
            ),
            'street_address' => array(
                'label' => 'Street Address',
                'description' => 'Physical address of your repair store',
                'category' => self::CATEGORY_CONTACT,
                'field_type' => self::FIELD_TEXT,
                'is_required' => true,
                'display_order' => 5,
                'placeholder' => '123 Main Street'
            ),
            'zip_code' => array(
                'label' => 'ZIP Code',
                'description' => 'Postal code for your location',
                'category' => self::CATEGORY_CONTACT,
                'field_type' => self::FIELD_TEXT,
                'is_required' => false,
                'display_order' => 6,
                'placeholder' => '12345'
            ),
            'email' => array(
                'label' => 'Business Email',
                'description' => 'Primary email for customer inquiries (optional)',
                'category' => self::CATEGORY_CONTACT,
                'field_type' => self::FIELD_EMAIL,
                'is_required' => false,
                'display_order' => 7,
                'placeholder' => '<EMAIL>'
            ),
            
            // Device Repair Specifics
            'devices_repaired' => array(
                'label' => 'Devices You Repair',
                'description' => 'Select all device types you repair',
                'category' => self::CATEGORY_SERVICES,
                'field_type' => self::FIELD_CHECKBOX,
                'is_required' => true,
                'display_order' => 8,
                'options' => array(
                )
            ),
            'specializations' => array(
                'label' => 'Specializations',
                'description' => 'What types of repairs do you specialize in?',
                'category' => self::CATEGORY_SERVICES,
                'field_type' => self::FIELD_TEXTAREA,
                'is_required' => false,
                'display_order' => 9,
                'placeholder' => 'Screen repair, water damage, battery replacement, data recovery'
            ),
            'brands_supported' => array(
                'label' => 'Brands You Work With',
                'description' => 'Major brands you repair and support',
                'category' => self::CATEGORY_SERVICES,
                'field_type' => self::FIELD_TEXTAREA,
                'is_required' => false,
                'display_order' => 10,
                'placeholder' => 'Apple, Samsung, Google, LG, HP, Dell, Lenovo'
            ),
            
            // Service Details
            'turnaround_time' => array(
                'label' => 'Typical Repair Time',
                'description' => 'How long do most repairs take?',
                'category' => self::CATEGORY_CHARACTERISTICS,
                'field_type' => self::FIELD_SELECT,
                'is_required' => false,
                'display_order' => 11,
                'options' => array(
                    'same_day' => 'Same day',
                    '1_2_hours' => '1-2 hours',
                    '24_hours' => '24 hours',
                    '2_3_days' => '2-3 days',
                    'varies' => 'Varies by repair'
                )
            ),
            'warranty_offered' => array(
                'label' => 'Warranty Period',
                'description' => 'How long do you warranty your repairs?',
                'category' => self::CATEGORY_CHARACTERISTICS,
                'field_type' => self::FIELD_SELECT,
                'is_required' => false,
                'display_order' => 12,
                'options' => array(
                    '30_days' => '30 days',
                    '90_days' => '90 days',
                    '6_months' => '6 months',
                    '1_year' => '1 year',
                    'lifetime' => 'Lifetime warranty'
                )
            ),
            'years_experience' => array(
                'label' => 'Years in Business',
                'description' => 'How many years have you been in business?',
                'category' => self::CATEGORY_CHARACTERISTICS,
                'field_type' => self::FIELD_NUMBER,
                'is_required' => false,
                'display_order' => 13,
                'placeholder' => '5'
            ),
            
            // Business Characteristics
            'key_benefits' => array(
                'label' => 'Top 3 Benefits',
                'description' => 'What are your main selling points?',
                'category' => self::CATEGORY_CHARACTERISTICS,
                'field_type' => self::FIELD_TEXTAREA,
                'is_required' => false,
                'display_order' => 14,
                'placeholder' => 'Fast service, Expert technicians, Competitive prices'
            ),

            
            // Additional Services
            'buy_devices' => array(
                'label' => 'Buy/Sell Devices',
                'description' => 'Do you buy and sell devices?',
                'category' => self::CATEGORY_ADDITIONAL,
                'field_type' => self::FIELD_SELECT,
                'is_required' => false,
                'display_order' => 15,
                'options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                    'buy_only' => 'Buy only',
                    'sell_only' => 'Sell only'
                )
            ),
            'data_recovery' => array(
                'label' => 'Data Recovery Service',
                'description' => 'Do you offer data recovery services?',
                'category' => self::CATEGORY_ADDITIONAL,
                'field_type' => self::FIELD_SELECT,
                'is_required' => false,
                'display_order' => 16,
                'options' => array(
                    'yes' => 'Yes',
                    'no' => 'No',
                    'basic' => 'Basic recovery only'
                )
            ),


            // Social Media & Online Presence
            'facebook_url' => array(
                'label' => 'Facebook Page URL',
                'description' => 'Your Facebook business page URL (appears in footer)',
                'category' => self::CATEGORY_ADDITIONAL,
                'field_type' => self::FIELD_TEXT,
                'is_required' => false,
                'display_order' => 17,
                'placeholder' => 'https://facebook.com/yourbusiness'
            ),
            'instagram_url' => array(
                'label' => 'Instagram Page URL',
                'description' => 'Your Instagram business page URL (appears in footer)',
                'category' => self::CATEGORY_ADDITIONAL,
                'field_type' => self::FIELD_TEXT,
                'is_required' => false,
                'display_order' => 18,
                'placeholder' => 'https://instagram.com/yourbusiness'
            ),
            'google_business_url' => array(
                'label' => 'Google Business Profile URL',
                'description' => 'Your Google Business Profile URL for reviews and directions',
                'category' => self::CATEGORY_ADDITIONAL,
                'field_type' => self::FIELD_TEXT,
                'is_required' => false,
                'display_order' => 19,
                'placeholder' => 'https://g.page/yourbusiness'
            ),


        );
    }
    
    /**
     * Initialize default fields in database
     */
    private function initialize_default_fields() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'website_generator_business_info';
        
        // Check if fields are already initialized
        $existing_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        
        if ($existing_count > 0) {
            return; // Already initialized
        }
        
        // Insert default field definitions
        foreach ($this->business_info_fields as $key => $field) {
            $wpdb->insert(
                $table_name,
                array(
                    'info_key' => $key,
                    'info_value' => null, // No default values
                    'info_category' => $field['category'],
                    'field_type' => $field['field_type'],
                    'is_required' => $field['is_required'],
                    'display_order' => $field['display_order']
                ),
                array('%s', '%s', '%s', '%s', '%d', '%d')
            );
        }
        
        error_log('Website Generator: Business info fields initialized');
    }
    
    /**
     * Get all business information fields with current values
     */
    public function get_all_business_info() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'website_generator_business_info';
        
        $results = $wpdb->get_results(
            "SELECT * FROM $table_name ORDER BY display_order ASC",
            ARRAY_A
        );
        
        $business_info = array();
        
        foreach ($results as $row) {
            $field_key = $row['info_key'];
            $field_definition = $this->business_info_fields[$field_key] ?? array();
            
            $business_info[$field_key] = array_merge($field_definition, array(
                'current_value' => $row['info_value'],
                'category' => $row['info_category'],
                'field_type' => $row['field_type'],
                'is_required' => (bool) $row['is_required'],
                'display_order' => (int) $row['display_order'],
                'updated_at' => $row['updated_at']
            ));
        }
        
        return $business_info;
    }
    
    /**
     * Get business information grouped by category
     */
    public function get_business_info_by_category() {
        $all_info = $this->get_all_business_info();
        $categorized = array();
        
        foreach ($all_info as $key => $info) {
            $category = $info['category'];
            if (!isset($categorized[$category])) {
                $categorized[$category] = array();
            }
            $categorized[$category][$key] = $info;
        }
        
        return $categorized;
    }

    /**
     * Update business information
     */
    public function update_business_info($updates) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'website_generator_business_info';
        $results = array();
        $errors = array();

        foreach ($updates as $key => $value) {
            // Validate the field
            $validation_result = $this->validate_field($key, $value);

            if ($validation_result['valid']) {
                // Update the field
                $result = $wpdb->update(
                    $table_name,
                    array(
                        'info_value' => $validation_result['sanitized_value'],
                        'updated_at' => current_time('mysql')
                    ),
                    array('info_key' => $key),
                    array('%s', '%s'),
                    array('%s')
                );

                if ($result !== false) {
                    $results[$key] = 'Updated successfully';
                } else {
                    $errors[$key] = 'Database update failed: ' . $wpdb->last_error;
                }
            } else {
                $errors[$key] = $validation_result['error'];
            }
        }

        return array(
            'status' => empty($errors) ? 'success' : 'partial',
            'results' => $results,
            'errors' => $errors,
            'message' => empty($errors) ? 'Business information updated successfully' : 'Some fields had errors'
        );
    }

    /**
     * Clear all business information
     */
    public function clear_all_business_info() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'website_generator_business_info';

        try {
            // Update all fields to have null values
            $result = $wpdb->update(
                $table_name,
                array(
                    'info_value' => null,
                    'updated_at' => current_time('mysql')
                ),
                array(), // No WHERE clause - update all rows
                array('%s', '%s'),
                array() // No WHERE format needed
            );

            if ($result !== false) {
                return array(
                    'status' => 'success',
                    'message' => 'All business information cleared successfully',
                    'cleared_count' => $result
                );
            } else {
                return array(
                    'status' => 'error',
                    'message' => 'Database error: ' . $wpdb->last_error
                );
            }

        } catch (Exception $e) {
            return array(
                'status' => 'error',
                'message' => 'Failed to clear business information: ' . $e->getMessage()
            );
        }
    }

    /**
     * Validate a single field
     */
    private function validate_field($key, $value) {
        if (!isset($this->business_info_fields[$key])) {
            return array(
                'valid' => false,
                'error' => 'Unknown field: ' . $key
            );
        }

        $field = $this->business_info_fields[$key];
        $sanitized_value = $value;

        // Check required fields
        if ($field['is_required'] && empty($value)) {
            return array(
                'valid' => false,
                'error' => $field['label'] . ' is required'
            );
        }

        // Validate based on field type
        switch ($field['field_type']) {
            case self::FIELD_EMAIL:
                if (!empty($value) && !is_email($value)) {
                    return array(
                        'valid' => false,
                        'error' => 'Invalid email format'
                    );
                }
                $sanitized_value = sanitize_email($value);
                break;

            case self::FIELD_PHONE:
                // Basic phone validation
                $phone_pattern = '/^[\+]?[1-9]?[\-\s\(\)]?[0-9]{3}[\-\s\(\)]?[0-9]{3}[\-\s]?[0-9]{4}$/';
                if (!empty($value) && !preg_match($phone_pattern, preg_replace('/[^\d\+\-\s\(\)]/', '', $value))) {
                    return array(
                        'valid' => false,
                        'error' => 'Invalid phone number format'
                    );
                }
                $sanitized_value = sanitize_text_field($value);
                break;

            case self::FIELD_NUMBER:
                if (!empty($value) && !is_numeric($value)) {
                    return array(
                        'valid' => false,
                        'error' => 'Must be a number'
                    );
                }
                $sanitized_value = intval($value);
                break;

            case self::FIELD_SELECT:
                if (!empty($value) && isset($field['options']) && !array_key_exists($value, $field['options'])) {
                    return array(
                        'valid' => false,
                        'error' => 'Invalid selection'
                    );
                }
                $sanitized_value = sanitize_text_field($value);
                break;

            case self::FIELD_CHECKBOX:
                // Handle checkbox arrays
                if (is_array($value)) {
                    $valid_options = array_keys($field['options'] ?? array());
                    $sanitized_array = array();

                    foreach ($value as $checkbox_value) {
                        if (in_array($checkbox_value, $valid_options)) {
                            $sanitized_array[] = sanitize_text_field($checkbox_value);
                        }
                    }

                    $sanitized_value = implode(',', $sanitized_array);
                } else {
                    $sanitized_value = sanitize_text_field($value);
                }
                break;

            case self::FIELD_TEXTAREA:
                $sanitized_value = sanitize_textarea_field($value);
                break;

            case self::FIELD_TEXT:
            default:
                $sanitized_value = sanitize_text_field($value);
                break;
        }

        return array(
            'valid' => true,
            'sanitized_value' => $sanitized_value
        );
    }

    /**
     * Get business information for AI prompt substitution
     */
    public function get_business_info_for_ai() {
        $business_info = $this->get_all_business_info();
        $ai_data = array();

        foreach ($business_info as $key => $info) {
            $value = $info['current_value'];

            // Handle checkbox fields (convert comma-separated to readable format)
            if ($info['field_type'] === self::FIELD_CHECKBOX && !empty($value)) {
                $selected_options = explode(',', $value);
                $readable_options = array();

                if (isset($info['options'])) {
                    foreach ($selected_options as $option_key) {
                        if (isset($info['options'][$option_key])) {
                            $readable_options[] = $info['options'][$option_key];
                        }
                    }
                }

                $value = implode(', ', $readable_options);
            }

            // Handle select fields (convert key to readable value)
            if ($info['field_type'] === self::FIELD_SELECT && !empty($value) && isset($info['options'][$value])) {
                $value = $info['options'][$value];
            }

            $ai_data[$key] = $value ?? '';
        }

        return $ai_data;
    }

    /**
     * Check if business information is complete enough for AI generation
     */
    public function is_business_info_complete() {
        $business_info = $this->get_all_business_info();
        $required_fields = array();
        $completed_required = 0;

        foreach ($business_info as $key => $info) {
            if ($info['is_required']) {
                $required_fields[] = $key;
                if (!empty($info['current_value'])) {
                    $completed_required++;
                }
            }
        }

        $completion_rate = count($required_fields) > 0 ? ($completed_required / count($required_fields)) * 100 : 100;

        return array(
            'is_complete' => $completion_rate >= 80, // 80% of required fields
            'completion_rate' => $completion_rate,
            'required_fields' => count($required_fields),
            'completed_required' => $completed_required,
            'missing_fields' => $this->get_missing_required_fields()
        );
    }

    /**
     * Get missing required fields
     */
    private function get_missing_required_fields() {
        $business_info = $this->get_all_business_info();
        $missing = array();

        foreach ($business_info as $key => $info) {
            if ($info['is_required'] && empty($info['current_value'])) {
                $missing[] = array(
                    'key' => $key,
                    'label' => $info['label'],
                    'description' => $info['description']
                );
            }
        }

        return $missing;
    }

    /**
     * Get field categories with labels
     */
    public function get_field_categories() {
        return array(
            self::CATEGORY_IDENTITY => array(
                'label' => 'Business Identity',
                'description' => 'Basic information about your repair store',
                'icon' => '🏢'
            ),
            self::CATEGORY_CONTACT => array(
                'label' => 'Contact Information',
                'description' => 'How customers can reach you',
                'icon' => '📞'
            ),
            self::CATEGORY_SERVICES => array(
                'label' => 'Services & Specializations',
                'description' => 'What devices and repairs you offer',
                'icon' => '🔧'
            ),
            self::CATEGORY_CHARACTERISTICS => array(
                'label' => 'Business Characteristics',
                'description' => 'What makes your business unique',
                'icon' => '⭐'
            ),
            self::CATEGORY_ADDITIONAL => array(
                'label' => 'Additional Services & Online Presence',
                'description' => 'Extra services, social media, and footer information',
                'icon' => '🌐'
            )
        );
    }
}
