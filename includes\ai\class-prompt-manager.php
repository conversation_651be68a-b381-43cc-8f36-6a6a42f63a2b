<?php

/**
 * AI Prompt Manager
 * 
 * Manages AI prompt templates for content generation
 * 
 * @package WebsiteGenerator
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PromptManager {
    
    private $table_name;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'website_generator_ai_prompts';
        $this->initialize_default_prompts();
    }
    
    /**
     * Initialize default prompts in database
     */
    private function initialize_default_prompts() {
        global $wpdb;

        // Load default prompts
        require_once plugin_dir_path(__FILE__) . 'class-device-repair-prompts.php';
        $default_prompts = DeviceRepairPrompts::get_default_prompts();

        // Check if prompts are already initialized
        $existing_count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");

        if ($existing_count > 0) {
            // Update existing prompts with enhanced versions
            $this->update_existing_prompts($default_prompts);
            return;
        }

        // Insert default prompts
        foreach ($default_prompts as $prompt_key => $prompt_data) {
            $this->create_prompt(
                $prompt_key,
                $prompt_data['name'],
                $prompt_data['template'],
                $prompt_data['content_type'],
                $prompt_data['variables'],
                $prompt_data['is_active'],
                $prompt_data['is_default']
            );
        }

        error_log('Website Generator: AI prompt templates initialized');
    }

    /**
     * Update existing prompts with enhanced versions
     */
    private function update_existing_prompts($default_prompts) {
        global $wpdb;

        // Character-sensitive fields that need updating
        $character_sensitive_fields = array('hero_heading', 'hero_tagline', 'about_title', 'about_description');

        foreach ($character_sensitive_fields as $prompt_key) {
            if (isset($default_prompts[$prompt_key])) {
                $prompt_data = $default_prompts[$prompt_key];

                // Check if this prompt exists and needs updating
                $existing_prompt = $wpdb->get_row($wpdb->prepare(
                    "SELECT prompt_template FROM {$this->table_name} WHERE prompt_key = %s",
                    $prompt_key
                ));

                if ($existing_prompt) {
                    // Check if the prompt contains the enhanced character limit instructions
                    if (strpos($existing_prompt->prompt_template, 'CRITICAL CHARACTER LIMIT') === false) {
                        // Update with enhanced version
                        $wpdb->update(
                            $this->table_name,
                            array(
                                'prompt_template' => $prompt_data['template'],
                                'variables' => json_encode($prompt_data['variables'])
                            ),
                            array('prompt_key' => $prompt_key),
                            array('%s', '%s'),
                            array('%s')
                        );

                        error_log("Website Generator: Updated prompt template for {$prompt_key} with character limits");
                    }
                } else {
                    // Create the prompt if it doesn't exist
                    $this->create_prompt(
                        $prompt_key,
                        $prompt_data['name'],
                        $prompt_data['template'],
                        $prompt_data['content_type'],
                        $prompt_data['variables'],
                        $prompt_data['is_active'],
                        $prompt_data['is_default']
                    );

                    error_log("Website Generator: Created missing prompt template for {$prompt_key}");
                }
            }
        }
    }
    
    /**
     * Create a new prompt template
     */
    public function create_prompt($prompt_key, $prompt_name, $template, $content_type, $variables = array(), $is_active = true, $is_default = false) {
        global $wpdb;
        
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'prompt_key' => $prompt_key,
                'prompt_name' => $prompt_name,
                'prompt_template' => $template,
                'content_type' => $content_type,
                'variables' => json_encode($variables),
                'is_active' => $is_active ? 1 : 0,
                'is_default' => $is_default ? 1 : 0
            ),
            array('%s', '%s', '%s', '%s', '%s', '%d', '%d')
        );
        
        if ($result === false) {
            return array(
                'success' => false,
                'error' => 'Failed to create prompt: ' . $wpdb->last_error
            );
        }
        
        return array(
            'success' => true,
            'prompt_id' => $wpdb->insert_id,
            'message' => 'Prompt created successfully'
        );
    }
    
    /**
     * Update an existing prompt template
     */
    public function update_prompt($prompt_key, $updates) {
        global $wpdb;
        
        $allowed_fields = array('prompt_name', 'prompt_template', 'content_type', 'variables', 'is_active');
        $update_data = array();
        $update_format = array();
        
        foreach ($updates as $field => $value) {
            if (in_array($field, $allowed_fields)) {
                if ($field === 'variables' && is_array($value)) {
                    $update_data[$field] = json_encode($value);
                } elseif ($field === 'is_active') {
                    $update_data[$field] = $value ? 1 : 0;
                } else {
                    $update_data[$field] = $value;
                }
                $update_format[] = '%s';
            }
        }
        
        if (empty($update_data)) {
            return array(
                'success' => false,
                'error' => 'No valid fields to update'
            );
        }
        
        $result = $wpdb->update(
            $this->table_name,
            $update_data,
            array('prompt_key' => $prompt_key),
            $update_format,
            array('%s')
        );
        
        if ($result === false) {
            return array(
                'success' => false,
                'error' => 'Failed to update prompt: ' . $wpdb->last_error
            );
        }
        
        return array(
            'success' => true,
            'message' => 'Prompt updated successfully'
        );
    }
    
    /**
     * Get a specific prompt template
     */
    public function get_prompt($prompt_key) {
        global $wpdb;
        
        $prompt = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE prompt_key = %s", $prompt_key),
            ARRAY_A
        );
        
        if (!$prompt) {
            return null;
        }
        
        // Decode variables JSON
        $prompt['variables'] = json_decode($prompt['variables'], true) ?: array();
        $prompt['is_active'] = (bool) $prompt['is_active'];
        $prompt['is_default'] = (bool) $prompt['is_default'];
        
        return $prompt;
    }
    
    /**
     * Get all prompt templates
     */
    public function get_all_prompts($content_type = null, $active_only = false) {
        global $wpdb;
        
        $where_conditions = array();
        $where_values = array();
        
        if ($content_type) {
            $where_conditions[] = 'content_type = %s';
            $where_values[] = $content_type;
        }
        
        if ($active_only) {
            $where_conditions[] = 'is_active = 1';
        }
        
        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }
        
        $query = "SELECT * FROM {$this->table_name} $where_clause ORDER BY content_type, prompt_name";
        
        if (!empty($where_values)) {
            $prompts = $wpdb->get_results($wpdb->prepare($query, $where_values), ARRAY_A);
        } else {
            $prompts = $wpdb->get_results($query, ARRAY_A);
        }
        
        // Process results
        foreach ($prompts as &$prompt) {
            $prompt['variables'] = json_decode($prompt['variables'], true) ?: array();
            $prompt['is_active'] = (bool) $prompt['is_active'];
            $prompt['is_default'] = (bool) $prompt['is_default'];
        }
        
        return $prompts;
    }
    
    /**
     * Get prompts grouped by content type
     */
    public function get_prompts_by_type($active_only = false) {
        $all_prompts = $this->get_all_prompts(null, $active_only);
        $grouped_prompts = array();
        
        foreach ($all_prompts as $prompt) {
            $content_type = $prompt['content_type'];
            if (!isset($grouped_prompts[$content_type])) {
                $grouped_prompts[$content_type] = array();
            }
            $grouped_prompts[$content_type][] = $prompt;
        }
        
        return $grouped_prompts;
    }
    
    /**
     * Delete a prompt template
     */
    public function delete_prompt($prompt_key) {
        global $wpdb;
        
        // Don't allow deletion of default prompts
        $prompt = $this->get_prompt($prompt_key);
        if ($prompt && $prompt['is_default']) {
            return array(
                'success' => false,
                'error' => 'Cannot delete default prompt templates'
            );
        }
        
        $result = $wpdb->delete(
            $this->table_name,
            array('prompt_key' => $prompt_key),
            array('%s')
        );
        
        if ($result === false) {
            return array(
                'success' => false,
                'error' => 'Failed to delete prompt: ' . $wpdb->last_error
            );
        }
        
        return array(
            'success' => true,
            'message' => 'Prompt deleted successfully'
        );
    }
    
    /**
     * Activate/deactivate a prompt template
     */
    public function toggle_prompt_status($prompt_key, $is_active) {
        return $this->update_prompt($prompt_key, array('is_active' => $is_active));
    }
    
    /**
     * Generate content using a specific prompt
     */
    public function generate_content($prompt_key, $business_info, $current_content = '') {
        $prompt = $this->get_prompt($prompt_key);
        
        if (!$prompt) {
            return array(
                'success' => false,
                'error' => 'Prompt template not found'
            );
        }
        
        if (!$prompt['is_active']) {
            return array(
                'success' => false,
                'error' => 'Prompt template is not active'
            );
        }
        
        // Add current content to business info for comparison
        $business_info['current_' . str_replace('_', '_', $prompt_key)] = $current_content;
        
        // Initialize Claude AI Manager
        require_once plugin_dir_path(__FILE__) . 'class-claude-ai-manager.php';
        $claude_ai = new ClaudeAIManager();
        
        if (!$claude_ai->is_configured()) {
            return array(
                'success' => false,
                'error' => 'Claude AI is not configured. Please add your API key in AI Settings.'
            );
        }
        
        // Generate content
        $result = $claude_ai->generate_content(
            $prompt['prompt_template'],
            $business_info,
            array('max_tokens' => 500) // Shorter content for website text
        );
        
        if ($result['success']) {
            // Store generation in history
            $this->store_generation_history(
                $prompt_key,
                $current_content,
                $result['content'],
                $prompt['prompt_template'],
                $business_info,
                $result['model'],
                $result['generation_time']
            );
        }
        
        return $result;
    }
    
    /**
     * Generate multiple variations of content
     */
    public function generate_variations($prompt_key, $business_info, $current_content = '', $count = 3) {
        $prompt = $this->get_prompt($prompt_key);
        
        if (!$prompt) {
            return array(
                'success' => false,
                'error' => 'Prompt template not found'
            );
        }
        
        // Add current content to business info
        $business_info['current_' . str_replace('_', '_', $prompt_key)] = $current_content;
        
        // Initialize Claude AI Manager
        require_once plugin_dir_path(__FILE__) . 'class-claude-ai-manager.php';
        $claude_ai = new ClaudeAIManager();
        
        if (!$claude_ai->is_configured()) {
            return array(
                'success' => false,
                'error' => 'Claude AI is not configured'
            );
        }
        
        // Generate variations
        $result = $claude_ai->generate_variations(
            $prompt['prompt_template'],
            $business_info,
            $count,
            array('max_tokens' => 500)
        );
        
        if ($result['success']) {
            // Store each variation in history
            foreach ($result['variations'] as $index => $variation) {
                $this->store_generation_history(
                    $prompt_key . '_variation_' . ($index + 1),
                    $current_content,
                    $variation['content'],
                    $prompt['prompt_template'],
                    $business_info,
                    $claude_ai->model ?? 'claude-3-sonnet',
                    $variation['generation_time']
                );
            }
        }
        
        return $result;
    }
    
    /**
     * Store generation history
     */
    private function store_generation_history($field_name, $original_content, $generated_content, $prompt_used, $business_info, $ai_model, $generation_time) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'website_generator_ai_history';
        $generation_id = wp_generate_uuid4();
        
        $wpdb->insert(
            $table_name,
            array(
                'generation_id' => $generation_id,
                'field_name' => $field_name,
                'original_content' => $original_content,
                'generated_content' => $generated_content,
                'prompt_used' => $prompt_used,
                'business_info_snapshot' => json_encode($business_info),
                'ai_model' => $ai_model,
                'generation_time' => $generation_time,
                'applied' => false,
                'created_by' => get_current_user_id()
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%f', '%d', '%d')
        );
        
        return $generation_id;
    }
    
    /**
     * Get content types with labels
     */
    public function get_content_types() {
        return array(
            'hero' => array(
                'label' => 'Hero Section',
                'description' => 'Main heading and tagline content',
                'icon' => '🎯'
            ),
            'about' => array(
                'label' => 'About Section',
                'description' => 'Business description and story',
                'icon' => '📖'
            ),
            'features' => array(
                'label' => 'Features Section',
                'description' => 'Key benefits and selling points',
                'icon' => '⭐'
            ),
            'services' => array(
                'label' => 'Services Section',
                'description' => 'Service descriptions and offerings',
                'icon' => '🔧'
            ),
            'cta' => array(
                'label' => 'Call-to-Action',
                'description' => 'Action-oriented content and buttons',
                'icon' => '📢'
            ),
            'contact' => array(
                'label' => 'Contact Section',
                'description' => 'Contact information and details',
                'icon' => '📞'
            ),
            'general' => array(
                'label' => 'General Content',
                'description' => 'Miscellaneous content templates',
                'icon' => '📝'
            )
        );
    }
}
