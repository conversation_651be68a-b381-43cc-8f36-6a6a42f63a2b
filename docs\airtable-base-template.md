# RepairLift Airtable Base Template

## Base Structure Overview
This document provides the exact field specifications for creating the Airtable base template for RepairLift WP Customizer integration.

## Table 1: Website Content Submissions

### System Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| Submission ID | Auto Number | Starting at 1 | Yes | Primary key for submissions |
| Site Identifier | Single Line Text | - | Yes | Unique WordPress site identifier |
| Submission Date | Date | Include time, GMT | No | Auto-populated submission timestamp |
| Status | Single Select | Pending, Processed, Failed, Rejected | No | Processing status |
| Processing Notes | Long Text | - | No | Error messages or processing details |

### Business Information Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| Business Name | Single Line Text | - | Yes | Repair shop business name |
| City | Single Line Text | - | Yes | Business location city |
| State | Single Line Text | - | Yes | Business location state |
| Devices Repaired | Multiple Select | iPhone, Android, Tablet, Computer, Apple Watch, Game Console | No | Types of devices serviced |
| Specializations | Multiple Select | Screen Repair, Battery Replacement, Water Damage, Data Recovery, Software Issues, Hardware Upgrades, Virus Removal | No | Service specializations |
| Brands Supported | Multiple Select | Apple, Samsung, Google, LG, Motorola, OnePlus, Huawei, Microsoft, Dell, HP, Lenovo | No | Device brands supported |
| Years Experience | Number | Integer, Min: 0, Max: 50 | No | Years in business |
| Warranty Offered | Single Select | 30 days, 90 days, 6 months, 1 year, Lifetime | No | Warranty period offered |
| Turnaround Time | Single Select | Same day, 24 hours, 2-3 days, 1 week | No | Typical repair timeframe |
| Service Area | Long Text | - | No | Geographic service area description |
| Additional Services | Multiple Select | Device Sales, Buyback Program, Accessories, Cases, Screen Protectors, Data Transfer | No | Additional services offered |
| Phone | Phone Number | - | No | Business phone number |
| Email | Email | - | No | Business email address |
| Website | URL | - | No | Business website URL |
| Facebook | URL | - | No | Facebook page URL |
| Instagram | URL | - | No | Instagram profile URL |
| Twitter | URL | - | No | Twitter profile URL |
| Google Business | URL | - | No | Google Business profile URL |
| Key Benefits | Long Text | Max 500 chars | No | Key business advantages |

### Hero Section Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| Hero Heading | Single Line Text | Max 100 chars | No | Main homepage heading |
| Hero Tagline | Single Line Text | Max 80 chars | No | Hero section tagline |
| Hero Button 1 | Single Line Text | Max 30 chars | No | Primary CTA button text |
| Hero Button 2 | Single Line Text | Max 30 chars | No | Secondary CTA button text |

### One-Stop Shop Section Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| One-Stop Heading | Single Line Text | Max 80 chars | No | Section heading |
| One-Stop Description | Long Text | Max 300 chars | No | Section description |
| One-Stop Button | Single Line Text | Max 30 chars | No | Section button text |

### Buy Devices Section Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| Buy Heading | Single Line Text | Max 80 chars | No | Section heading |
| Buy Description | Long Text | Max 300 chars | No | Section description |
| Buy Button | Single Line Text | Max 30 chars | No | Section button text |

### Sell Devices Section Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| Sell Heading | Single Line Text | Max 80 chars | No | Section heading |
| Sell Description | Long Text | Max 300 chars | No | Section description |
| Sell Button | Single Line Text | Max 30 chars | No | Section button text |

### Call-to-Action Section Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| CTA Heading | Single Line Text | Max 80 chars | No | Final CTA heading |
| CTA Description | Long Text | Max 300 chars | No | Final CTA description |
| CTA Button | Single Line Text | Max 30 chars | No | Final CTA button text |

### Contact Information Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| Contact Phone | Phone Number | - | No | Display phone number |
| Contact Email | Email | - | No | Display email address |
| Contact Address | Long Text | Max 200 chars | No | Business address |

### Design System Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| Primary Color | Single Line Text | Hex format validation | No | Primary brand color (#RRGGBB) |
| Secondary Color | Single Line Text | Hex format validation | No | Secondary brand color (#RRGGBB) |
| Accent Color | Single Line Text | Hex format validation | No | Accent color (#RRGGBB) |
| Button Text Color | Single Line Text | Hex format validation | No | Button text color (#RRGGBB) |

### Logo & Branding Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| Logo Upload | Attachment | PNG, JPG, SVG, WebP, Max 5MB | No | Business logo file |

## Table 2: Site Configurations

### Configuration Fields
| Field Name | Field Type | Configuration | Required | Description |
|------------|------------|---------------|----------|-------------|
| Site ID | Single Line Text | - | Yes | Unique site identifier (primary key) |
| Site Name | Single Line Text | - | Yes | Friendly site name |
| WordPress URL | URL | - | Yes | Base WordPress site URL |
| Webhook Endpoint | URL | - | Yes | Full webhook URL |
| API Key | Single Line Text | - | Yes | Authentication API key |
| Status | Single Select | Active, Inactive, Testing | Yes | Site status |
| Created Date | Date | Include time | No | Configuration creation date |
| Last Updated | Date | Include time | No | Last modification date |
| Notes | Long Text | - | No | Additional notes |

## Form Configuration

### Form Sections
1. **Site Selection**
   - Site Identifier (required)
   - Instructions: "Enter the unique identifier for your WordPress site"

2. **Business Information**
   - Business Name (required)
   - City (required)
   - State (required)
   - All other business fields (optional)

3. **Hero Section Content**
   - All hero fields with character limit reminders

4. **Service Sections**
   - One-Stop Shop fields
   - Buy Devices fields
   - Sell Devices fields

5. **Call-to-Action**
   - CTA fields

6. **Contact Information**
   - Contact fields

7. **Design & Branding**
   - Color fields with hex format instructions
   - Logo upload field

### Form Validation Rules
- Required fields: Site Identifier, Business Name, City, State
- Character limits enforced on all text fields
- Email format validation
- Phone number format validation
- Color fields must match hex pattern: #[0-9A-Fa-f]{6}
- Logo files: PNG, JPG, SVG, WebP only, max 5MB

### Form Customization
- Welcome message explaining form purpose
- Character limit indicators for text fields
- Helpful tooltips for technical fields
- Thank you message with next steps
- Allow multiple submissions enabled

## Automation Configuration

### Trigger Setup
- **Trigger**: When record created
- **Table**: Website Content Submissions
- **Condition**: Status is empty or "Pending"

### Webhook Action Setup
- **Method**: POST
- **Content-Type**: application/json
- **Headers**: 
  ```
  X-API-Key: {API_KEY_FROM_WORDPRESS}
  Content-Type: application/json
  ```

### Webhook URL Format
```
https://yourwordpresssite.com/wp-admin/admin-ajax.php?action=airtable_webhook
```

### Payload Template
```json
{
  "site_identifier": "{{Site Identifier}}",
  "business_name": "{{Business Name}}",
  "city": "{{City}}",
  "state": "{{State}}",
  "devices_repaired": "{{Devices Repaired}}",
  "specializations": "{{Specializations}}",
  "brands_supported": "{{Brands Supported}}",
  "years_experience": "{{Years Experience}}",
  "warranty_offered": "{{Warranty Offered}}",
  "turnaround_time": "{{Turnaround Time}}",
  "service_area": "{{Service Area}}",
  "additional_services": "{{Additional Services}}",
  "phone": "{{Phone}}",
  "email": "{{Email}}",
  "website": "{{Website}}",
  "facebook": "{{Facebook}}",
  "instagram": "{{Instagram}}",
  "twitter": "{{Twitter}}",
  "google_business": "{{Google Business}}",
  "key_benefits": "{{Key Benefits}}",
  "hero_heading": "{{Hero Heading}}",
  "hero_tagline": "{{Hero Tagline}}",
  "hero_button1": "{{Hero Button 1}}",
  "hero_button2": "{{Hero Button 2}}",
  "onestop_heading": "{{One-Stop Heading}}",
  "onestop_description": "{{One-Stop Description}}",
  "onestop_button": "{{One-Stop Button}}",
  "buy_heading": "{{Buy Heading}}",
  "buy_description": "{{Buy Description}}",
  "buy_button": "{{Buy Button}}",
  "sell_heading": "{{Sell Heading}}",
  "sell_description": "{{Sell Description}}",
  "sell_button": "{{Sell Button}}",
  "cta_heading": "{{CTA Heading}}",
  "cta_description": "{{CTA Description}}",
  "cta_button": "{{CTA Button}}",
  "contact_phone": "{{Contact Phone}}",
  "contact_email": "{{Contact Email}}",
  "contact_address": "{{Contact Address}}",
  "primary_color": "{{Primary Color}}",
  "secondary_color": "{{Secondary Color}}",
  "accent_color": "{{Accent Color}}",
  "button_text_color": "{{Button Text Color}}",
  "logo_upload": "{{Logo Upload}}"
}
```

## Views Configuration

### Default Views
1. **All Submissions** - Shows all records
2. **Pending** - Filter: Status = "Pending"
3. **Processed** - Filter: Status = "Processed"
4. **Failed** - Filter: Status = "Failed"
5. **By Site** - Group by Site Identifier

### Recommended Views
1. **Recent Submissions** - Last 30 days, sorted by Submission Date desc
2. **Needs Review** - Status = "Failed" or "Rejected"
3. **Complete Submissions** - All required fields filled

## Permissions & Sharing

### Recommended Permissions
- **Form**: Public (for external submissions)
- **Base**: Restricted to authorized users only
- **Site Configurations Table**: Admin access only
- **Submissions Table**: Read/write for processors, read-only for reviewers

### Security Considerations
- Keep API keys secure and rotate regularly
- Limit base access to necessary personnel
- Monitor submission patterns for abuse
- Use webhook signatures for additional security

## Backup & Maintenance

### Regular Tasks
- Export base monthly for backup
- Review and clean old submissions quarterly
- Update site configurations as needed
- Monitor automation run history for errors

### Monitoring
- Check automation success rates weekly
- Review failed submissions for patterns
- Monitor webhook response times
- Validate field mappings after updates
