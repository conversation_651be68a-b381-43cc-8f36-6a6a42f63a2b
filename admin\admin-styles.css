/* Website Generator Pro Admin Styles */

.website-generator-container {
    max-width: 1200px;
}

.generator-form-section,
.generator-preview-section,
#generation-results {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin-bottom: 20px;
}

.form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
}

.form-table td {
    padding: 15px 10px;
}

.form-table h2 {
    margin: 0;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
    color: #23282d;
}

/* Color Preview */
.color-preview {
    border-radius: 3px;
    vertical-align: middle;
    transition: all 0.3s ease;
}

.color-preview:hover {
    transform: scale(1.1);
}

/* Design System Preview Styles */
.design-preview {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin-top: 15px;
}

.preview-section {
    margin-bottom: 20px;
}

.preview-section h5 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

/* Button Preview Styles */
.preview-btn {
    display: inline-block;
    padding: 12px 24px;
    background-color: #165C9C;
    color: #FFFFFF;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-family: inherit;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

/* Secondary button default */
.preview-btn.secondary {
    background-color: #111111;
    color: #FFFFFF;
}

/* Button Shapes */
.preview-btn.pill {
    border-radius: 50px;
}

.preview-btn.rounded {
    border-radius: 8px;
}

.preview-btn.square {
    border-radius: 0;
}

/* Button Sizes */
.preview-btn.small {
    padding: 8px 16px;
    font-size: 14px;
}

.preview-btn.medium {
    padding: 12px 24px;
    font-size: 16px;
}

.preview-btn.large {
    padding: 16px 32px;
    font-size: 18px;
}

/* Button Shadows */
.preview-btn.shadow-none {
    box-shadow: none;
}

.preview-btn.shadow-subtle {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-btn.shadow-medium {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.preview-btn.shadow-strong {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Button Hover Effects */
.preview-btn.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.preview-btn.hover-scale:hover {
    transform: scale(1.05);
}

.preview-btn.hover-glow:hover {
    box-shadow: 0 0 20px rgba(22, 92, 156, 0.4);
}

.preview-btn.hover-slide:hover {
    transform: translateX(3px);
}

/* Typography Preview Styles */
.typography-preview {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #ddd;
}

.typography-preview h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.typography-preview p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

/* Font Families */
.typography-preview.font-modern {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.typography-preview.font-classic {
    font-family: 'Playfair Display', Georgia, serif;
}

.typography-preview.font-tech {
    font-family: 'Space Grotesk', 'Helvetica Neue', sans-serif;
}

.typography-preview.font-friendly {
    font-family: 'Poppins', 'Arial', sans-serif;
}

/* Heading Sizes */
.typography-preview.heading-small h3 {
    font-size: 1.5rem;
}

.typography-preview.heading-medium h3 {
    font-size: 2rem;
}

.typography-preview.heading-large h3 {
    font-size: 2.5rem;
}

/* Body Sizes */
.typography-preview.body-14 p {
    font-size: 14px;
}

.typography-preview.body-16 p {
    font-size: 16px;
}

.typography-preview.body-18 p {
    font-size: 18px;
}

/* Animation Preview Styles */
.animation-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.animation-preview h4 {
    margin: 0 0 10px 0;
    font-size: 1.5rem;
}

.animation-preview p {
    margin: 0;
    opacity: 0.9;
}

/* Hero Animations */
.animation-preview.hero-fade {
    animation: fadeIn 2s ease-in-out;
}

.animation-preview.hero-slide {
    animation: slideInUp 1.5s ease-out;
}

.animation-preview.hero-zoom {
    animation: zoomIn 1.8s ease-out;
}

/* Image Animations */
.animation-preview.image-float {
    animation: float 3s ease-in-out infinite;
}

.animation-preview.image-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animation-preview.image-rotate {
    animation: rotate 4s linear infinite;
}

/* Animation Keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Spacing Preview Styles */
.spacing-preview {
    background: white;
    border: 2px dashed #ddd;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
}

.spacing-preview .section {
    background: #f0f0f0;
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    color: #666;
}

.spacing-preview .section:last-child {
    margin-bottom: 0;
}

/* Spacing Sizes */
.spacing-preview.spacing-compact .section {
    margin-bottom: 5px;
    padding: 5px;
}

.spacing-preview.spacing-normal .section {
    margin-bottom: 10px;
    padding: 10px;
}

.spacing-preview.spacing-relaxed .section {
    margin-bottom: 20px;
    padding: 20px;
}

/* Container Width Preview */
.container-preview {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
    text-align: center;
    font-size: 12px;
    color: #666;
}

.container-preview.width-narrow {
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.container-preview.width-normal {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.container-preview.width-wide {
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

/* Color Input Enhancements */
.color-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-input {
    width: 50px;
    height: 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.color-value {
    font-family: monospace;
    font-size: 12px;
    color: #666;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
    min-width: 70px;
    text-align: center;
}

/* Color Swatches */
.color-swatches {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.color-swatch {
    text-align: center;
    flex: 1;
    min-width: 80px;
}

.color-swatch .swatch {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin: 0 auto 8px;
    border: 2px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.color-swatch .swatch:hover {
    transform: scale(1.05);
}

.color-swatch span {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

/* Button Preview Enhancements */
.button-preview {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.button-preview .preview-btn {
    min-width: 120px;
}

/* Animation Preview Enhancements */
.animation-preview {
    position: relative;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.preview-hero {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.preview-hero h2 {
    margin: 0 0 10px 0;
    color: white;
    font-size: 1.5rem;
}

.preview-hero p {
    margin: 0 0 15px 0;
    color: rgba(255, 255, 255, 0.9);
}

.preview-image-container {
    text-align: center;
}

.preview-image {
    font-size: 3rem;
    margin-bottom: 10px;
    display: block;
}

.replay-animation-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.replay-animation-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.replay-animation-btn .dashicons {
    font-size: 14px;
    margin-right: 5px;
}

/* Logo Preview */
#logo-preview img {
    max-width: 200px;
    max-height: 100px;
    border: 1px solid #ddd;
    padding: 5px;
    background: #fff;
    border-radius: 4px;
}

/* Buttons */
.button-large {
    height: auto !important;
    line-height: 1.4 !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
}

.button .dashicons {
    margin-right: 5px;
    margin-top: -2px;
}

#generate-website {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
    text-shadow: none;
    font-weight: 600;
}

#generate-website:hover {
    background: #005177;
    border-color: #005177;
}

#preview-changes {
    margin-left: 10px;
}

/* Preview Container */
#preview-container {
    border-radius: 4px;
    min-height: 300px;
    overflow: hidden;
}

/* Messages */
.success-message {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 12px;
    border-radius: 4px;
    margin: 10px 0;
}

.error-message {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 12px;
    border-radius: 4px;
    margin: 10px 0;
}

.success-message h4,
.error-message h4 {
    margin-top: 0;
}

.success-message ul,
.error-message ul {
    margin-bottom: 0;
}

/* Form Styling */
.regular-text {
    width: 300px;
}

.large-text {
    width: 500px;
}

textarea {
    resize: vertical;
}

select {
    min-width: 200px;
}

/* Loading Animation */
.dashicons-update-alt {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    .regular-text,
    .large-text {
        width: 100%;
        max-width: 100%;
    }
    
    .button-large {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }
    
    #preview-changes {
        margin-left: 0;
        width: 100%;
    }
}

/* Admin Notice Styling */
.notice.website-generator-notice {
    border-left-color: #0073aa;
}

.notice.website-generator-notice.notice-success {
    border-left-color: #46b450;
}

.notice.website-generator-notice.notice-error {
    border-left-color: #dc3232;
}
