// 🚀 AIRTABLE "RUN SCRIPT" AUTOMATION - STANDARDIZED & DEBUGGED
// For use in Airtable Automation "Run script" action
// Field Name Standardization: hero_title, hero_tagline (clean 1:1 mapping)

console.log('🔥 === AIRTABLE RUN SCRIPT STARTED ===');
console.log('📅 Timestamp:', new Date().toISOString());

// ===== STEP 1: GET INPUT CONFIGURATION =====
console.log('\n📋 STEP 1: Getting input configuration...');

// Get the input configuration from Airtable automation
let inputConfig = input.config();
console.log('🔍 Input config:', inputConfig);
console.log('🔍 Input config keys:', Object.keys(inputConfig));

// ===== STEP 2: INITIALIZE VARIABLES =====
console.log('\n📋 STEP 2: Initializing variables...');

let heroTitle = '';
let heroTagline = '';
let siteIdentifier = 'example-site';
let businessName = '';

// Track which method works for debugging
let workingMethod = 'none';
let debugInfo = {
    inputConfigKeys: Object.keys(inputConfig),
    methods: {
        directInput: { attempted: false, success: false, data: {} },
        recordAccess: { attempted: false, success: false, data: {} }
    }
};

// ===== STEP 3: DATA EXTRACTION WITH DEBUGGING =====
console.log('\n📋 STEP 3: Attempting data extraction methods...');

// METHOD 1: Direct input access (when fields are configured as input variables)
console.log('\n🔬 METHOD 1: Testing direct input access...');
debugInfo.methods.directInput.attempted = true;

try {
    // Try to get values directly from input configuration
    let testTitle = inputConfig.hero_title || inputConfig['hero_title'];
    let testTagline = inputConfig.hero_tagline || inputConfig['hero_tagline'];
    let testSite = inputConfig.site_identifier || inputConfig['Site Identifier'];
    let testBusiness = inputConfig.business_name || inputConfig['Business Name'];
    
    console.log('📤 Direct input results:');
    console.log('   hero_title:', testTitle, '(type:', typeof testTitle, ')');
    console.log('   hero_tagline:', testTagline, '(type:', typeof testTagline, ')');
    console.log('   site_identifier:', testSite, '(type:', typeof testSite, ')');
    console.log('   business_name:', testBusiness, '(type:', typeof testBusiness, ')');
    
    // Check if we got valid data
    if (testTitle || testTagline || testSite || testBusiness) {
        heroTitle = testTitle || '';
        heroTagline = testTagline || '';
        siteIdentifier = testSite || 'example-site';
        businessName = testBusiness || '';
        
        workingMethod = 'directInput';
        debugInfo.methods.directInput.success = true;
        debugInfo.methods.directInput.data = {
            hero_title: testTitle,
            hero_tagline: testTagline,
            site_identifier: testSite,
            business_name: testBusiness
        };
        
        console.log('✅ METHOD 1 SUCCESS: Direct input access worked!');
    } else {
        console.log('⚠️ METHOD 1 FAILED: No data found in direct input');
    }
} catch (error) {
    console.log('❌ METHOD 1 ERROR:', error.message);
}

// METHOD 2: Record access (when record is passed as input)
console.log('\n🔬 METHOD 2: Testing record access...');
debugInfo.methods.recordAccess.attempted = true;

try {
    // Check if there's a record in the input
    let record = inputConfig.record;
    
    if (record) {
        console.log('✅ Record found in input');
        console.log('🔍 Record type:', typeof record);
        console.log('🔍 Record keys:', Object.keys(record));
        
        // Try different ways to access record data
        let testTitle, testTagline, testSite, testBusiness;
        
        // Try getCellValue method
        if (typeof record.getCellValue === 'function') {
            console.log('🔬 Trying getCellValue method...');
            testTitle = record.getCellValue('hero_title');
            testTagline = record.getCellValue('hero_tagline');
            testSite = record.getCellValue('Site Identifier');
            testBusiness = record.getCellValue('Business Name');
            console.log('   getCellValue results - Title:', testTitle, 'Tagline:', testTagline);
        }
        
        // Try fields access if getCellValue didn't work
        if (!testTitle && !testTagline && record.fields) {
            console.log('🔬 Trying fields access...');
            testTitle = record.fields['hero_title'];
            testTagline = record.fields['hero_tagline'];
            testSite = record.fields['Site Identifier'];
            testBusiness = record.fields['Business Name'];
            console.log('   Fields access results - Title:', testTitle, 'Tagline:', testTagline);
        }
        
        // Try direct property access if others didn't work
        if (!testTitle && !testTagline) {
            console.log('🔬 Trying direct property access...');
            testTitle = record['hero_title'] || record.hero_title;
            testTagline = record['hero_tagline'] || record.hero_tagline;
            testSite = record['Site Identifier'] || record.site_identifier;
            testBusiness = record['Business Name'] || record.business_name;
            console.log('   Direct access results - Title:', testTitle, 'Tagline:', testTagline);
        }
        
        console.log('📤 Record access final results:');
        console.log('   hero_title:', testTitle, '(type:', typeof testTitle, ')');
        console.log('   hero_tagline:', testTagline, '(type:', typeof testTagline, ')');
        console.log('   site_identifier:', testSite, '(type:', typeof testSite, ')');
        console.log('   business_name:', testBusiness, '(type:', typeof testBusiness, ')');
        
        // Use this method if direct input didn't work
        if (workingMethod === 'none' && (testTitle || testTagline || testSite || testBusiness)) {
            heroTitle = testTitle || '';
            heroTagline = testTagline || '';
            siteIdentifier = testSite || 'example-site';
            businessName = testBusiness || '';
            
            workingMethod = 'recordAccess';
            debugInfo.methods.recordAccess.success = true;
            debugInfo.methods.recordAccess.data = {
                hero_title: testTitle,
                hero_tagline: testTagline,
                site_identifier: testSite,
                business_name: testBusiness
            };
            
            console.log('✅ METHOD 2 SUCCESS: Record access worked!');
        } else if (workingMethod !== 'none') {
            console.log('ℹ️ METHOD 2 SKIPPED: Already have working method');
        } else {
            console.log('⚠️ METHOD 2 FAILED: Record exists but no data found');
        }
    } else {
        console.log('❌ METHOD 2 FAILED: No record found in input');
    }
} catch (error) {
    console.log('❌ METHOD 2 ERROR:', error.message);
}

// ===== STEP 4: DEBUGGING SUMMARY =====
console.log('\n📊 STEP 4: Data extraction summary...');
console.log('🎯 Working method:', workingMethod);
console.log('📋 Method results:');
console.log('   directInput - Attempted:', debugInfo.methods.directInput.attempted, 'Success:', debugInfo.methods.directInput.success);
console.log('   recordAccess - Attempted:', debugInfo.methods.recordAccess.attempted, 'Success:', debugInfo.methods.recordAccess.success);

console.log('\n✅ Final extracted values:');
console.log('   Hero Title:', heroTitle, '(length:', heroTitle.length, ')');
console.log('   Hero Tagline:', heroTagline, '(length:', heroTagline.length, ')');
console.log('   Site Identifier:', siteIdentifier);
console.log('   Business Name:', businessName);

// ===== STEP 5: VALIDATION =====
console.log('\n📋 STEP 5: Data validation...');

if (!heroTitle && !heroTagline) {
    console.log('⚠️ WARNING: No hero content found - webhook will still be sent for testing');
} else {
    console.log('✅ Hero content found - proceeding with webhook');
}

// ===== STEP 6: WEBHOOK CONFIGURATION =====
console.log('\n📋 STEP 6: Webhook configuration...');

// REPLACE THESE WITH YOUR ACTUAL VALUES
const webhookUrl = 'https://your-wordpress-site.com/wp-json/website-generator/v1/webhook';
const apiKey = 'your-api-key-here';

console.log('🔗 Webhook URL:', webhookUrl);
console.log('🔑 API Key:', apiKey ? 'Set (length: ' + apiKey.length + ')' : 'Not set');

// ===== STEP 7: BUILD PAYLOAD =====
console.log('\n📋 STEP 7: Building webhook payload...');

const payload = {
    api_key: apiKey,
    site_identifier: siteIdentifier,
    hero_title: heroTitle,        // Standardized field name
    hero_tagline: heroTagline,    // Standardized field name
    business_name: businessName,
    // Debug info for troubleshooting
    debug_info: {
        working_method: workingMethod,
        timestamp: new Date().toISOString(),
        airtable_automation: true
    }
};

console.log('📤 Payload built:');
console.log(JSON.stringify(payload, null, 2));

// ===== STEP 8: SEND WEBHOOK =====
console.log('\n📋 STEP 8: Sending webhook...');

try {
    const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
    });

    const responseText = await response.text();
    
    console.log('📥 Response received:');
    console.log('   Status:', response.status);
    console.log('   Status Text:', response.statusText);
    console.log('   Body:', responseText);

    if (response.ok) {
        console.log('✅ WEBHOOK SUCCESS!');
        console.log('🎯 Successfully updated:');
        if (heroTitle) console.log('   - Hero Title:', heroTitle);
        if (heroTagline) console.log('   - Hero Tagline:', heroTagline);
        console.log('🔧 Working method was:', workingMethod);
    } else {
        console.log('❌ WEBHOOK FAILED');
        console.log('   Status:', response.status);
        console.log('   Response:', responseText);
    }
    
} catch (error) {
    console.log('❌ WEBHOOK ERROR:', error.message);
    console.log('   Error details:', error);
}

// ===== STEP 9: FINAL SUMMARY =====
console.log('\n🏁 FINAL SUMMARY:');
console.log('✅ Script execution completed');
console.log('🎯 Working data access method:', workingMethod);
console.log('📊 Data extracted:', heroTitle || heroTagline ? 'Yes' : 'No');
console.log('🚀 Webhook sent:', 'Yes');
console.log('📅 Completed at:', new Date().toISOString());

console.log('\n💡 OPTIMIZATION RECOMMENDATION:');
if (workingMethod !== 'none') {
    console.log('✅ For future script optimization, use only the "' + workingMethod + '" method');
    console.log('✅ Remove the other methods to simplify the script');
    console.log('✅ Configure Airtable automation input variables accordingly');
} else {
    console.log('⚠️ No working method found - check Airtable automation input configuration');
}

console.log('\n🔥 === AIRTABLE RUN SCRIPT COMPLETED ===');
