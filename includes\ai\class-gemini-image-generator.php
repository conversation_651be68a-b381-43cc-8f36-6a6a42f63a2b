<?php
/**
 * Google Gemini 2.5 Flash Image Generator Class
 * 
 * Handles AI image generation for device repair business using Google's Gemini API
 * 
 * @package RepairLift_WP_Customizer
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Gemini_Image_Generator {
    
    /**
     * Gemini API endpoint for image generation
     */
    private const API_ENDPOINT = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-image-preview:generateContent';
    
    /**
     * API key for Gemini service
     */
    private $api_key;
    
    /**
     * Upload directory for AI-generated images
     */
    private $upload_dir;
    
    /**
     * Device-specific prompt templates
     */
    private $prompt_templates;
    
    /**
     * Error messages
     */
    private $last_error = '';
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->api_key = get_option('repairlift_gemini_api_key', '');
        $this->setup_upload_directory();
        $this->init_prompt_templates();
        
        // Add WordPress hooks
        add_action('wp_ajax_generate_hero_image', array($this, 'ajax_generate_hero_image'));
        add_action('wp_ajax_get_generated_images', array($this, 'ajax_get_generated_images'));
        add_action('wp_ajax_delete_generated_image', array($this, 'ajax_delete_generated_image'));
        add_action('wp_ajax_save_gemini_api_key', array($this, 'ajax_save_gemini_api_key'));
        add_action('wp_ajax_test_gemini_connection', array($this, 'ajax_test_gemini_connection'));
        add_action('wp_ajax_get_current_hero_image', array($this, 'ajax_get_current_hero_image'));
    }
    
    /**
     * Set up the upload directory structure for AI images
     */
    private function setup_upload_directory() {
        $wp_upload_dir = wp_upload_dir();
        $this->upload_dir = $wp_upload_dir['basedir'] . '/ai-images/';
        
        // Create directory structure if it doesn't exist
        $directories = [
            $this->upload_dir,
            $this->upload_dir . 'hero-section/',
            $this->upload_dir . 'hero-section/composite-arrangements/',
            $this->upload_dir . 'thumbnails/',
            $this->upload_dir . 'metadata/'
        ];
        
        foreach ($directories as $dir) {
            if (!file_exists($dir)) {
                wp_mkdir_p($dir);
                
                // Add .htaccess to protect directory
                $htaccess_content = "Options -Indexes\n<Files *.php>\nDeny from all\n</Files>";
                file_put_contents($dir . '.htaccess', $htaccess_content);
            }
        }
    }
    
    /**
     * Initialize device-specific prompt templates
     */
    private function init_prompt_templates() {
        $this->prompt_templates = [
            
            'samsung_repair_display' => "A premium, professional product showcase of {device_model} smartphones positioned for a device repair service business. Features clean studio lighting, modern aesthetic, and sophisticated arrangement. High-quality commercial photography style perfect for repair shop marketing and website hero sections.",
            
            'multi_device_composite' => "A sophisticated, professional product display featuring multiple premium smartphones ({device_list}) arranged in an elegant, modern composition for a device repair business. Clean studio lighting, professional photography aesthetic, modern background, perfect for a repair shop website hero section. Emphasize quality and professionalism.",
            
            'repair_shop_scene' => "A professional product display showing {device_model} devices in a modern repair shop setting. Clean, well-lit environment showcasing quality repair services. Professional commercial photography with modern aesthetic, perfect for repair business website hero section.",
            
            'device_parts_display' => "A high-quality product display photo of {device_model} with carefully arranged repair components and parts visible. Professional studio lighting, clean background, emphasizing quality repair services and technical expertise. Commercial photography style for repair shop marketing."
        ];
    }
    
    /**
     * Generate AI image using Gemini 2.5 Flash API
     * 
     * @param string $prompt The consolidated prompt string.
     * @param array $options Additional generation options
     * @return array|false Generation result or false on failure
     */
    public function generate_device_image($prompt, $device_type, $device_model, $style, $options = []) {
     if (empty($this->api_key)) {
     	$this->last_error = 'Gemini API key not configured. Please add your API key in the plugin settings.';
     	return false;
     }

     // Make API request
     $response = $this->make_api_request($prompt, $options);
     if (!$response) {
     	return false;
     }

     // Process and save generated image
     $result = $this->process_generated_image($response, $device_type, $device_model, $style);
     if (!$result) {
     	return false;
     }

     // Log generation metadata
     $this->log_generation_metadata($result, $prompt, $device_type, $device_model, $style, $options);

     return $result;
    }
    
    
    /**
     * Make API request to Gemini
     * 
     * @param string $prompt
     * @param array $options
     * @return array|false
     */
    private function make_api_request($prompt, $options = []) {
        // Correct API structure for Gemini 2.5 Flash image generation
        $request_data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => 'Generate an image: ' . $prompt
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => $options['temperature'] ?? 0.7,
                'topP' => $options['top_p'] ?? 0.9,
                'topK' => $options['top_k'] ?? 32,
                'maxOutputTokens' => $options['max_tokens'] ?? 4096
            ],
            'safetySettings' => [
                [
                    'category' => 'HARM_CATEGORY_HARASSMENT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ],
                [
                    'category' => 'HARM_CATEGORY_HATE_SPEECH',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ],
                [
                    'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ],
                [
                    'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ]
            ]
        ];
        
        // Log the request for debugging
        error_log('🚀 [GEMINI] Starting API request with prompt: ' . substr($prompt, 0, 100) . '...');
        error_log('🚀 [GEMINI] API Endpoint: ' . self::API_ENDPOINT);
        error_log('🚀 [GEMINI] Request data: ' . json_encode($request_data));
        
        $response = wp_remote_post(self::API_ENDPOINT . '?key=' . $this->api_key, [
            'headers' => [
                'Content-Type' => 'application/json',
                'User-Agent' => 'RepairLift-WP-Customizer/2.0.0'
            ],
            'body' => json_encode($request_data),
            'timeout' => 120, // Increased timeout for image generation
            'sslverify' => true
        ]);
        
        if (is_wp_error($response)) {
            $this->last_error = 'API request failed: ' . $response->get_error_message();
            error_log('❌ [GEMINI] API request failed: ' . $response->get_error_message());
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        // Log the response for debugging
        error_log('📥 [GEMINI] Response Code: ' . $response_code);
        error_log('📥 [GEMINI] Response Body Length: ' . strlen($body) . ' characters');
        error_log('📥 [GEMINI] Response Body Sample: ' . substr($body, 0, 500) . '...');
        
        if ($response_code !== 200) {
            $error_data = json_decode($body, true);
            $error_message = 'API returned error code: ' . $response_code;
            
            if (isset($error_data['error']['message'])) {
                $error_message .= ' - ' . $error_data['error']['message'];
            }
            
            $this->last_error = $error_message;
            return false;
        }
        
        $data = json_decode($body, true);
        
        if (!$data) {
            $this->last_error = 'Failed to parse API response JSON';
            return false;
        }
        
        if (isset($data['error'])) {
            $this->last_error = 'API Error: ' . $data['error']['message'];
            return false;
        }
        
        if (!isset($data['candidates']) || empty($data['candidates'])) {
            $this->last_error = 'No candidates in API response';
            return false;
        }
        
        return $data;
    }
    
    /**
     * Process generated image data and save to filesystem
     * 
     * @param array $api_response
     * @param string $device_type
     * @param string $device_model
     * @param string $style
     * @return array|false
     */
    private function process_generated_image($api_response, $device_type, $device_model, $style) {
        // Log the full response for debugging
        error_log('🔄 [GEMINI] Processing API Response...');
        error_log('🔄 [GEMINI] Response Keys: ' . json_encode(array_keys($api_response)));
        
        // Check multiple possible response structures
        $image_data = null;
        $base64_data = '';
        $mime_type = 'image/jpeg';
        
        // Try different response structures
        if (isset($api_response['candidates'][0]['content']['parts'][0]['inlineData'])) {
            // Standard inline data format
            $image_data = $api_response['candidates'][0]['content']['parts'][0]['inlineData'];
            $mime_type = $image_data['mimeType'] ?? 'image/jpeg';
            $base64_data = $image_data['data'] ?? '';
            error_log('✅ [GEMINI] Found image data in inlineData format');
        } elseif (isset($api_response['candidates'][0]['content']['parts'])) {
            // Check each part for image data
            error_log('🔍 [GEMINI] Searching through ' . count($api_response['candidates'][0]['content']['parts']) . ' parts...');
            foreach ($api_response['candidates'][0]['content']['parts'] as $index => $part) {
                error_log('🔍 [GEMINI] Part ' . $index . ' keys: ' . json_encode(array_keys($part)));
                if (isset($part['inlineData'])) {
                    $image_data = $part['inlineData'];
                    $mime_type = $image_data['mimeType'] ?? 'image/jpeg';
                    $base64_data = $image_data['data'] ?? '';
                    error_log('✅ [GEMINI] Found image data in parts array at index ' . $index);
                    break;
                }
                if (isset($part['fileData'])) {
                    $image_data = $part['fileData'];
                    $mime_type = $image_data['mimeType'] ?? 'image/jpeg';
                    $base64_data = $image_data['fileUri'] ?? '';
                    error_log('✅ [GEMINI] Found image data in fileData format at index ' . $index);
                    break;
                }
            }
        } elseif (isset($api_response['candidates'][0]['output']['parts'][0]['blob'])) {
            // Alternative blob format
            $blob_data = $api_response['candidates'][0]['output']['parts'][0]['blob'];
            $base64_data = $blob_data['data'] ?? '';
            $mime_type = $blob_data['mimeType'] ?? 'image/jpeg';
            error_log('✅ [GEMINI] Found image data in blob format');
        }
        
        if (empty($base64_data)) {
            $this->last_error = 'No image data found in API response. Response structure: ' . json_encode(array_keys($api_response));
            error_log('❌ [GEMINI] No image data found. Full response: ' . json_encode($api_response, JSON_PRETTY_PRINT));
            return false;
        }
        
        error_log('🔄 [GEMINI] Found base64 data, length: ' . strlen($base64_data) . ' characters');
        
        // Handle different data formats
        if (strpos($base64_data, 'data:') === 0) {
            // Data URI format: data:image/jpeg;base64,/9j/4AAQ...
            $data_parts = explode(',', $base64_data, 2);
            if (count($data_parts) === 2) {
                $base64_data = $data_parts[1];
                // Extract mime type from data URI
                preg_match('/data:([^;]+)/', $data_parts[0], $matches);
                if (isset($matches[1])) {
                    $mime_type = $matches[1];
                }
            }
        }
        
        // Decode base64 image data
        error_log('🔄 [GEMINI] Decoding base64 data...');
        $binary_data = base64_decode($base64_data);
        if (!$binary_data) {
            $this->last_error = 'Failed to decode base64 image data. Data length: ' . strlen($base64_data);
            error_log('❌ [GEMINI] Base64 decode failed. Sample data: ' . substr($base64_data, 0, 100) . '...');
            return false;
        }
        
        error_log('✅ [GEMINI] Base64 decoded, binary length: ' . strlen($binary_data) . ' bytes');
        
        // Validate image data
        $image_info = @getimagesizefromstring($binary_data);
        if (!$image_info) {
            $this->last_error = 'Invalid image data received from API';
            error_log('❌ [GEMINI] Invalid image data. Binary length: ' . strlen($binary_data));
            return false;
        }
        
        error_log('✅ [GEMINI] Valid image data: ' . $image_info[0] . 'x' . $image_info[1] . ' pixels');
        
        // Resize image to 768px width if needed
        $binary_data = $this->resize_image_to_768px($binary_data, $image_info);
        if (!$binary_data) {
            error_log('❌ [GEMINI] Failed to resize image to 768px');
            return false;
        }
        
        // Generate unique filename with proper extension
        $timestamp = time();
        $extension = $this->get_extension_from_mime_type($mime_type);
        $filename = "generated-{$timestamp}-{$device_type}-{$style}.{$extension}";
        $subfolder = $this->get_subfolder_for_device_type($device_type);
        $file_path = $this->upload_dir . "hero-section/{$subfolder}/" . $filename;
        
        error_log('🔄 [GEMINI] Saving to: ' . $file_path);
        
        // Ensure directory exists
        $directory = dirname($file_path);
        if (!file_exists($directory)) {
            wp_mkdir_p($directory);
            error_log('📁 [GEMINI] Created directory: ' . $directory);
        }
        
        // Save file
        if (!file_put_contents($file_path, $binary_data)) {
            $this->last_error = 'Failed to save generated image to filesystem at: ' . $file_path;
            error_log('❌ [GEMINI] File save failed. Directory writable: ' . (is_writable(dirname($file_path)) ? 'yes' : 'no'));
            return false;
        }
        
        error_log('✅ [GEMINI] Successfully saved image to: ' . $file_path . ' (Size: ' . filesize($file_path) . ' bytes)');
        
        // Create thumbnail
        $thumbnail_path = $this->create_thumbnail($file_path, $filename);
        
        // Register with WordPress media library
        $attachment_id = $this->register_with_media_library($file_path, $filename, $mime_type);
        
        return [
            'file_path' => $file_path,
            'filename' => $filename,
            'url' => wp_upload_dir()['baseurl'] . "/ai-images/hero-section/{$subfolder}/" . $filename,
            'thumbnail_path' => $thumbnail_path,
            'thumbnail_url' => $thumbnail_path ? wp_upload_dir()['baseurl'] . "/ai-images/thumbnails/" . basename($thumbnail_path) : null,
            'attachment_id' => $attachment_id,
            'device_type' => $device_type,
            'device_model' => $device_model,
            'style' => $style,
            'generated_at' => $timestamp,
            'file_size' => filesize($file_path),
            'dimensions' => $image_info[0] . 'x' . $image_info[1],
            'mime_type' => $mime_type
        ];
    }
    
    /**
     * Get file extension from MIME type
     */
    private function get_extension_from_mime_type($mime_type) {
        $extensions = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/bmp' => 'bmp'
        ];
        
        return $extensions[$mime_type] ?? 'jpg';
    }
    
    /**
     * Get subfolder name for device type
     */
    private function get_subfolder_for_device_type($device_type) {
        $subfolders = [
            'multi_device' => 'composite-arrangements'
        ];
        
        return $subfolders[$device_type] ?? 'composite-arrangements';
    }
    
    /**
     * Create thumbnail for generated image
     * 
     * @param string $source_path
     * @param string $filename
     * @return string|false Thumbnail path or false on failure
     */
    private function create_thumbnail($source_path, $filename) {
        $thumbnail_filename = 'thumb_' . $filename;
        $thumbnail_path = $this->upload_dir . 'thumbnails/' . $thumbnail_filename;
        
        // Use WordPress image functions to create thumbnail
        $image = wp_get_image_editor($source_path);
        if (is_wp_error($image)) {
            return false;
        }
        
        $image->resize(300, 200, true);
        $result = $image->save($thumbnail_path);
        
        if (is_wp_error($result)) {
            return false;
        }
        
        return $thumbnail_path;
    }
    
    /**
     * Register generated image with WordPress media library
     * 
     * @param string $file_path
     * @param string $filename
     * @param string $mime_type
     * @return int|false Attachment ID or false on failure
     */
    private function register_with_media_library($file_path, $filename, $mime_type) {
        $attachment_data = [
            'post_title' => sanitize_file_name(pathinfo($filename, PATHINFO_FILENAME)),
            'post_content' => '',
            'post_status' => 'inherit',
            'post_mime_type' => $mime_type
        ];
        
        $attachment_id = wp_insert_attachment($attachment_data, $file_path);
        
        if (!$attachment_id || is_wp_error($attachment_id)) {
            return false;
        }
        
        // Generate metadata
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attachment_metadata = wp_generate_attachment_metadata($attachment_id, $file_path);
        wp_update_attachment_metadata($attachment_id, $attachment_metadata);
        
        // Add custom metadata
        update_post_meta($attachment_id, '_repairlift_ai_generated', true);
        update_post_meta($attachment_id, '_repairlift_generation_timestamp', time());
        update_post_meta($attachment_id, '_repairlift_device_type', $device_type);
        update_post_meta($attachment_id, '_repairlift_style', $style);
        update_post_meta($attachment_id, '_repairlift_device_type', $device_type);
        update_post_meta($attachment_id, '_repairlift_style', $style);
        
        return $attachment_id;
    }
    
    /**
     * Log generation metadata for tracking and debugging
     */
    private function log_generation_metadata($result, $prompt, $device_type, $device_model, $style, $options) {
        $metadata = [
            'timestamp' => time(),
            'filename' => $result['filename'],
            'prompt' => $prompt,
            'device_type' => $device_type,
            'device_model' => $device_model,
            'style' => $style,
            'options' => $options,
            'file_size' => $result['file_size'],
            'attachment_id' => $result['attachment_id']
        ];
        
        $log_file = $this->upload_dir . 'metadata/generation-logs.json';
        $existing_logs = [];
        
        if (file_exists($log_file)) {
            $existing_logs = json_decode(file_get_contents($log_file), true) ?: [];
        }
        
        $existing_logs[] = $metadata;
        
        // Keep only last 100 entries
        if (count($existing_logs) > 100) {
            $existing_logs = array_slice($existing_logs, -100);
        }
        
        file_put_contents($log_file, json_encode($existing_logs, JSON_PRETTY_PRINT));
    }
    
    /**
     * Get list of generated images
     * 
     * @param array $filters Optional filters
     * @return array
     */
    public function get_generated_images($filters = []) {
        $args = [
            'post_type' => 'attachment',
            'post_status' => 'inherit',
            'posts_per_page' => -1,
            'meta_query' => [
                [
                    'key' => '_repairlift_ai_generated',
                    'value' => '1',
                ]
            ]
        ];

        if (!empty($filters['device_type'])) {
            $args['meta_query'][] = [
                'key' => '_repairlift_device_type',
                'value' => $filters['device_type'],
            ];
        }

        $attachments = get_posts($args);
        $images = [];

        foreach ($attachments as $attachment) {
            $images[] = [
    'attachment_id' => $attachment->ID,
                'filename' => basename(get_attached_file($attachment->ID)),
                'url' => wp_get_attachment_url($attachment->ID),
                'thumbnail_url' => wp_get_attachment_thumb_url($attachment->ID),
                'device_type' => get_post_meta($attachment->ID, '_repairlift_device_type', true),
                'style' => get_post_meta($attachment->ID, '_repairlift_style', true),
                'timestamp' => get_post_meta($attachment->ID, '_repairlift_generation_timestamp', true),
                'file_size' => filesize(get_attached_file($attachment->ID)),
                'created_date' => $attachment->post_date
            ];
        }

        return $images;
    }
    
    /**
     * Parse filename to extract metadata
     */
    private function parse_filename($filename) {
        // Format: generated-{timestamp}-{device_type}-{style}.{extension}
        // Support multiple file extensions
        if (preg_match('/generated-(\d+)-([^-]+)-([^.]+)\.(jpg|jpeg|png|webp|gif)$/', $filename, $matches)) {
            error_log('✅ [GEMINI] Parsed filename: ' . $filename . ' -> device_type: ' . $matches[2] . ', style: ' . $matches[3]);
            return [
                'timestamp' => (int)$matches[1],
                'device_type' => $matches[2],
                'style' => $matches[3],
                'extension' => $matches[4]
            ];
        }
        
        error_log('❌ [GEMINI] Failed to parse filename: ' . $filename);
        return false;
    }
    
    /**
     * Check if file info matches filters
     */
    private function matches_filters($file_info, $filters) {
        foreach ($filters as $key => $value) {
            if (isset($file_info[$key]) && $file_info[$key] !== $value) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Delete generated image
     * 
     * @param string $filename
     * @return bool
     */
    public function delete_generated_image($attachment_id) {
        // Get the file path before deleting the attachment
        $file_path = get_attached_file($attachment_id);
        
        // Delete the WordPress attachment
        if (wp_delete_attachment($attachment_id, true)) {
            // After successful deletion, manually delete the physical files
            if ($file_path && file_exists($file_path)) {
                unlink($file_path);
            }
            
            // Delete the corresponding thumbnail
            $thumbnail_path = $this->upload_dir . 'thumbnails/thumb_' . basename($file_path);
            if (file_exists($thumbnail_path)) {
                unlink($thumbnail_path);
            }
            
            return ['success' => true];
        }
        
        return ['success' => false, 'message' => 'Failed to delete attachment.'];
    }
    
    /**
     * Generate hero image - wrapper for generate_device_image
     *
     * @param string $device_type
     * @param string $device_model
     * @param string $style
     * @param float $temperature
     * @param string $business_name
     * @return array
     */
    public function generate_hero_image($prompt, $temperature = 0.7) {
           $options = [
               'temperature' => $temperature,
           ];
   
           $result = $this->generate_device_image($prompt, $options);
   
           if ($result) {
               return [
                   'success' => true,
                   'image_data' => $result,
                   'message' => 'Image generated successfully'
               ];
           } else {
               return [
                   'success' => false,
                   'message' => $this->get_last_error()
               ];
           }
       }
    
    /**
     * Get generated images with success wrapper for AJAX
     *
     * @return array
     */
    public function get_generated_images_response() {
        $images = $this->get_generated_images();
        
        return [
            'success' => true,
            'images' => $images
        ];
    }
    
    /**
     * Delete generated image with success wrapper for AJAX
     *
     * @param string $filename
     * @return array
     */
    public function delete_generated_image_response($filename) {
        $result = $this->delete_generated_image($filename);
        
        if ($result) {
            return [
                'success' => true,
                'message' => 'Image deleted successfully'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to delete image'
            ];
        }
    }
    
    /**
     * AJAX handler for generating hero images
     */
    public function ajax_generate_hero_image() {
        check_ajax_referer('website_generator_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access');
        }

        $prompt = wp_unslash($_POST['prompt'] ?? '');
        if (empty($prompt)) {
            wp_send_json_error('Prompt cannot be empty.');
        }

        $device_type = sanitize_text_field($_POST['device_type'] ?? 'undefined');
        $device_model = sanitize_text_field($_POST['device_model'] ?? 'undefined');
        $style = sanitize_text_field($_POST['lighting_style'] ?? 'undefined');
        
        $options = [
            'temperature' => floatval($_POST['temperature'] ?? 0.7),
        ];
        
        $result = $this->generate_device_image($prompt, $device_type, $device_model, $style, $options);
        
        if ($result) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($this->get_last_error());
        }
    }
    
    /**
     * AJAX handler for getting generated images list
     */
    public function ajax_get_generated_images() {
        check_ajax_referer('website_generator_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access');
        }
        
        error_log('🔄 [GEMINI] Getting generated images list...');
        
        $filters = [];
        if (!empty($_POST['device_type'])) {
            $filters['device_type'] = sanitize_text_field($_POST['device_type']);
        }
        
        $images = $this->get_generated_images($filters);
        error_log('✅ [GEMINI] Found ' . count($images) . ' images');
        
        wp_send_json_success($images);
    }
    
    /**
     * AJAX handler for deleting generated images
     */
    public function ajax_delete_generated_image() {
        check_ajax_referer('website_generator_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access');
        }
        
        $attachment_id = absint($_POST['attachment_id'] ?? 0);
        
        if (empty($attachment_id)) {
            wp_send_json_error('Attachment ID is required.');
        }
        
        $result = $this->delete_generated_image($attachment_id);
        
        if ($result['success']) {
            wp_send_json_success(['message' => 'Image and its files deleted successfully']);
        } else {
            wp_send_json_error(['message' => $result['message']]);
        }
    }
    
    /**
     * Get the last error message
     * 
     * @return string
     */
    public function get_last_error() {
        return $this->last_error;
    }
    
    /**
     * Set Gemini API key
     * 
     * @param string $api_key
     */
    public function set_api_key($api_key) {
        $this->api_key = $api_key;
        update_option('repairlift_gemini_api_key', $api_key);
    }
    
    /**
     * Get available prompt templates
     * 
     * @return array
     */
    public function get_prompt_templates() {
        return $this->prompt_templates;
    }
    
    /**
     * Add custom prompt template
     * 
     * @param string $key
     * @param string $template
     */
    public function add_prompt_template($key, $template) {
        $this->prompt_templates[$key] = $template;
    }
    
    /**
     * Resize image to 768px width maintaining aspect ratio
     *
     * @param string $binary_data
     * @param array $image_info
     * @return string|false
     */
    private function resize_image_to_768px($binary_data, $image_info) {
        $original_width = $image_info[0];
        $original_height = $image_info[1];
        
        // If image is already 768px or smaller, return as-is
        if ($original_width <= 768) {
            error_log('✅ [GEMINI] Image is already 768px or smaller (' . $original_width . 'x' . $original_height . ')');
            return $binary_data;
        }
        
        // Calculate new dimensions
        $new_width = 768;
        $new_height = (int)(($original_height / $original_width) * $new_width);
        
        error_log('🔄 [GEMINI] Resizing from ' . $original_width . 'x' . $original_height . ' to ' . $new_width . 'x' . $new_height);
        
        // Create image resource from string
        $source = imagecreatefromstring($binary_data);
        if (!$source) {
            error_log('❌ [GEMINI] Failed to create image resource from string');
            return false;
        }
        
        // Create new image
        $target = imagecreatetruecolor($new_width, $new_height);
        
        // Preserve transparency for PNG
        if ($image_info[2] === IMAGETYPE_PNG) {
            imagealphablending($target, false);
            imagesavealpha($target, true);
            $transparent = imagecolorallocatealpha($target, 255, 255, 255, 127);
            imagefilledrectangle($target, 0, 0, $new_width, $new_height, $transparent);
        }
        
        // Resize
        if (!imagecopyresampled($target, $source, 0, 0, 0, 0, $new_width, $new_height, $original_width, $original_height)) {
            error_log('❌ [GEMINI] Failed to resize image');
            imagedestroy($source);
            imagedestroy($target);
            return false;
        }
        
        // Save to string
        ob_start();
        switch ($image_info[2]) {
            case IMAGETYPE_PNG:
                imagepng($target, null, 6); // PNG compression level 6
                break;
            case IMAGETYPE_GIF:
                imagegif($target);
                break;
            case IMAGETYPE_WEBP:
                imagewebp($target, null, 80); // WebP quality 80
                break;
            default:
                imagejpeg($target, null, 85); // JPEG quality 85
                break;
        }
        $resized_data = ob_get_contents();
        ob_end_clean();
        
        // Clean up
        imagedestroy($source);
        imagedestroy($target);
        
        if (!$resized_data) {
            error_log('❌ [GEMINI] Failed to get resized image data');
            return false;
        }
        
        error_log('✅ [GEMINI] Successfully resized image. New size: ' . strlen($resized_data) . ' bytes');
        return $resized_data;
    }
    
    /**
     * AJAX handler for saving Gemini API key
     */
    public function ajax_save_gemini_api_key() {
        check_ajax_referer('website_generator_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access');
        }
        
        $api_key = sanitize_text_field($_POST['gemini_api_key'] ?? '');
        
        if (empty($api_key)) {
            wp_send_json_error('API key is required');
        }
        
        // Validate API key format (basic validation)
        if (!preg_match('/^[A-Za-z0-9\-_]+$/', $api_key)) {
            wp_send_json_error('Invalid API key format');
        }
        
        // Save API key
        update_option('repairlift_gemini_api_key', $api_key);
        $this->api_key = $api_key;
        
        wp_send_json_success('Gemini API key saved successfully');
    }
    
    /**
     * AJAX handler for testing Gemini API connection
     */
    public function ajax_test_gemini_connection() {
        check_ajax_referer('website_generator_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access');
        }
        
        $api_key = sanitize_text_field($_POST['gemini_api_key'] ?? '');
        
        if (empty($api_key)) {
            wp_send_json_error('API key is required');
        }
        
        // Temporarily set API key for testing
        $original_api_key = $this->api_key;
        $this->api_key = $api_key;
        
        // Make a simple test request
        $test_prompt = 'Generate a simple test image: a blue circle on white background';
        
        $request_data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => 'Generate an image: ' . $test_prompt
                        ]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.5,
                'maxOutputTokens' => 100
            ]
        ];
        
        $response = wp_remote_post(self::API_ENDPOINT . '?key=' . $api_key, [
            'headers' => [
                'Content-Type' => 'application/json',
                'User-Agent' => 'RepairLift-WP-Customizer/2.0.0'
            ],
            'body' => json_encode($request_data),
            'timeout' => 30,
            'sslverify' => true
        ]);
        
        // Restore original API key
        $this->api_key = $original_api_key;
        
        if (is_wp_error($response)) {
            wp_send_json_error('Connection failed: ' . $response->get_error_message());
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($response_code === 200) {
            wp_send_json_success([
                'message' => 'Connection successful',
                'model' => 'gemini-2.5-flash-image-preview'
            ]);
        } else {
            $error_data = json_decode($body, true);
            $error_message = 'HTTP ' . $response_code;
            
            if (isset($error_data['error']['message'])) {
                $error_message .= ': ' . $error_data['error']['message'];
            }
            
            wp_send_json_error($error_message);
        }
    }
    
    /**
     * AJAX handler for getting current hero image
     */
    public function ajax_get_current_hero_image() {
        check_ajax_referer('website_generator_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access');
        }
        
        error_log('🖼️ [GEMINI] Getting current hero image...');
        
        // Try to get current hero image from WordPress theme customizer
        $hero_image_id = get_theme_mod('custom_logo', 0);
        
        // If no custom logo, try to get site icon
        if (!$hero_image_id) {
            $hero_image_id = get_option('site_icon', 0);
        }
        
        // If still no image, try to find hero image in theme options
        if (!$hero_image_id) {
            $hero_image_id = get_option('repairlift_hero_image_id', 0);
        }
        
        if ($hero_image_id) {
            $image_url = wp_get_attachment_image_url($hero_image_id, 'full');
            $image_meta = wp_get_attachment_metadata($hero_image_id);
            $image_title = get_the_title($hero_image_id);
            
            if ($image_url) {
                error_log('✅ [GEMINI] Found hero image: ' . $image_url);
                
                $hero_data = [
                    'image_url' => $image_url,
                    'image_title' => $image_title,
                    'attachment_id' => $hero_image_id
                ];
                
                if ($image_meta && isset($image_meta['width'], $image_meta['height'])) {
                    $hero_data['image_dimensions'] = $image_meta['width'] . 'x' . $image_meta['height'];
                }
                
                if ($image_meta && isset($image_meta['filesize'])) {
                    $hero_data['image_size'] = size_format($image_meta['filesize']);
                } else {
                    $file_path = get_attached_file($hero_image_id);
                    if ($file_path && file_exists($file_path)) {
                        $hero_data['image_size'] = size_format(filesize($file_path));
                    }
                }
                
                wp_send_json_success($hero_data);
            }
        }
        
        error_log('⚠️ [GEMINI] No hero image found');
        
        // If no hero image found, return empty state
        wp_send_json_success([
            'image_url' => null,
            'image_title' => null,
            'message' => 'No hero image currently set'
        ]);
    }
}