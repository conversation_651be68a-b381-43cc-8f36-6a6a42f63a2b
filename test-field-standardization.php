<?php
/**
 * Test Field Name Standardization
 * Verifies that the field name changes are working correctly
 */

echo "<h2>🧪 Field Name Standardization Test</h2>\n";

// Test 1: Check field mapping configuration
echo "<h3>1. Testing Field Mapping Configuration</h3>\n";

// Mock the WordPress functions for testing
if (!function_exists('sanitize_text_field')) {
    function sanitize_text_field($str) {
        return trim(strip_tags($str));
    }
}

if (!function_exists('esc_html')) {
    function esc_html($str) {
        return htmlspecialchars($str, ENT_QUOTES, 'UTF-8');
    }
}

require_once 'includes/airtable-field-mapper.php';
$field_mapper = new AirtableFieldMapper();

// Test data with standardized field names
$test_data = array(
    'hero_title' => 'Test Hero Title from Airtable',
    'hero_tagline' => 'Test Hero Tagline from Airtable',
    'business_name' => 'Test Repair Shop'
);

echo "📝 Input data:\n";
foreach ($test_data as $key => $value) {
    echo "   - $key: $value\n";
}

try {
    $mapped_data = $field_mapper->map_airtable_to_wordpress($test_data);
    
    echo "\n📤 Mapped data:\n";
    if (isset($mapped_data['visual_blocks'])) {
        foreach ($mapped_data['visual_blocks'] as $key => $value) {
            echo "   - $key: $value\n";
        }
    }
    
    // Check if hero fields are mapped correctly
    if (isset($mapped_data['visual_blocks']['hero_title']) && 
        isset($mapped_data['visual_blocks']['hero_tagline'])) {
        echo "\n✅ Field mapping successful!\n";
        echo "   - hero_title: " . $mapped_data['visual_blocks']['hero_title'] . "\n";
        echo "   - hero_tagline: " . $mapped_data['visual_blocks']['hero_tagline'] . "\n";
    } else {
        echo "\n❌ Field mapping failed\n";
        print_r($mapped_data);
    }
    
} catch (Exception $e) {
    echo "\n❌ Error in field mapping: " . $e->getMessage() . "\n";
}

// Test 2: Verify no field conversion is happening
echo "\n<h3>2. Testing Direct Field Name Mapping</h3>\n";

$direct_test = array(
    'hero_title' => 'Direct Hero Title',
    'hero_tagline' => 'Direct Hero Tagline'
);

echo "📝 Testing direct field names (no conversion should happen):\n";
foreach ($direct_test as $key => $value) {
    echo "   Input: $key → Expected Output: $key\n";
}

try {
    $direct_mapped = $field_mapper->map_airtable_to_wordpress($direct_test);
    
    if (isset($direct_mapped['visual_blocks']['hero_title']) && 
        isset($direct_mapped['visual_blocks']['hero_tagline'])) {
        
        $input_title = $direct_test['hero_title'];
        $output_title = $direct_mapped['visual_blocks']['hero_title'];
        $input_tagline = $direct_test['hero_tagline'];
        $output_tagline = $direct_mapped['visual_blocks']['hero_tagline'];
        
        if ($input_title === $output_title && $input_tagline === $output_tagline) {
            echo "\n✅ Direct mapping successful - no field conversion!\n";
            echo "   - hero_title: $input_title → $output_title ✓\n";
            echo "   - hero_tagline: $input_tagline → $output_tagline ✓\n";
        } else {
            echo "\n⚠️ Field conversion detected:\n";
            echo "   - hero_title: $input_title → $output_title\n";
            echo "   - hero_tagline: $input_tagline → $output_tagline\n";
        }
    } else {
        echo "\n❌ Direct mapping failed\n";
    }
    
} catch (Exception $e) {
    echo "\n❌ Error in direct mapping: " . $e->getMessage() . "\n";
}

// Test 3: Summary
echo "\n<h3>3. Standardization Summary</h3>\n";
echo "✅ Changes implemented:\n";
echo "   - Airtable columns renamed: 'Hero Heading' → 'hero_title', 'Hero Tagline' → 'hero_tagline'\n";
echo "   - Plugin field mapping updated to use direct names\n";
echo "   - Admin interface updated to use 'hero_title' instead of 'hero_heading'\n";
echo "   - Test files updated with standardized field names\n";
echo "   - Documentation updated\n";

echo "\n🎯 Result: Clean 1:1 field mapping with no conversion complexity!\n";
echo "\n<h3>✅ Field Name Standardization Test Complete</h3>\n";
?>
