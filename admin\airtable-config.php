<?php
/**
 * Airtable Configuration Admin Interface
 * 
 * Admin interface for managing Airtable integration settings
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Get current configuration
$airtable_enabled = get_option('airtable_integration_enabled', false);
$airtable_api_key = get_option('airtable_api_key', '');
$airtable_webhook_secret = get_option('airtable_webhook_secret', '');
$airtable_site_identifier = get_option('airtable_site_identifier', '');
$airtable_rate_limit = get_option('airtable_rate_limit', 60);

// Generate webhook URL
$webhook_url = admin_url('admin-ajax.php?action=airtable_webhook');
?>

<div class="airtable-config-container">
    <h2><span class="dashicons dashicons-admin-settings"></span> Airtable Integration Configuration</h2>
    
    <!-- Configuration Status -->
    <div class="airtable-status-card">
        <h3>Integration Status</h3>
        <div class="status-indicators">
            <div class="status-item">
                <span class="status-label">Integration:</span>
                <span class="status-value <?php echo $airtable_enabled ? 'enabled' : 'disabled'; ?>">
                    <?php echo $airtable_enabled ? 'Enabled' : 'Disabled'; ?>
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">API Key:</span>
                <span class="status-value <?php echo !empty($airtable_api_key) ? 'configured' : 'not-configured'; ?>">
                    <?php echo !empty($airtable_api_key) ? 'Configured' : 'Not Configured'; ?>
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">Webhook Secret:</span>
                <span class="status-value <?php echo !empty($airtable_webhook_secret) ? 'configured' : 'not-configured'; ?>">
                    <?php echo !empty($airtable_webhook_secret) ? 'Configured' : 'Optional'; ?>
                </span>
            </div>
        </div>
    </div>

    <!-- Configuration Form -->
    <form id="airtable-config-form" method="post">
        <?php wp_nonce_field('airtable_config_nonce', 'airtable_config_nonce'); ?>
        
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="airtable_integration_enabled">Enable Airtable Integration</label>
                </th>
                <td>
                    <label class="switch">
                        <input type="checkbox" id="airtable_integration_enabled" name="airtable_integration_enabled" 
                               value="1" <?php checked($airtable_enabled); ?>>
                        <span class="slider"></span>
                    </label>
                    <p class="description">Enable or disable Airtable webhook integration</p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="airtable_api_key">API Key</label>
                </th>
                <td>
                    <div class="api-key-container">
                        <input type="password" id="airtable_api_key" name="airtable_api_key" 
                               class="regular-text" value="<?php echo esc_attr($airtable_api_key); ?>"
                               placeholder="Enter your Airtable API key">
                        <button type="button" class="button toggle-visibility" data-target="airtable_api_key">
                            <span class="dashicons dashicons-visibility"></span>
                        </button>
                    </div>
                    <p class="description">
                        API key for authenticating webhook requests. Generate a unique key for this site.
                        <br><strong>Recommended:</strong> Use a UUID or random string (32+ characters)
                    </p>
                    <button type="button" class="button button-secondary" id="generate-api-key">
                        Generate Random API Key
                    </button>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="airtable_webhook_secret">Webhook Secret</label>
                </th>
                <td>
                    <div class="api-key-container">
                        <input type="password" id="airtable_webhook_secret" name="airtable_webhook_secret" 
                               class="regular-text" value="<?php echo esc_attr($airtable_webhook_secret); ?>"
                               placeholder="Optional: Enter webhook signature secret">
                        <button type="button" class="button toggle-visibility" data-target="airtable_webhook_secret">
                            <span class="dashicons dashicons-visibility"></span>
                        </button>
                    </div>
                    <p class="description">
                        Optional: Secret for webhook signature validation (recommended for production)
                    </p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="airtable_site_identifier">Site Identifier</label>
                </th>
                <td>
                    <input type="text" id="airtable_site_identifier" name="airtable_site_identifier" 
                           class="regular-text" value="<?php echo esc_attr($airtable_site_identifier); ?>"
                           placeholder="e.g., repair-shop-miami">
                    <p class="description">
                        Unique identifier for this WordPress site. Must match the Site Identifier in Airtable forms.
                        <br><strong>Format:</strong> lowercase letters, numbers, and hyphens only
                    </p>
                </td>
            </tr>
            
            <tr>
                <th scope="row">
                    <label for="airtable_rate_limit">Rate Limit</label>
                </th>
                <td>
                    <input type="number" id="airtable_rate_limit" name="airtable_rate_limit" 
                           class="small-text" value="<?php echo esc_attr($airtable_rate_limit); ?>"
                           min="0" max="1000">
                    <span>requests per hour</span>
                    <p class="description">
                        Maximum webhook requests per hour (0 = no limit). Recommended: 60 for production.
                    </p>
                </td>
            </tr>
        </table>
        
        <div class="form-actions">
            <button type="submit" class="button button-primary" id="save-airtable-config">
                <span class="dashicons dashicons-yes"></span>
                Save Configuration
            </button>
            <button type="button" class="button button-secondary" id="test-webhook">
                <span class="dashicons dashicons-admin-tools"></span>
                Test Webhook
            </button>
        </div>
    </form>

    <!-- Webhook Information -->
    <div class="webhook-info-card">
        <h3>Webhook Information</h3>
        <div class="webhook-details">
            <div class="webhook-item">
                <label>Webhook URL:</label>
                <div class="webhook-url-container">
                    <input type="text" readonly value="<?php echo esc_attr($webhook_url); ?>" class="webhook-url">
                    <button type="button" class="button copy-webhook-url" data-url="<?php echo esc_attr($webhook_url); ?>">
                        <span class="dashicons dashicons-admin-page"></span>
                        Copy
                    </button>
                </div>
            </div>
            <div class="webhook-item">
                <label>Method:</label>
                <span class="webhook-method">POST</span>
            </div>
            <div class="webhook-item">
                <label>Content-Type:</label>
                <span class="webhook-content-type">application/json</span>
            </div>
            <div class="webhook-item">
                <label>Authentication:</label>
                <span class="webhook-auth">API Key in request body or X-API-Key header</span>
            </div>
        </div>
    </div>

    <!-- Airtable Setup Instructions -->
    <div class="setup-instructions-card">
        <h3>Airtable Setup Instructions</h3>
        <div class="instructions-content">
            <ol>
                <li>
                    <strong>Create Airtable Base:</strong>
                    <p>Use the provided Airtable base template with all required fields</p>
                </li>
                <li>
                    <strong>Configure Form:</strong>
                    <p>Create an Airtable form with all business info and content fields</p>
                </li>
                <li>
                    <strong>Set up Webhook:</strong>
                    <p>Configure Airtable automation to send webhook to the URL above when form is submitted</p>
                </li>
                <li>
                    <strong>Add Authentication:</strong>
                    <p>Include the API key in webhook payload or headers for security</p>
                </li>
                <li>
                    <strong>Test Integration:</strong>
                    <p>Use the "Test Webhook" button to verify connectivity</p>
                </li>
            </ol>
        </div>
    </div>

    <!-- Integration Testing -->
    <div class="testing-tools-card">
        <h3>Integration Testing & Validation</h3>
        <div class="testing-container">
            <div class="testing-header">
                <p>Run comprehensive tests to validate your Airtable integration setup.</p>
                <button type="button" class="button button-primary" id="run-test-suite">
                    <span class="dashicons dashicons-admin-tools"></span>
                    Run Full Test Suite
                </button>
            </div>

            <!-- Database Diagnostic Tool -->
            <div class="diagnostic-section">
                <h4>Database Diagnostic</h4>
                <p>Check what data is currently stored in the WordPress database:</p>
                <button type="button" class="button button-secondary" id="check-database-data">
                    <span class="dashicons dashicons-database"></span>
                    Check Current Database Values
                </button>
                <button type="button" class="button button-primary" id="test-block-manager" style="margin-left: 10px;">
                    <span class="dashicons dashicons-admin-tools"></span>
                    🧪 Test Block Manager
                </button>
                <div id="database-diagnostic-results" style="margin-top: 15px; padding: 15px; background: #f9f9f9; border: 1px solid #ddd; display: none; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto;">
                    <!-- Results will be shown here -->
                </div>
            </div>
            <div id="test-results-content">
                <!-- Test results will be loaded via AJAX -->
                <p>Click "Run Full Test Suite" to validate your integration setup.</p>
            </div>
        </div>
    </div>

    <!-- Recent Webhook Logs -->
    <div class="webhook-logs-card">
        <h3>Recent Webhook Activity</h3>
        <div class="logs-container">
            <div class="logs-header">
                <button type="button" class="button button-secondary" id="refresh-logs">
                    <span class="dashicons dashicons-update"></span>
                    Refresh Logs
                </button>
                <button type="button" class="button button-secondary" id="clear-logs">
                    <span class="dashicons dashicons-trash"></span>
                    Clear Logs
                </button>
            </div>
            <div id="webhook-logs-content">
                <!-- Logs will be loaded via AJAX -->
                <p>Loading webhook logs...</p>
            </div>
        </div>
    </div>
</div>

<style>
.airtable-config-container {
    max-width: 1000px;
    margin: 20px 0;
}

.airtable-status-card,
.webhook-info-card,
.setup-instructions-card,
.webhook-logs-card,
.testing-tools-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.status-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.status-value.enabled,
.status-value.configured {
    color: #46b450;
    font-weight: 600;
}

.status-value.disabled,
.status-value.not-configured {
    color: #dc3232;
    font-weight: 600;
}

.api-key-container {
    display: flex;
    gap: 5px;
    align-items: center;
}

.toggle-visibility {
    padding: 6px 10px;
    border: 1px solid #ddd;
    background: #f7f7f7;
    cursor: pointer;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.webhook-url-container {
    display: flex;
    gap: 5px;
    align-items: center;
}

.webhook-url {
    flex: 1;
    font-family: monospace;
    background: #f9f9f9;
    border: 1px solid #ddd;
    padding: 8px;
    border-radius: 4px;
}

.form-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

.instructions-content ol {
    margin-left: 20px;
}

.instructions-content li {
    margin-bottom: 15px;
}

.logs-header {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

#webhook-logs-content {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #f9f9f9;
    font-family: monospace;
    font-size: 12px;
}

/* Testing Tools Styles */
.testing-header {
    margin-bottom: 20px;
}

.testing-header p {
    margin-bottom: 15px;
    color: #666;
}

#test-results-content {
    border: 1px solid #ddd;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
    min-height: 100px;
}

.test-results {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.test-summary {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border-left: 4px solid #ddd;
}

.test-summary.test-excellent {
    background: #d4edda;
    border-left-color: #28a745;
}

.test-summary.test-good {
    background: #d1ecf1;
    border-left-color: #17a2b8;
}

.test-summary.test-fair {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.test-summary.test-poor {
    background: #f8d7da;
    border-left-color: #dc3545;
}

.test-stats {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.test-stats .stat {
    font-size: 14px;
    font-weight: 600;
    color: #555;
}

.test-category {
    margin-bottom: 25px;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    overflow: hidden;
}

.test-category h4 {
    background: #f8f9fa;
    margin: 0;
    padding: 12px 15px;
    border-bottom: 1px solid #e1e1e1;
    font-size: 14px;
    font-weight: 600;
}

.test-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f1f1f1;
}

.test-item:last-child {
    border-bottom: none;
}

.test-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.test-icon {
    font-weight: bold;
    font-size: 16px;
}

.test-item.test-success .test-icon {
    color: #28a745;
}

.test-item.test-warning .test-icon {
    color: #ffc107;
}

.test-item.test-error .test-icon {
    color: #dc3545;
}

.test-name {
    flex: 1;
    font-weight: 600;
    font-size: 13px;
}

.test-status {
    font-size: 11px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    background: #e9ecef;
    color: #495057;
}

.test-item.test-success .test-status {
    background: #28a745;
    color: white;
}

.test-item.test-warning .test-status {
    background: #ffc107;
    color: #212529;
}

.test-item.test-error .test-status {
    background: #dc3545;
    color: white;
}

.test-message {
    font-size: 12px;
    color: #666;
    margin-left: 26px;
}

.test-recommendations {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #007cba;
}

.test-recommendations h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #007cba;
}

.test-recommendations ul {
    margin: 0;
    padding-left: 20px;
}

.test-recommendations li {
    margin-bottom: 8px;
    font-size: 13px;
}

.recommendation.critical {
    color: #dc3545;
    font-weight: 600;
}

.recommendation.normal {
    color: #495057;
}
</style>
