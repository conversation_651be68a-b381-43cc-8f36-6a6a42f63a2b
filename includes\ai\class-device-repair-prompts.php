<?php

/**
 * Device Repair Prompt Templates
 * 
 * Specialized prompt templates for device repair store content generation
 * 
 * @package WebsiteGenerator
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class DeviceRepairPrompts {
    
    /**
     * Get all default prompt templates for device repair stores
     */
    public static function get_default_prompts() {
        return array(
            
            // HERO SECTION PROMPTS
            'hero_heading' => array(
                'name' => 'Hero Heading',
                'content_type' => 'hero',
                'template' => "You are creating a hero heading for {business_name}, a device repair store in {city}, {state}.

Business Context:
- Devices repaired: {devices_repaired}
- Key benefits: {key_benefits}
- Turnaround time: {turnaround_time}
- Years in business: {years_experience}

CRITICAL CHARACTER LIMIT: MAXIMUM 78 CHARACTERS TOTAL
Current heading: {current_hero_heading} (length: " . strlen('{current_hero_heading}') . " characters)

MANDATORY INSTRUCTIONS:
1. Count every single character as you write (letters, spaces, punctuation)
2. STOP writing when you reach 78 characters - do not exceed this limit
3. Create a complete, grammatically correct heading within this limit
4. Do not write anything after the heading - just the heading text

EXAMPLES WITH EXACT CHARACTER COUNTS:
- 'Expert Device Repair in Miami, FL' (33 characters) ✓ GOOD
- 'Fast Phone & Tablet Repair Services' (35 characters) ✓ GOOD
- 'Professional Device Repair in Downtown Tampa' (44 characters) ✓ GOOD
- 'Same-Day Device Repair in {city}' (varies by city length) ✓ GOOD
- 'Quick & Reliable Device Repair Solutions' (40 characters) ✓ GOOD

Content Requirements:
- Include location reference (city/state)
- Mention device repair or service type
- Professional and trustworthy tone
- Action-oriented language
- Focus on speed, expertise, or reliability

RESPONSE FORMAT: Provide ONLY the heading text, nothing else. Count characters as you write and ensure you stay under 78 characters total.",
                'variables' => array('business_name', 'city', 'state', 'devices_repaired', 'key_benefits', 'turnaround_time', 'years_experience', 'current_hero_heading'),
                'is_active' => true,
                'is_default' => true
            ),
            
            'hero_tagline' => array(
                'name' => 'Hero Tagline',
                'content_type' => 'hero',
                'template' => "You are creating a device tagline for {business_name} that lists repair services.

Devices repaired: {devices_repaired}
Specializations: {specializations}

CRITICAL CHARACTER LIMIT: MAXIMUM 86 CHARACTERS TOTAL
Current tagline: {current_hero_tagline} (length: " . strlen('{current_hero_tagline}') . " characters)

MANDATORY INSTRUCTIONS:
1. Count every character as you write (letters, spaces, pipes, punctuation)
2. STOP writing when you reach 86 characters - do not exceed this limit
3. Use the exact format: 'Device1 | Device2 | Device3 | & More'
4. Each pipe separator ' | ' counts as 3 characters (space-pipe-space)

EXAMPLES WITH EXACT CHARACTER COUNTS:
- 'Smartphones | Tablets | Computers | & More' (42 characters) ✓ GOOD
- 'iPhones | iPads | MacBooks | Gaming Consoles | & More' (53 characters) ✓ GOOD
- 'Phone Repair | Tablet Repair | Computer Repair | & More' (55 characters) ✓ GOOD
- 'Cell Phones | Tablets | Laptops | Game Systems | Watches | & More' (66 characters) ✓ GOOD

CHARACTER COUNTING GUIDE:
- 'Smartphones | ' = 14 characters
- 'Tablets | ' = 10 characters
- 'Computers | ' = 12 characters
- '& More' = 6 characters
- Total example: 14 + 10 + 12 + 6 = 42 characters

Content Requirements:
- Include main device categories from the devices_repaired list
- Use ' | ' (space-pipe-space) as separators
- End with '& More' if listing multiple categories
- Keep device names concise (use 'Phones' not 'Smartphones' if needed for space)
- Maximum 6 categories including '& More'

RESPONSE FORMAT: Provide ONLY the tagline text, nothing else. Count characters as you write and ensure you stay under 86 characters total.",
                'variables' => array('business_name', 'devices_repaired', 'specializations', 'current_hero_tagline'),
                'is_active' => true,
                'is_default' => true
            ),
            
            // ABOUT SECTION PROMPTS
            'about_title' => array(
                'name' => 'About Section Title',
                'content_type' => 'about',
                'template' => "You are creating a section title for {business_name}'s 'About' or 'One-Stop Shop' section.

Business Information:
- Years in business: {years_experience}
- Specializations: {specializations}
- Key benefits: {key_benefits}

CRITICAL CHARACTER LIMIT: MAXIMUM 64 CHARACTERS TOTAL
Current title: {current_about_title} (length: " . strlen('{current_about_title}') . " characters)

MANDATORY INSTRUCTIONS:
1. Count every single character as you write (letters, spaces, punctuation)
2. STOP writing when you reach 64 characters - do not exceed this limit
3. Create a complete, grammatically correct title within this limit
4. Do not write anything after the title - just the title text

EXAMPLES WITH EXACT CHARACTER COUNTS:
- 'Your One-Stop Shop Repair Store' (32 characters) ✓ GOOD
- 'Complete Device Repair Solutions' (33 characters) ✓ GOOD
- 'All Your Device Repair Needs' (28 characters) ✓ GOOD
- 'Professional Repair Services Hub' (32 characters) ✓ GOOD
- 'Comprehensive Device Repair Center' (34 characters) ✓ GOOD
- 'Expert Repair Solutions for All Devices' (39 characters) ✓ GOOD

Content Requirements:
- Focus on comprehensive/complete service offering
- Emphasize convenience and one-stop solution
- Professional and trustworthy tone
- Can reference 'one-stop', 'complete', 'all', or 'comprehensive'
- Should convey breadth of services

RESPONSE FORMAT: Provide ONLY the section title text, nothing else. Count characters as you write and ensure you stay under 64 characters total.",
                'variables' => array('business_name', 'years_experience', 'specializations', 'key_benefits', 'current_about_title'),
                'is_active' => true,
                'is_default' => true
            ),

            'about_description' => array(
                'name' => 'About Description',
                'content_type' => 'about',
                'template' => "You are writing a professional description for {business_name}'s 'One-Stop Shop' section.

Business Information:
- Years in business: {years_experience}
- Specializations: {specializations}
- Warranty offered: {warranty_offered}
- Key benefits: {key_benefits}

CRITICAL CHARACTER LIMIT: MAXIMUM 300 CHARACTERS TOTAL
Current description: {current_about_description} (length: " . strlen('{current_about_description}') . " characters)

MANDATORY INSTRUCTIONS:
1. Count every single character as you write (letters, spaces, punctuation)
2. STOP writing when you reach 300 characters - do not exceed this limit
3. Write 2-3 complete sentences within this limit
4. Do not write anything after the description - just the description text

EXAMPLES WITH EXACT CHARACTER COUNTS:
- 'With over 10 years of experience, we provide expert device repair services with genuine parts and warranty coverage. Our certified technicians specialize in same-day repairs for all major brands.' (198 characters) ✓ GOOD

- 'We are your trusted local repair experts, offering comprehensive device repair services with a 90-day warranty. Our experienced team provides fast, reliable repairs using quality parts.' (186 characters) ✓ GOOD

CHARACTER COUNTING GUIDE:
- Average sentence: 60-80 characters
- 2 sentences: 120-160 characters ✓ GOOD
- 3 sentences: 180-240 characters ✓ GOOD
- Maximum allowed: 300 characters

Content Requirements:
- 2-3 complete sentences maximum
- Mention experience/expertise if available
- Include warranty information if provided
- Professional and trustworthy tone
- Focus on customer benefits and quality
- Avoid overly technical language

RESPONSE FORMAT: Provide ONLY the description text, nothing else. Count characters as you write and ensure you stay under 300 characters total.",
                'variables' => array('business_name', 'years_experience', 'specializations', 'warranty_offered', 'key_benefits', 'current_about_description'),
                'is_active' => true,
                'is_default' => true
            ),
            
            // FEATURES SECTION PROMPTS
            'feature_customer_service' => array(
                'name' => 'Customer Service Feature',
                'content_type' => 'features',
                'template' => "Write a description for the 'Premier Customer Service' feature of {business_name}.

Business Context:
- Key benefits: {key_benefits}
- Warranty offered: {warranty_offered}
- Years in business: {years_experience}

Requirements:
- 1-2 sentences
- Focus on customer satisfaction and quality
- Mention warranty if applicable
- Professional and confident tone
- Emphasize reliability and trust

Current description: {current_customer_service_feature}

Generate a new description:",
                'variables' => array('business_name', 'key_benefits', 'warranty_offered', 'years_experience', 'current_customer_service_feature'),
                'is_active' => true,
                'is_default' => true
            ),
            
            'feature_quick_turnaround' => array(
                'name' => 'Quick Turnaround Feature',
                'content_type' => 'features',
                'template' => "Write a description for the 'Quick Turnaround' feature of {business_name}.

Business Context:
- Turnaround time: {turnaround_time}
- Key benefits: {key_benefits}
- Specializations: {specializations}

Requirements:
- 1-2 sentences
- Emphasize speed without sacrificing quality
- Mention typical turnaround time if provided
- Professional tone
- Focus on efficiency and expertise

Current description: {current_quick_turnaround_feature}

Generate a new description:",
                'variables' => array('business_name', 'turnaround_time', 'key_benefits', 'specializations', 'current_quick_turnaround_feature'),
                'is_active' => true,
                'is_default' => true
            ),
            
            'feature_price_guarantee' => array(
                'name' => 'Price Guarantee Feature',
                'content_type' => 'features',
                'template' => "Write a description for the 'Low Price Guarantee' feature of {business_name}.

Business Context:
- Key benefits: {key_benefits}

Requirements:
- 1-2 sentences
- Emphasize fair and competitive pricing
- Match the price positioning strategy
- Professional and trustworthy tone
- Focus on value for money

Current description: {current_price_guarantee_feature}

Generate a new description:",
                'variables' => array('business_name', 'key_benefits', 'current_price_guarantee_feature'),
                'is_active' => true,
                'is_default' => true
            ),
            
            'feature_expert_technicians' => array(
                'name' => 'Expert Technicians Feature',
                'content_type' => 'features',
                'template' => "Write a description for the 'Expert Technicians' feature of {business_name}.

Business Context:
- Years in business: {years_experience}
- Specializations: {specializations}
- Key benefits: {key_benefits}
- Brands supported: {brands_supported}

Requirements:
- 1-2 sentences
- Emphasize expertise and experience
- Mention specializations if provided
- Professional and confident tone
- Focus on technical skill and knowledge

Current description: {current_expert_technicians_feature}

Generate a new description:",
                'variables' => array('business_name', 'years_experience', 'specializations', 'key_benefits', 'brands_supported', 'current_expert_technicians_feature'),
                'is_active' => true,
                'is_default' => true
            ),
            
            // SERVICE DESCRIPTION PROMPTS
            'service_description_general' => array(
                'name' => 'General Service Description',
                'content_type' => 'services',
                'template' => "Write a service description for {business_name}'s device repair services.

Business Information:
- Devices repaired: {devices_repaired}
- Specializations: {specializations}
- Brands supported: {brands_supported}
- Turnaround time: {turnaround_time}
- Warranty offered: {warranty_offered}
- Key benefits: {key_benefits}

Requirements:
- 2-3 sentences
- Mention main device types and common repairs
- Include supported brands if provided
- Highlight speed and quality
- Professional and informative tone
- Focus on customer benefits

Current description: {current_service_description}

Generate a new service description:",
                'variables' => array('business_name', 'devices_repaired', 'specializations', 'brands_supported', 'turnaround_time', 'warranty_offered', 'key_benefits', 'current_service_description'),
                'is_active' => true,
                'is_default' => true
            ),
            
            // CALL-TO-ACTION PROMPTS
            'cta_description' => array(
                'name' => 'Call-to-Action Description',
                'content_type' => 'cta',
                'template' => "Create a compelling call-to-action description for {business_name}.

Business Context:
- Key benefits: {key_benefits}
- Warranty offered: {warranty_offered}
- Specializations: {specializations}
- Quality focus: Professional parts and service

Requirements:
- 1-2 sentences
- Mention quality parts and professional service
- Include reliability and warranty if applicable
- Professional and persuasive tone
- Create urgency without being pushy
- Focus on peace of mind and quality

Current CTA description: {current_cta_description}

Generate a new CTA description:",
                'variables' => array('business_name', 'key_benefits', 'warranty_offered', 'specializations', 'current_cta_description'),
                'is_active' => true,
                'is_default' => true
            ),
            
            'cta_title' => array(
                'name' => 'Call-to-Action Title',
                'content_type' => 'cta',
                'template' => "Create a compelling call-to-action title for {business_name}.

Business Context:
- Devices repaired: {devices_repaired}
- Turnaround time: {turnaround_time}
- Key benefits: {key_benefits}

Requirements:
- Maximum 6 words
- Action-oriented and urgent
- Professional tone
- Focus on main service (device repair)
- Create motivation to act

Examples:
- 'Repair Your Device Today!'
- 'Get Your Phone Fixed Now!'
- 'Professional Repair Services'

Current CTA title: {current_cta_title}

Generate a new CTA title:",
                'variables' => array('business_name', 'devices_repaired', 'turnaround_time', 'key_benefits', 'current_cta_title'),
                'is_active' => true,
                'is_default' => true
            ),
            
            // CONTACT SECTION PROMPTS
            'contact_description' => array(
                'name' => 'Contact Section Description',
                'content_type' => 'contact',
                'template' => "Generate professional contact section content for {business_name}.

Contact Information:
- Address: {street_address}, {city}, {state} {zip_code}
- Phone: {phone_number}
- Email: {email}
- Business hours: Available during business hours
- Service area: {city} and surrounding areas

Additional Services:
- On-site service: {house_calls}
- Data recovery: {data_recovery}

Requirements:
- Professional and welcoming tone
- Include all contact methods
- Mention service area
- Add business hours information
- Encourage customers to reach out

Current contact info: {current_contact_description}

Generate new contact section content:",
                'variables' => array('business_name', 'street_address', 'city', 'state', 'zip_code', 'phone_number', 'email', 'house_calls', 'data_recovery', 'current_contact_description'),
                'is_active' => true,
                'is_default' => true
            )
        );
    }
    
    /**
     * Get prompts by content type
     */
    public static function get_prompts_by_type($content_type) {
        $all_prompts = self::get_default_prompts();
        $filtered_prompts = array();
        
        foreach ($all_prompts as $key => $prompt) {
            if ($prompt['content_type'] === $content_type) {
                $filtered_prompts[$key] = $prompt;
            }
        }
        
        return $filtered_prompts;
    }
    
    /**
     * Get required variables for all prompts
     */
    public static function get_required_variables() {
        $all_prompts = self::get_default_prompts();
        $all_variables = array();
        
        foreach ($all_prompts as $prompt) {
            $all_variables = array_merge($all_variables, $prompt['variables']);
        }
        
        return array_unique($all_variables);
    }
    
    /**
     * Validate prompt template
     */
    public static function validate_prompt_template($template, $variables) {
        $errors = array();
        
        // Check if template is not empty
        if (empty(trim($template))) {
            $errors[] = 'Prompt template cannot be empty';
        }
        
        // Check for required variables in template
        foreach ($variables as $variable) {
            $placeholder = '{' . $variable . '}';
            if (strpos($template, $placeholder) === false) {
                $errors[] = "Template should include variable: $placeholder";
            }
        }
        
        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }
}
