<?php

/**
 * Claude AI Manager
 * 
 * Handles Claude API integration for AI text generation
 * 
 * @package WebsiteGenerator
 * @version 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ClaudeAIManager {
    
    // Claude API Configuration
    const API_ENDPOINT = 'https://api.anthropic.com/v1/messages';
    const DEFAULT_MODEL = 'claude-3-5-haiku-20241022';  // Cheapest and fastest model
    const MAX_TOKENS = 1000;
    const TEMPERATURE = 0.7;
    
    // Rate limiting
    const MAX_REQUESTS_PER_HOUR = 50;
    const MAX_REQUESTS_PER_DAY = 200;
    
    private $api_key;
    private $model;
    private $max_tokens;
    private $temperature;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->api_key = get_option('website_generator_claude_api_key', '');
        $this->model = get_option('website_generator_claude_model', self::DEFAULT_MODEL);
        $this->max_tokens = intval(get_option('website_generator_claude_max_tokens', self::MAX_TOKENS));
        $this->temperature = floatval(get_option('website_generator_claude_temperature', self::TEMPERATURE));
    }
    
    /**
     * Check if Claude API is configured
     */
    public function is_configured() {
        return !empty($this->api_key);
    }
    
    /**
     * Test Claude API connection
     */
    public function test_connection() {
        if (!$this->is_configured()) {
            return array(
                'success' => false,
                'error' => 'API key not configured'
            );
        }
        
        try {
            $response = $this->make_api_request(
                'Test connection - please respond with "Connection successful"',
                array(),
                100 // Small token limit for test
            );
            
            if ($response['success']) {
                return array(
                    'success' => true,
                    'message' => 'Claude API connection successful',
                    'model' => $this->model
                );
            } else {
                return array(
                    'success' => false,
                    'error' => $response['error']
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => 'Connection test failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Test connection with provided credentials (for testing before saving)
     */
    public function test_connection_with_credentials($api_key, $model = null) {
        if (empty($api_key)) {
            return array(
                'success' => false,
                'error' => 'API key is required'
            );
        }

        // Use provided model or default
        $test_model = $model ?: self::DEFAULT_MODEL;

        // Validate model
        $available_models = $this->get_available_models();
        if (!isset($available_models[$test_model])) {
            return array(
                'success' => false,
                'error' => 'Invalid model selected: ' . $test_model
            );
        }

        try {
            // Make test API request with provided credentials
            $body = array(
                'model' => $test_model,
                'max_tokens' => 50,
                'temperature' => 0.7,
                'messages' => array(
                    array(
                        'role' => 'user',
                        'content' => 'Test connection - please respond with "Connection successful"'
                    )
                )
            );

            $headers = array(
                'Content-Type' => 'application/json',
                'x-api-key' => $api_key,
                'anthropic-version' => '2023-06-01'
            );

            $response = wp_remote_post(self::API_ENDPOINT, array(
                'headers' => $headers,
                'body' => json_encode($body),
                'timeout' => 30,
                'sslverify' => true
            ));

            if (is_wp_error($response)) {
                return array(
                    'success' => false,
                    'error' => 'API request failed: ' . $response->get_error_message()
                );
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);

            if ($response_code !== 200) {
                $error_data = json_decode($response_body, true);
                $error_message = 'Unknown API error';

                if (isset($error_data['error']['message'])) {
                    $error_message = $error_data['error']['message'];
                } elseif (isset($error_data['error']['type'])) {
                    $error_message = $error_data['error']['type'];
                }

                return array(
                    'success' => false,
                    'error' => "API error ($response_code): $error_message"
                );
            }

            $data = json_decode($response_body, true);

            if (!isset($data['content'][0]['text'])) {
                return array(
                    'success' => false,
                    'error' => 'Invalid API response format'
                );
            }

            return array(
                'success' => true,
                'message' => 'Claude API connection successful',
                'model' => $test_model,
                'response' => trim($data['content'][0]['text'])
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => 'Connection test failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Generate text content using Claude API
     */
    public function generate_content($prompt, $business_info = array(), $options = array()) {
        if (!$this->is_configured()) {
            return array(
                'success' => false,
                'error' => 'Claude API not configured'
            );
        }
        
        // Check rate limits
        $rate_limit_check = $this->check_rate_limits();
        if (!$rate_limit_check['allowed']) {
            return array(
                'success' => false,
                'error' => $rate_limit_check['message']
            );
        }
        
        try {
            // Substitute business information in prompt
            $processed_prompt = $this->substitute_variables($prompt, $business_info);
            
            // Make API request
            $start_time = microtime(true);
            $response = $this->make_api_request(
                $processed_prompt,
                $options,
                $options['max_tokens'] ?? $this->max_tokens
            );
            $generation_time = microtime(true) - $start_time;
            
            if ($response['success']) {
                // Log successful generation
                $this->log_api_usage('success', $generation_time);
                
                return array(
                    'success' => true,
                    'content' => $response['content'],
                    'generation_time' => $generation_time,
                    'model' => $this->model,
                    'prompt_used' => $processed_prompt
                );
            } else {
                // Log failed generation
                $this->log_api_usage('error', $generation_time, $response['error']);
                
                return array(
                    'success' => false,
                    'error' => $response['error']
                );
            }
            
        } catch (Exception $e) {
            error_log('Claude AI Manager Error: ' . $e->getMessage());
            
            return array(
                'success' => false,
                'error' => 'AI generation failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Generate multiple content variations
     */
    public function generate_variations($prompt, $business_info = array(), $count = 3, $options = array()) {
        $variations = array();
        $errors = array();
        
        for ($i = 0; $i < $count; $i++) {
            // Add variation instruction to prompt
            $variation_prompt = $prompt . "\n\nGenerate a unique variation (variation " . ($i + 1) . " of $count).";
            
            $result = $this->generate_content($variation_prompt, $business_info, $options);
            
            if ($result['success']) {
                $variations[] = array(
                    'content' => $result['content'],
                    'generation_time' => $result['generation_time']
                );
            } else {
                $errors[] = $result['error'];
            }
            
            // Small delay between requests to respect rate limits
            if ($i < $count - 1) {
                usleep(500000); // 0.5 second delay
            }
        }
        
        return array(
            'success' => !empty($variations),
            'variations' => $variations,
            'errors' => $errors,
            'count' => count($variations)
        );
    }
    
    /**
     * Make API request to Claude
     */
    private function make_api_request($prompt, $options = array(), $max_tokens = null) {
        $max_tokens = $max_tokens ?? $this->max_tokens;

        $body = array(
            'model' => $this->model,
            'max_tokens' => intval($max_tokens),  // Ensure integer type
            'temperature' => floatval($options['temperature'] ?? $this->temperature),  // Ensure float type
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            )
        );
        
        $headers = array(
            'Content-Type' => 'application/json',
            'x-api-key' => $this->api_key,
            'anthropic-version' => '2023-06-01'
        );
        
        $response = wp_remote_post(self::API_ENDPOINT, array(
            'headers' => $headers,
            'body' => json_encode($body),
            'timeout' => 30,
            'sslverify' => true
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => 'API request failed: ' . $response->get_error_message()
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            $error_data = json_decode($response_body, true);
            $error_message = $error_data['error']['message'] ?? 'Unknown API error';
            
            return array(
                'success' => false,
                'error' => "API error ($response_code): $error_message"
            );
        }
        
        $data = json_decode($response_body, true);
        
        if (!isset($data['content'][0]['text'])) {
            return array(
                'success' => false,
                'error' => 'Invalid API response format'
            );
        }
        
        return array(
            'success' => true,
            'content' => trim($data['content'][0]['text'])
        );
    }
    
    /**
     * Substitute variables in prompt template
     */
    private function substitute_variables($prompt, $business_info) {
        $processed_prompt = $prompt;
        
        foreach ($business_info as $key => $value) {
            $placeholder = '{' . $key . '}';
            $processed_prompt = str_replace($placeholder, $value, $processed_prompt);
        }
        
        // Remove any remaining placeholders
        $processed_prompt = preg_replace('/\{[^}]+\}/', '[not provided]', $processed_prompt);
        
        return $processed_prompt;
    }
    
    /**
     * Check rate limits
     */
    private function check_rate_limits() {
        $current_time = time();
        $hour_start = $current_time - 3600; // 1 hour ago
        $day_start = $current_time - 86400; // 24 hours ago
        
        // Get usage from last hour
        $hourly_usage = get_option('website_generator_claude_hourly_usage', array());
        $hourly_count = 0;
        
        foreach ($hourly_usage as $timestamp => $count) {
            if ($timestamp > $hour_start) {
                $hourly_count += $count;
            }
        }
        
        // Get usage from last day
        $daily_usage = get_option('website_generator_claude_daily_usage', array());
        $daily_count = 0;
        
        foreach ($daily_usage as $timestamp => $count) {
            if ($timestamp > $day_start) {
                $daily_count += $count;
            }
        }
        
        // Check limits
        if ($hourly_count >= self::MAX_REQUESTS_PER_HOUR) {
            return array(
                'allowed' => false,
                'message' => 'Hourly rate limit exceeded. Please wait before making more requests.'
            );
        }
        
        if ($daily_count >= self::MAX_REQUESTS_PER_DAY) {
            return array(
                'allowed' => false,
                'message' => 'Daily rate limit exceeded. Please try again tomorrow.'
            );
        }
        
        return array(
            'allowed' => true,
            'hourly_remaining' => self::MAX_REQUESTS_PER_HOUR - $hourly_count,
            'daily_remaining' => self::MAX_REQUESTS_PER_DAY - $daily_count
        );
    }
    
    /**
     * Log API usage for rate limiting and monitoring
     */
    private function log_api_usage($status, $generation_time = 0, $error = '') {
        $current_time = time();
        
        // Update hourly usage
        $hourly_usage = get_option('website_generator_claude_hourly_usage', array());
        $hour_key = floor($current_time / 3600) * 3600; // Round to hour
        $hourly_usage[$hour_key] = ($hourly_usage[$hour_key] ?? 0) + 1;
        
        // Clean old hourly data (keep last 2 hours)
        $two_hours_ago = $current_time - 7200;
        foreach ($hourly_usage as $timestamp => $count) {
            if ($timestamp < $two_hours_ago) {
                unset($hourly_usage[$timestamp]);
            }
        }
        update_option('website_generator_claude_hourly_usage', $hourly_usage);
        
        // Update daily usage
        $daily_usage = get_option('website_generator_claude_daily_usage', array());
        $day_key = floor($current_time / 86400) * 86400; // Round to day
        $daily_usage[$day_key] = ($daily_usage[$day_key] ?? 0) + 1;
        
        // Clean old daily data (keep last 2 days)
        $two_days_ago = $current_time - 172800;
        foreach ($daily_usage as $timestamp => $count) {
            if ($timestamp < $two_days_ago) {
                unset($daily_usage[$timestamp]);
            }
        }
        update_option('website_generator_claude_daily_usage', $daily_usage);
        
        // Log detailed usage for monitoring
        $usage_log = get_option('website_generator_claude_usage_log', array());
        $usage_log[] = array(
            'timestamp' => $current_time,
            'status' => $status,
            'generation_time' => $generation_time,
            'error' => $error,
            'model' => $this->model
        );
        
        // Keep only last 100 entries
        if (count($usage_log) > 100) {
            $usage_log = array_slice($usage_log, -100);
        }
        
        update_option('website_generator_claude_usage_log', $usage_log);
    }

    /**
     * Get API usage statistics
     */
    public function get_usage_stats() {
        $current_time = time();
        $hour_start = $current_time - 3600;
        $day_start = $current_time - 86400;

        // Get hourly usage
        $hourly_usage = get_option('website_generator_claude_hourly_usage', array());
        $hourly_count = 0;
        foreach ($hourly_usage as $timestamp => $count) {
            if ($timestamp > $hour_start) {
                $hourly_count += $count;
            }
        }

        // Get daily usage
        $daily_usage = get_option('website_generator_claude_daily_usage', array());
        $daily_count = 0;
        foreach ($daily_usage as $timestamp => $count) {
            if ($timestamp > $day_start) {
                $daily_count += $count;
            }
        }

        // Get recent usage log
        $usage_log = get_option('website_generator_claude_usage_log', array());
        $recent_log = array_slice($usage_log, -10); // Last 10 entries

        return array(
            'hourly_usage' => $hourly_count,
            'hourly_limit' => self::MAX_REQUESTS_PER_HOUR,
            'hourly_remaining' => self::MAX_REQUESTS_PER_HOUR - $hourly_count,
            'daily_usage' => $daily_count,
            'daily_limit' => self::MAX_REQUESTS_PER_DAY,
            'daily_remaining' => self::MAX_REQUESTS_PER_DAY - $daily_count,
            'recent_requests' => $recent_log,
            'is_configured' => $this->is_configured(),
            'current_model' => $this->model
        );
    }

    /**
     * Update API configuration
     */
    public function update_configuration($config) {
        $updated = array();

        if (isset($config['api_key'])) {
            update_option('website_generator_claude_api_key', sanitize_text_field($config['api_key']));
            $this->api_key = $config['api_key'];
            $updated[] = 'API key';
        }

        if (isset($config['model'])) {
            $allowed_models = array(
                'claude-3-5-haiku-20241022',
                'claude-sonnet-4-20250514',
                'claude-3-7-sonnet-20250219',
                'claude-opus-4-20250514',
                'claude-opus-4-1-20250805'
            );

            if (in_array($config['model'], $allowed_models)) {
                update_option('website_generator_claude_model', $config['model']);
                $this->model = $config['model'];
                $updated[] = 'Model';
            } else {
                error_log('Website Generator: Invalid model provided: ' . $config['model']);
            }
        }

        if (isset($config['max_tokens']) && is_numeric($config['max_tokens'])) {
            $max_tokens = max(100, min(4000, intval($config['max_tokens'])));
            update_option('website_generator_claude_max_tokens', $max_tokens);
            $this->max_tokens = $max_tokens;
            $updated[] = 'Max tokens';
        }

        if (isset($config['temperature']) && is_numeric($config['temperature'])) {
            $temperature = max(0, min(1, floatval($config['temperature'])));
            update_option('website_generator_claude_temperature', $temperature);
            $this->temperature = $temperature;
            $updated[] = 'Temperature';
        }

        // Log what was received for debugging if nothing was updated
        if (empty($updated)) {
            error_log('Website Generator: No valid configuration provided. Received config: ' . print_r($config, true));
        }

        return array(
            'success' => !empty($updated),
            'updated' => $updated,
            'message' => empty($updated) ? 'No valid configuration provided. Please check your API key and model selection.' : 'Configuration updated: ' . implode(', ', $updated)
        );
    }

    /**
     * Get available models
     */
    public function get_available_models() {
        return array(
            'claude-3-5-haiku-20241022' => array(
                'name' => 'Claude 3.5 Haiku',
                'description' => 'Fastest and most affordable model - perfect for device repair content generation',
                'max_tokens' => 8192,
                'input_cost_per_1m_tokens' => 0.80,
                'output_cost_per_1m_tokens' => 4.00,
                'context_window' => 200000,
                'recommended' => true,
                'tier' => 'Budget-Friendly',
                'speed' => 'Fastest',
                'cost' => 'Very Low'
            ),
            'claude-sonnet-4-20250514' => array(
                'name' => 'Claude Sonnet 4',
                'description' => 'High-performance model with exceptional reasoning - great for complex content',
                'max_tokens' => 64000,
                'input_cost_per_1m_tokens' => 3.00,
                'output_cost_per_1m_tokens' => 15.00,
                'context_window' => 200000,
                'recommended' => false,
                'tier' => 'High-Performance',
                'speed' => 'Fast',
                'cost' => 'Medium'
            ),
            'claude-3-7-sonnet-20250219' => array(
                'name' => 'Claude Sonnet 3.7',
                'description' => 'High-performance model with extended thinking capabilities',
                'max_tokens' => 64000,
                'input_cost_per_1m_tokens' => 3.00,
                'output_cost_per_1m_tokens' => 15.00,
                'context_window' => 200000,
                'recommended' => false,
                'tier' => 'High-Performance',
                'speed' => 'Fast',
                'cost' => 'Medium'
            ),
            'claude-opus-4-20250514' => array(
                'name' => 'Claude Opus 4',
                'description' => 'Previous flagship model with very high intelligence',
                'max_tokens' => 32000,
                'input_cost_per_1m_tokens' => 15.00,
                'output_cost_per_1m_tokens' => 75.00,
                'context_window' => 200000,
                'recommended' => false,
                'tier' => 'Premium',
                'speed' => 'Moderately Fast',
                'cost' => 'High'
            ),
            'claude-opus-4-1-20250805' => array(
                'name' => 'Claude Opus 4.1',
                'description' => 'Most capable and intelligent model - highest level of performance available',
                'max_tokens' => 32000,
                'input_cost_per_1m_tokens' => 15.00,
                'output_cost_per_1m_tokens' => 75.00,
                'context_window' => 200000,
                'recommended' => false,
                'tier' => 'Premium',
                'speed' => 'Moderately Fast',
                'cost' => 'Very High'
            )
        );
    }

    /**
     * Clear usage statistics
     */
    public function clear_usage_stats() {
        delete_option('website_generator_claude_hourly_usage');
        delete_option('website_generator_claude_daily_usage');
        delete_option('website_generator_claude_usage_log');

        return array(
            'success' => true,
            'message' => 'Usage statistics cleared'
        );
    }

    /**
     * Validate prompt template
     */
    public function validate_prompt($prompt, $required_variables = array()) {
        $errors = array();

        // Check if prompt is not empty
        if (empty(trim($prompt))) {
            $errors[] = 'Prompt cannot be empty';
        }

        // Check for required variables
        foreach ($required_variables as $variable) {
            $placeholder = '{' . $variable . '}';
            if (strpos($prompt, $placeholder) === false) {
                $errors[] = "Missing required variable: $placeholder";
            }
        }

        // Check prompt length (rough estimate)
        if (strlen($prompt) > 10000) {
            $errors[] = 'Prompt is too long (max ~10,000 characters)';
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }
}
