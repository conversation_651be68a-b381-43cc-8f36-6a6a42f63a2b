<?php
/**
 * Airtable Webhook Handler
 * 
 * Handles webhook validation, security, and authentication
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class AirtableWebhookHandler {
    
    private $api_key;
    private $webhook_secret;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->api_key = get_option('airtable_api_key', '');
        $this->webhook_secret = get_option('airtable_webhook_secret', '');
    }
    
    /**
     * Validate incoming webhook
     */
    public function validate_webhook($data, $server_data) {
        // Check if Airtable integration is enabled
        if (!get_option('airtable_integration_enabled', false)) {
            return false;
        }
        
        // Validate API key
        if (!$this->validate_api_key($data)) {
            return false;
        }
        
        // Validate webhook signature (if configured)
        if (!empty($this->webhook_secret)) {
            if (!$this->validate_webhook_signature($data, $server_data)) {
                return false;
            }
        }
        
        // Validate site identifier
        if (!$this->validate_site_identifier($data)) {
            return false;
        }
        
        // Rate limiting check
        if (!$this->check_rate_limit()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate API key
     */
    private function validate_api_key($data) {
        if (empty($this->api_key)) {
            $this->log_security_event('missing_api_key_config');
            return false;
        }

        // Check for API key in data or headers
        $provided_key = '';

        if (isset($data['api_key'])) {
            $provided_key = $data['api_key'];
        } elseif (isset($_SERVER['HTTP_X_API_KEY'])) {
            $provided_key = $_SERVER['HTTP_X_API_KEY'];
        } elseif (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            // Handle Bearer token format
            $auth_header = $_SERVER['HTTP_AUTHORIZATION'];
            if (strpos($auth_header, 'Bearer ') === 0) {
                $provided_key = substr($auth_header, 7);
            }
        }

        if (empty($provided_key)) {
            $this->log_security_event('missing_api_key');
            return false;
        }

        $is_valid = hash_equals($this->api_key, $provided_key);

        if (!$is_valid) {
            $this->log_security_event('invalid_api_key', null, array('provided_key_length' => strlen($provided_key)));
        }

        return $is_valid;
    }
    
    /**
     * Validate webhook signature
     */
    private function validate_webhook_signature($data, $server_data) {
        if (empty($this->webhook_secret)) {
            return true; // Skip validation if no secret is set
        }

        // Get signature from headers
        $signature = '';
        $signature_header = '';

        if (isset($server_data['HTTP_X_AIRTABLE_SIGNATURE'])) {
            $signature = $server_data['HTTP_X_AIRTABLE_SIGNATURE'];
            $signature_header = 'X-Airtable-Signature';
        } elseif (isset($server_data['HTTP_X_WEBHOOK_SIGNATURE'])) {
            $signature = $server_data['HTTP_X_WEBHOOK_SIGNATURE'];
            $signature_header = 'X-Webhook-Signature';
        } elseif (isset($server_data['HTTP_X_HUB_SIGNATURE_256'])) {
            $signature = $server_data['HTTP_X_HUB_SIGNATURE_256'];
            $signature_header = 'X-Hub-Signature-256';
        }

        if (empty($signature)) {
            $this->log_security_event('missing_webhook_signature');
            return false;
        }

        // Calculate expected signature
        $payload = file_get_contents('php://input');
        if ($payload === false) {
            $this->log_security_event('invalid_payload');
            return false;
        }

        // Support different signature formats
        $expected_signature = '';
        if (strpos($signature, 'sha256=') === 0) {
            // GitHub/GitLab style: sha256=hash
            $expected_signature = 'sha256=' . hash_hmac('sha256', $payload, $this->webhook_secret);
        } else {
            // Plain hash
            $expected_signature = hash_hmac('sha256', $payload, $this->webhook_secret);
        }

        $is_valid = hash_equals($expected_signature, $signature);

        if (!$is_valid) {
            $this->log_security_event('invalid_signature', null, array(
                'signature_header' => $signature_header,
                'signature_length' => strlen($signature),
                'payload_length' => strlen($payload)
            ));
        }

        return $is_valid;
    }
    
    /**
     * Validate site identifier
     */
    private function validate_site_identifier($data) {
        if (!isset($data['site_identifier'])) {
            return false;
        }
        
        $site_identifier = sanitize_text_field($data['site_identifier']);
        $configured_identifier = get_option('airtable_site_identifier', '');
        
        if (empty($configured_identifier)) {
            // If no identifier is configured, accept any
            return true;
        }
        
        return $site_identifier === $configured_identifier;
    }
    
    /**
     * Check rate limiting
     */
    private function check_rate_limit() {
        $rate_limit = get_option('airtable_rate_limit', 60); // requests per hour
        if ($rate_limit <= 0) {
            return true; // No rate limiting
        }

        // Get client IP for rate limiting
        $client_ip = $this->get_client_ip();

        $current_hour = date('Y-m-d-H');
        $rate_key = 'airtable_rate_' . $current_hour . '_' . md5($client_ip);
        $current_count = get_transient($rate_key);

        if ($current_count === false) {
            $current_count = 0;
        }

        if ($current_count >= $rate_limit) {
            $this->log_security_event('rate_limit_exceeded', $client_ip);
            return false;
        }

        // Increment counter
        set_transient($rate_key, $current_count + 1, HOUR_IN_SECONDS);

        return true;
    }

    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        );

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    /**
     * Log security events
     */
    private function log_security_event($event_type, $ip = null, $details = array()) {
        $security_log = get_option('airtable_security_log', array());

        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'event_type' => $event_type,
            'ip' => $ip ?: $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'details' => $details
        );

        $security_log[] = $log_entry;

        // Keep only last 200 security log entries
        if (count($security_log) > 200) {
            $security_log = array_slice($security_log, -200);
        }

        update_option('airtable_security_log', $security_log);

        // Log critical events to WordPress error log
        if (in_array($event_type, array('rate_limit_exceeded', 'invalid_signature', 'invalid_api_key'))) {
            error_log("Airtable Security Event: {$event_type} from IP {$ip}");
        }
    }
    
    /**
     * Sanitize webhook data
     */
    public function sanitize_webhook_data($data) {
        $sanitized = array();
        
        foreach ($data as $key => $value) {
            $sanitized_key = sanitize_key($key);
            
            if (is_array($value)) {
                $sanitized[$sanitized_key] = $this->sanitize_webhook_data($value);
            } elseif (is_string($value)) {
                // Different sanitization based on field type
                if ($this->is_email_field($key)) {
                    $sanitized[$sanitized_key] = sanitize_email($value);
                } elseif ($this->is_url_field($key)) {
                    $sanitized[$sanitized_key] = esc_url_raw($value);
                } elseif ($this->is_phone_field($key)) {
                    $sanitized[$sanitized_key] = $this->sanitize_phone($value);
                } elseif ($this->is_color_field($key)) {
                    $sanitized[$sanitized_key] = $this->sanitize_color($value);
                } elseif ($this->is_textarea_field($key)) {
                    $sanitized[$sanitized_key] = sanitize_textarea_field($value);
                } else {
                    $sanitized[$sanitized_key] = sanitize_text_field($value);
                }
            } elseif (is_numeric($value)) {
                $sanitized[$sanitized_key] = floatval($value);
            } else {
                $sanitized[$sanitized_key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Check if field is email type
     */
    private function is_email_field($field_name) {
        $email_fields = array('email', 'contact_email');
        return in_array($field_name, $email_fields);
    }
    
    /**
     * Check if field is URL type
     */
    private function is_url_field($field_name) {
        $url_fields = array('website', 'facebook', 'instagram', 'twitter', 'google_business');
        return in_array($field_name, $url_fields);
    }
    
    /**
     * Check if field is phone type
     */
    private function is_phone_field($field_name) {
        $phone_fields = array('phone', 'contact_phone');
        return in_array($field_name, $phone_fields);
    }
    
    /**
     * Check if field is color type
     */
    private function is_color_field($field_name) {
        $color_fields = array('primary_color', 'secondary_color', 'accent_color', 'button_text_color');
        return in_array($field_name, $color_fields);
    }
    
    /**
     * Check if field is textarea type
     */
    private function is_textarea_field($field_name) {
        $textarea_fields = array(
            'onestop_description', 'buy_description', 'sell_description', 
            'cta_description', 'contact_address', 'service_area', 'key_benefits'
        );
        return in_array($field_name, $textarea_fields);
    }
    
    /**
     * Sanitize phone number
     */
    private function sanitize_phone($phone) {
        // Remove all non-numeric characters except + and spaces
        $phone = preg_replace('/[^0-9+\s\-\(\)]/', '', $phone);
        return sanitize_text_field($phone);
    }
    
    /**
     * Sanitize color code
     */
    private function sanitize_color($color) {
        // Ensure it's a valid hex color
        $color = sanitize_text_field($color);
        
        // Add # if missing
        if (strpos($color, '#') !== 0) {
            $color = '#' . $color;
        }
        
        // Validate hex format
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $color)) {
            return '#000000'; // Default to black if invalid
        }
        
        return $color;
    }
    
    /**
     * Validate required fields
     */
    public function validate_required_fields($data) {
        $required_fields = array(
            'site_identifier',
            'business_name'
        );
        
        $missing_fields = array();
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                $missing_fields[] = $field;
            }
        }
        
        return array(
            'valid' => empty($missing_fields),
            'missing_fields' => $missing_fields
        );
    }
    
    /**
     * Validate field lengths
     */
    public function validate_field_lengths($data) {
        $field_limits = array(
            'hero_heading' => 100,
            'hero_tagline' => 80,
            'hero_button1' => 30,
            'hero_button2' => 30,
            'onestop_heading' => 80,
            'onestop_description' => 300,
            'onestop_button' => 30,
            'buy_heading' => 80,
            'buy_description' => 300,
            'buy_button' => 30,
            'sell_heading' => 80,
            'sell_description' => 300,
            'sell_button' => 30,
            'cta_heading' => 80,
            'cta_description' => 300,
            'cta_button' => 30
        );
        
        $length_errors = array();
        
        foreach ($field_limits as $field => $limit) {
            if (isset($data[$field]) && strlen($data[$field]) > $limit) {
                $length_errors[] = "{$field} exceeds {$limit} character limit";
            }
        }
        
        return array(
            'valid' => empty($length_errors),
            'errors' => $length_errors
        );
    }
    
    /**
     * Get webhook configuration status
     */
    public function get_configuration_status() {
        return array(
            'api_key_configured' => !empty($this->api_key),
            'webhook_secret_configured' => !empty($this->webhook_secret),
            'integration_enabled' => get_option('airtable_integration_enabled', false),
            'site_identifier' => get_option('airtable_site_identifier', ''),
            'rate_limit' => get_option('airtable_rate_limit', 60)
        );
    }
}
