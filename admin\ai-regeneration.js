/**
 * AI Text Regeneration System
 *
 * Handles AI-powered text generation for all website content fields
 * including homepage and all device repair service pages
 *
 * @package WebsiteGenerator
 * @version 2.0.0
 */

jQuery(document).ready(function($) {
    
    // AI-enabled fields mapping to prompt keys
    const AI_ENABLED_FIELDS = {
        // Homepage fields
        'hero_heading': 'hero_heading',
        'hero_tagline': 'hero_tagline',
        'onestop_heading': 'about_title',
        'onestop_description': 'about_description',
        'buy_heading': 'service_description_general',
        'buy_description': 'service_description_general',
        'sell_heading': 'service_description_general',
        'sell_description': 'service_description_general',
        'cta_heading': 'cta_title',
        'cta_description': 'cta_description',
        'benefit1_title': 'feature_customer_service',
        'benefit1_description': 'feature_customer_service',
        'benefit2_title': 'feature_quick_turnaround',
        'benefit2_description': 'feature_quick_turnaround',
        'benefit3_title': 'feature_price_guarantee',
        'benefit3_description': 'feature_price_guarantee',
        'benefit4_title': 'feature_expert_technicians',
        'benefit4_description': 'feature_expert_technicians'
    };
    
    let businessInfoComplete = false;
    let aiConfigured = false;
    let aiSystemInitialized = false;

    /**
     * Initialize AI regeneration system
     */
    function initAIRegeneration() {
        if (aiSystemInitialized) {
            console.log('AI regeneration system already initialized, skipping...');
            return;
        }

        console.log('Initializing AI regeneration system...');
        checkAIReadiness();
        addAIButtons();
        bindAIEvents();
        aiSystemInitialized = true;
    }
    
    /**
     * Check if AI system is ready (API configured + prompt available)
     */
    function checkAIReadiness() {
        // Check business info completion (optional now)
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_business_info',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success && response.data.completion_status) {
                    businessInfoComplete = response.data.completion_status.is_complete;
                }
                updateAIButtonStates();
            }
        });

        // Check AI configuration
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_ai_usage_stats',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    aiConfigured = response.data.is_configured;
                }
                updateAIButtonStates();
            }
        });
    }
    
    /**
     * Remove all existing AI buttons (cleanup function)
     */
    function removeExistingAIButtons() {
        $('.ai-regeneration-container').remove();
        $('.form-row').removeClass('has-ai-regeneration');
    }

    /**
     * Reset AI system for re-initialization (useful for tab switching)
     */
    function resetAISystem() {
        console.log('Resetting AI regeneration system...');
        removeExistingAIButtons();
        aiSystemInitialized = false;
    }

    /**
     * Add AI regeneration buttons to enabled fields
     */
    function addAIButtons() {
        // First remove any existing buttons to prevent duplicates
        removeExistingAIButtons();

        Object.keys(AI_ENABLED_FIELDS).forEach(fieldId => {
            const $field = $('#' + fieldId);
            if ($field.length > 0) {
                addAIButtonToField($field, fieldId);
            }
        });
    }
    
    /**
     * Add AI button to a specific field
     */
    function addAIButtonToField($field, fieldId) {
        const $formRow = $field.closest('.form-row');

        // Check if AI button already exists for this field
        if ($field.siblings('.ai-regeneration-container').length > 0) {
            console.log('AI button already exists for field:', fieldId);
            return; // Button already exists, don't add another
        }

        // Create AI button container
        const $aiContainer = $(`
            <div class="ai-regeneration-container">
                <button type="button" class="ai-regenerate-btn" data-field="${fieldId}" disabled>
                    <span class="ai-icon">🤖</span>
                    <span class="ai-text">AI Regenerate</span>
                </button>
                <div class="ai-suggestions" id="${fieldId}_suggestions" style="display: none;">
                    <div class="ai-suggestions-modal">
                        <div class="ai-suggestions-header">
                            <h4>🤖 AI Suggestions</h4>
                            <button type="button" class="ai-close-btn">&times;</button>
                        </div>
                        <div class="ai-suggestions-content">
                            <!-- Suggestions will be populated here -->
                        </div>
                        <div class="ai-suggestions-actions">
                            <button type="button" class="button button-secondary ai-generate-more">
                                Generate More
                            </button>
                            <button type="button" class="button button-secondary ai-close-suggestions">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);
        
        // Insert AI container after the field
        $field.after($aiContainer);
        
        // Add wrapper class to form row for styling
        $formRow.addClass('has-ai-regeneration');
    }
    
    /**
     * Update AI button states based on readiness
     */
    function updateAIButtonStates() {
        // AI is ready if API is configured (business info is now optional)
        const isReady = aiConfigured;

        $('.ai-regenerate-btn').each(function() {
            const $btn = $(this);

            if (isReady) {
                $btn.prop('disabled', false)
                    .removeClass('ai-not-ready')
                    .attr('title', 'Generate AI content for this field using custom prompt');

                // Add visual indicator that business info would improve results
                if (!businessInfoComplete) {
                    $btn.addClass('ai-basic-mode')
                        .attr('title', 'Generate AI content (Basic Mode - configure Business Info for enhanced results)');
                } else {
                    $btn.removeClass('ai-basic-mode')
                        .attr('title', 'Generate AI content (Enhanced Mode - business info configured)');
                }
            } else {
                $btn.prop('disabled', true)
                    .addClass('ai-not-ready')
                    .attr('title', 'Configure Claude API in AI Settings first');
            }
        });
    }
    
    /**
     * Bind AI-related events
     */
    function bindAIEvents() {
        // AI regenerate button click
        $(document).on('click', '.ai-regenerate-btn', function() {
            const fieldId = $(this).data('field');
            generateAIContent(fieldId);
        });
        
        // Close suggestions
        $(document).on('click', '.ai-close-btn, .ai-close-suggestions', function() {
            $(this).closest('.ai-suggestions').hide();
        });

        // Close suggestions when clicking on background overlay
        $(document).on('click', '.ai-suggestions', function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });

        // Close suggestions with Escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && $('.ai-suggestions:visible').length > 0) {
                $('.ai-suggestions:visible').hide();
            }
        });

        // Handle individual regenerate buttons
        $(document).on('click', '.regenerate-single-btn', function() {
            const fieldId = $(this).data('field');
            const suggestionType = $(this).data('type');

            regenerateSingleSuggestion(fieldId, suggestionType, $(this));
        });
        
        // Generate more suggestions
        $(document).on('click', '.ai-generate-more', function() {
            const $suggestions = $(this).closest('.ai-suggestions');
            const fieldId = $suggestions.attr('id').replace('_suggestions', '');
            generateAIContent(fieldId, true);
        });
        
        // Apply suggestion
        $(document).on('click', '.ai-suggestion-item .apply-btn', function() {
            const $item = $(this).closest('.ai-suggestion-item');
            const fieldId = $item.data('field');
            const content = $item.find('.suggestion-content').text();
            
            applySuggestion(fieldId, content);
        });
        
        // Preview suggestion
        $(document).on('click', '.ai-suggestion-item .preview-btn', function() {
            const $item = $(this).closest('.ai-suggestion-item');
            const content = $item.find('.suggestion-content').text();
            
            previewSuggestion(content);
        });
    }
    
    /**
     * Generate AI content for a field
     */
    function generateAIContent(fieldId, isAdditional = false) {
        const promptKey = AI_ENABLED_FIELDS[fieldId];
        const $field = $('#' + fieldId);
        const currentContent = $field.val();
        const $btn = $(`.ai-regenerate-btn[data-field="${fieldId}"]`);
        const $suggestions = $('#' + fieldId + '_suggestions');
        
        // Show loading state
        $btn.prop('disabled', true)
            .html('<span class="ai-icon spinning">⚙️</span><span class="ai-text">Generating...</span>');

        if (!isAdditional) {
            $suggestions.show();
            $suggestions.find('.ai-suggestions-content').html('<div class="ai-loading">🤖 Generating AI content...</div>');
        }

        // Debug logging
        console.log('AI Generation Request:', {
            fieldId: fieldId,
            promptKey: promptKey,
            currentContentLength: currentContent.length,
            isAdditional: isAdditional
        });

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'generate_ai_content',
                nonce: website_generator_ajax.nonce,
                prompt_key: promptKey,
                current_content: currentContent,
                generate_variations: true,
                variation_count: 3,
                use_custom_prompt: true  // Use the custom prompt system
            },
            success: function(response) {
                if (response.success) {
                    // Debug: Log the raw AI response
                    console.log('Raw AI Response:', response.data);

                    displayAISuggestions(fieldId, response.data.variations || [response.data]);
                } else {
                    // Handle different types of error responses (same as AI config/backup systems)
                    let errorMessage = 'AI generation failed';
                    if (typeof response.data === 'string') {
                        errorMessage = response.data;
                    } else if (response.data && response.data.error) {
                        errorMessage = response.data.error;
                    } else if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    } else if (response.data && typeof response.data === 'object') {
                        // Log the full object for debugging
                        console.error('AI Generation Error Object:', response.data);
                        errorMessage = JSON.stringify(response.data);
                    }

                    console.error('AI Generation Failed:', errorMessage);
                    showAIError(fieldId, errorMessage);
                }
            },
            error: function() {
                showAIError(fieldId, 'Network error occurred');
            },
            complete: function() {
                // Restore button state
                $btn.prop('disabled', false)
                    .html('<span class="ai-icon">🤖</span><span class="ai-text">AI Regenerate</span>');
            }
        });
    }
    
    /**
     * Get character limits for different field types
     */
    function getCharacterLimits(fieldId) {
        const limits = {
            'hero_heading': { max: 78, original: 39 },
            'hero_tagline': { max: 86, original: 43 },
            'onestop_heading': { max: 64, original: 32 },
            'onestop_description': { max: 300, original: 150 }
        };

        return limits[fieldId] || null;
    }

    /**
     * Display AI suggestions
     */
    function displayAISuggestions(fieldId, suggestions) {
        const $suggestions = $('#' + fieldId + '_suggestions');
        const $content = $suggestions.find('.ai-suggestions-content');

        if (!suggestions || suggestions.length === 0) {
            $content.html('<div class="ai-error">No suggestions generated. Please try again.</div>');
            return;
        }

        // Parse the AI response to extract the three versions
        const parsedSuggestions = parseAIResponse(suggestions[0]);

        if (parsedSuggestions.length === 0) {
            $content.html('<div class="ai-error">Could not parse AI suggestions. Please try again.</div>');
            return;
        }

        let html = '';
        parsedSuggestions.forEach((suggestion, index) => {
            // Calculate character count and get limits
            const charCount = suggestion.content.length;
            const charLimits = getCharacterLimits(fieldId);
            const charLimitText = charLimits ? ` / ${charLimits.max}` : '';
            const charWarning = charLimits && charCount > charLimits.max ? ' char-warning' : '';

            html += `
                <div class="ai-suggestion-item" data-field="${fieldId}">
                    <div class="suggestion-header">
                        <div class="suggestion-type">
                            <span class="suggestion-number">#${index + 1}</span>
                            <span class="suggestion-label">${suggestion.type}</span>
                            <span class="character-count${charWarning}">${charCount}${charLimitText} chars</span>
                        </div>
                        <div class="suggestion-actions">
                            <button type="button" class="button button-small regenerate-single-btn" data-type="${suggestion.typeKey}" data-field="${fieldId}">
                                🔄 Regenerate
                            </button>
                            <button type="button" class="button button-small preview-btn">Preview</button>
                            <button type="button" class="button button-primary button-small apply-btn">Apply</button>
                        </div>
                    </div>
                    <div class="suggestion-purpose">
                        <strong>Purpose:</strong> ${suggestion.purpose}
                    </div>
                    <div class="suggestion-content">${escapeHtml(suggestion.content)}</div>
                    ${charLimits && charCount > charLimits.max ? `<div class="char-limit-warning">⚠️ Content exceeds recommended length of ${charLimits.max} characters. Consider regenerating for better fit.</div>` : ''}
                    ${suggestion.length_warning ? `<div class="ai-length-warning">ℹ️ ${suggestion.length_warning}</div>` : ''}
                </div>
            `;
        });

        $content.html(html);
        $suggestions.show();

        // If all suggestions have the same content, automatically regenerate individual versions
        if (parsedSuggestions.length === 3 &&
            parsedSuggestions[0].content === parsedSuggestions[1].content &&
            parsedSuggestions[1].content === parsedSuggestions[2].content) {

            console.log('All suggestions identical, auto-generating individual versions...');

            // Auto-regenerate each suggestion type
            setTimeout(() => {
                $('.regenerate-single-btn[data-type="seo"]').click();
            }, 500);

            setTimeout(() => {
                $('.regenerate-single-btn[data-type="geo"]').click();
            }, 1000);
        }
    }

    /**
     * Parse AI response to extract the three versions
     */
    function parseAIResponse(response) {
        const content = response.content || response;
        const suggestions = [];

        // Debug logging
        console.log('AI Response Content:', content);

        // Define multiple pattern variations to catch different AI response formats
        const suggestionTypes = [
            {
                key: 'context',
                type: 'Context-Aware Content',
                purpose: 'Professional content that maintains the original message while building trust and emphasizing key benefits',
                patterns: [
                    /\*\*VERSION 1 - CONTEXT-AWARE CONTENT:\*\*\s*\n([\s\S]*?)(?=\*\*VERSION 2|$)/i,
                    /VERSION 1.*?CONTEXT-AWARE.*?\n([\s\S]*?)(?=VERSION 2|$)/i,
                    /1\.\s*CONTEXT-AWARE.*?\n([\s\S]*?)(?=2\.|$)/i,
                    /CONTEXT-AWARE.*?\n([\s\S]*?)(?=SEO-OPTIMIZED|$)/i
                ]
            },
            {
                key: 'seo',
                type: 'SEO-Optimized Content',
                purpose: 'Search engine optimized content with relevant keywords for better visibility and ranking',
                patterns: [
                    /\*\*VERSION 2 - SEO-OPTIMIZED CONTENT:\*\*\s*\n([\s\S]*?)(?=\*\*VERSION 3|$)/i,
                    /VERSION 2.*?SEO-OPTIMIZED.*?\n([\s\S]*?)(?=VERSION 3|$)/i,
                    /2\.\s*SEO-OPTIMIZED.*?\n([\s\S]*?)(?=3\.|$)/i,
                    /SEO-OPTIMIZED.*?\n([\s\S]*?)(?=GEO-OPTIMIZED|$)/i
                ]
            },
            {
                key: 'geo',
                type: 'GEO-Optimized Content',
                purpose: 'AI search engine optimized content designed for ChatGPT, Perplexity, and Google AI Overviews',
                patterns: [
                    /\*\*VERSION 3 - GEO-OPTIMIZED CONTENT:\*\*\s*\n([\s\S]*?)$/i,
                    /VERSION 3.*?GEO-OPTIMIZED.*?\n([\s\S]*?)$/i,
                    /3\.\s*GEO-OPTIMIZED.*?\n([\s\S]*?)$/i,
                    /GEO-OPTIMIZED.*?\n([\s\S]*?)$/i
                ]
            }
        ];

        // Extract each version using multiple pattern attempts
        suggestionTypes.forEach(type => {
            let extractedContent = null;

            // Try each pattern until one matches
            for (const pattern of type.patterns) {
                const match = content.match(pattern);
                if (match && match[1]) {
                    extractedContent = match[1].trim();
                    // Remove any leading brackets or formatting
                    extractedContent = extractedContent.replace(/^\[|\]$/g, '').trim();
                    break;
                }
            }

            if (extractedContent) {
                suggestions.push({
                    type: type.type,
                    typeKey: type.key,
                    purpose: type.purpose,
                    content: extractedContent
                });
                console.log(`Extracted ${type.type}:`, extractedContent.substring(0, 100) + '...');
            }
        });

        // If parsing fails, generate three different versions using individual prompts
        if (suggestions.length === 0) {
            console.log('Pattern matching failed, generating individual versions...');

            // Use the original content as the base and generate three different versions
            const baseContent = content.trim();

            // For now, create three suggestions with the same content but different purposes
            // The individual regenerate buttons will create truly different versions
            suggestions.push(
                {
                    type: 'Context-Aware Content',
                    typeKey: 'context',
                    purpose: 'Professional content that maintains the original message while building trust and emphasizing key benefits',
                    content: baseContent
                },
                {
                    type: 'SEO-Optimized Content',
                    typeKey: 'seo',
                    purpose: 'Search engine optimized content with relevant keywords for better visibility and ranking',
                    content: baseContent
                },
                {
                    type: 'GEO-Optimized Content',
                    typeKey: 'geo',
                    purpose: 'AI search engine optimized content designed for ChatGPT, Perplexity, and Google AI Overviews',
                    content: baseContent
                }
            );
        }

        console.log('Final suggestions:', suggestions.length);
        return suggestions;
    }

    /**
     * Regenerate a single suggestion type
     */
    function regenerateSingleSuggestion(fieldId, suggestionType, $button) {
        const promptKey = AI_ENABLED_FIELDS[fieldId];
        const $field = $('#' + fieldId);
        const currentContent = $field.val();

        // Show loading state on the specific button
        const originalText = $button.html();
        $button.prop('disabled', true).html('⚙️ Generating...');

        // Get character limits for this field
        const charLimits = getCharacterLimits(fieldId);
        const charLimitText = charLimits ? ` CRITICAL: Stay within ${charLimits.max} characters total.` : '';

        // Create a specific prompt for the suggestion type
        let specificPrompt = '';
        switch(suggestionType) {
            case 'context':
                specificPrompt = `You are a customer psychology expert who understands device repair emergencies. Transform this content to be more empathetic and trust-building while addressing the emotional stress of device problems.

**CHARACTER LIMIT:${charLimitText} Count every character as you write.**

**Focus on:**
- Acknowledging customer frustration and urgency
- Building confidence in your repair expertise
- Emphasizing care and attention to detail
- Highlighting customer success stories mindset
- Using reassuring, professional language
- Creating emotional connection with device owners

**Emotional triggers to include (space permitting):**
- "We understand how frustrating a broken device can be"
- "Your device is in expert hands"
- "We treat your device like our own"
- "Get back to what matters most"
- "Trusted by thousands of satisfied customers"

Content to rewrite: {ORIGINAL_CONTENT}

Create content that makes customers feel understood and confident in choosing your services. ${charLimitText ? 'IMPORTANT: Count characters and stop before exceeding the limit.' : ''}`;
                break;
            case 'seo':
                specificPrompt = `You are a local SEO expert specializing in device repair businesses. Rewrite this content to rank higher for repair-related searches while maintaining natural readability.

**CHARACTER LIMIT:${charLimitText} Count every character as you write.**

**TARGET KEYWORDS TO NATURALLY INCLUDE (space permitting):**
Primary: device repair, phone repair, computer repair, screen replacement, [device] fix
Secondary: [device] repair near me, same day repair, data recovery, battery replacement
Local: repair shop, local technicians, neighborhood service, nearby repair
Long-tail: cracked screen repair, water damage fix, virus removal, charging port repair

**LOCAL SEO ELEMENTS:**
- Include service area references naturally
- Add "near me" context where appropriate
- Mention local community service (if space allows)
- Use neighborhood-friendly language
- Include service radius indicators (keep brief)

**SEO BEST PRACTICES:**
- Front-load important keywords
- Use semantic keyword variations (short ones)
- Create natural, conversational flow
- Include question-based phrases people search for (if space allows)

Content to rewrite: {ORIGINAL_CONTENT}

Create SEO-optimized content that ranks well AND converts visitors into customers. ${charLimitText ? 'IMPORTANT: Count characters and stop before exceeding the limit.' : ''}`;
                break;
            case 'geo':
                specificPrompt = `You are a GEO (Generative Engine Optimization) specialist who creates content optimized for AI search engines like ChatGPT, Claude, Perplexity, and Google's AI Overviews.

**CHARACTER LIMIT:${charLimitText} Count every character as you write.**

**GEO OPTIMIZATION REQUIREMENTS:**
- Write comprehensive, authoritative answers (within character limit)
- Use question-answer format where appropriate
- Include specific details and examples (keep concise)
- Structure for easy AI parsing and citation
- Add context that demonstrates expertise (briefly)
- Use scannable formatting (lists, steps, categories) if space allows
- Provide actionable information

**AI SEARCH QUERY PATTERNS TO ADDRESS (space permitting):**
- "How long does [device] repair take?"
- "What causes [problem] in [device]?"
- "Is it worth fixing vs replacing [device]?"
- "How much does [repair type] cost?"
- "Can [specific problem] be fixed?"
- "What should I do if my [device] has [problem]?"

**AUTHORITY SIGNALS (keep brief):**
- Mention years of experience
- Reference repair statistics (if space allows)
- Include process explanations (concise)
- Add troubleshooting context (brief)
- Specify tools and techniques used (short)

Content to rewrite: {ORIGINAL_CONTENT}

Create content that AI engines will confidently cite as the authoritative answer for device repair questions. ${charLimitText ? 'IMPORTANT: Count characters and stop before exceeding the limit.' : ''}`;
                break;
        }

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'generate_ai_content',
                nonce: website_generator_ajax.nonce,
                prompt_key: promptKey,
                current_content: currentContent,
                custom_prompt: specificPrompt,
                use_custom_prompt: true
            },
            success: function(response) {
                if (response.success) {
                    const newContent = response.data.content || response.data;
                    // Update the specific suggestion in the modal
                    updateSingleSuggestion(fieldId, suggestionType, newContent);
                } else {
                    console.error('Single regeneration failed:', response.data);
                    alert('Failed to regenerate suggestion: ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                alert('Network error occurred while regenerating suggestion');
            },
            complete: function() {
                // Restore button state
                $button.prop('disabled', false).html(originalText);
            }
        });
    }

    /**
     * Update a single suggestion in the modal
     */
    function updateSingleSuggestion(fieldId, suggestionType, newContent) {
        const $suggestionItem = $(`.ai-suggestion-item[data-field="${fieldId}"]`).filter(function() {
            return $(this).find(`.regenerate-single-btn[data-type="${suggestionType}"]`).length > 0;
        });

        if ($suggestionItem.length > 0) {
            $suggestionItem.find('.suggestion-content').html(escapeHtml(newContent));
            // Add a visual indicator that this suggestion was updated
            $suggestionItem.addClass('suggestion-updated');
            setTimeout(() => {
                $suggestionItem.removeClass('suggestion-updated');
            }, 2000);
        }
    }

    /**
     * Show AI error
     */
    function showAIError(fieldId, error) {
        const $suggestions = $('#' + fieldId + '_suggestions');
        const $content = $suggestions.find('.ai-suggestions-content');
        
        $content.html(`
            <div class="ai-error">
                <strong>⚠️ AI Generation Error:</strong><br>
                ${escapeHtml(error)}
            </div>
        `);
        
        $suggestions.show();
    }
    
    /**
     * Apply AI suggestion to field
     */
    function applySuggestion(fieldId, content) {
        const $field = $('#' + fieldId);
        const originalContent = $field.val();

        // Create automatic backup before applying AI changes
        createAutomaticBackup(fieldId, originalContent, content, function(backupSuccess) {
            if (backupSuccess) {
                // Apply new content
                $field.val(content).trigger('change');

                // Update preview if available
                if ($field.data('preview')) {
                    updateBlockPreview($field);
                }

                // Close suggestions
                $('#' + fieldId + '_suggestions').hide();

                // Show success message with backup info
                showNotice('AI content applied successfully! Automatic backup created. You can undo this change if needed.', 'success');

                // Add undo button temporarily
                addUndoButton(fieldId, originalContent);
            } else {
                // Backup failed, ask user if they want to proceed
                if (confirm('Automatic backup failed. Do you want to apply the AI content anyway? You can still undo manually.')) {
                    // Create local backup as fallback
                    createContentBackup(fieldId, originalContent);

                    // Apply content
                    $field.val(content).trigger('change');

                    if ($field.data('preview')) {
                        updateBlockPreview($field);
                    }

                    $('#' + fieldId + '_suggestions').hide();
                    showNotice('AI content applied (backup failed, but local undo available).', 'warning');
                    addUndoButton(fieldId, originalContent);
                } else {
                    showNotice('AI content application cancelled.', 'info');
                }
            }
        });
    }
    
    /**
     * Preview AI suggestion
     */
    function previewSuggestion(content) {
        // Create a temporary preview modal
        const $modal = $(`
            <div class="ai-preview-modal">
                <div class="ai-preview-content">
                    <div class="ai-preview-header">
                        <h3>🔍 Content Preview</h3>
                        <button type="button" class="ai-preview-close">&times;</button>
                    </div>
                    <div class="ai-preview-body">
                        <div class="preview-content">${escapeHtml(content)}</div>
                    </div>
                    <div class="ai-preview-footer">
                        <button type="button" class="button button-secondary ai-preview-close">Close</button>
                    </div>
                </div>
            </div>
        `);
        
        $('body').append($modal);
        
        // Close modal events
        $modal.find('.ai-preview-close').on('click', function() {
            $modal.remove();
        });
        
        // Close on background click
        $modal.on('click', function(e) {
            if (e.target === this) {
                $modal.remove();
            }
        });
    }
    
    /**
     * Create automatic backup using the simplified backup system
     */
    function createAutomaticBackup(fieldId, originalContent, newContent, callback) {
        // Create backup using the simplified backup system
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'create_backup',
                nonce: website_generator_ajax.nonce,
                description: `AI content change for field: ${fieldId} (${new Date().toLocaleString()})`
            },
            success: function(response) {
                if (response.success) {
                    // Store backup ID for potential restoration
                    const aiBackups = JSON.parse(localStorage.getItem('ai_backup_ids') || '{}');
                    aiBackups[fieldId] = {
                        backup_id: response.data.backup_id,
                        timestamp: Date.now(),
                        original_content: originalContent
                    };
                    localStorage.setItem('ai_backup_ids', JSON.stringify(aiBackups));

                    callback(true);
                } else {
                    console.error('Backup creation failed:', response.data);
                    callback(false);
                }
            },
            error: function() {
                console.error('Backup creation request failed');
                callback(false);
            }
        });
    }

    /**
     * Create local content backup (fallback)
     */
    function createContentBackup(fieldId, content) {
        const backups = JSON.parse(localStorage.getItem('ai_content_backups') || '{}');
        backups[fieldId] = {
            content: content,
            timestamp: Date.now()
        };
        localStorage.setItem('ai_content_backups', JSON.stringify(backups));
    }
    
    /**
     * Add undo button with backup restoration option
     */
    function addUndoButton(fieldId, originalContent) {
        const $field = $('#' + fieldId);
        const $undoContainer = $(`
            <div class="ai-undo-container">
                <button type="button" class="ai-undo-btn" data-field="${fieldId}" data-original="${originalContent}">
                    <span class="dashicons dashicons-undo"></span> Undo AI Change
                </button>
                <button type="button" class="ai-restore-backup-btn" data-field="${fieldId}">
                    <span class="dashicons dashicons-backup"></span> Restore from Backup
                </button>
            </div>
        `);

        $field.after($undoContainer);

        // Remove undo buttons after 5 minutes (better for template editing)
        setTimeout(() => {
            $undoContainer.fadeOut(() => $undoContainer.remove());
        }, 300000);

        // Quick undo functionality
        $undoContainer.find('.ai-undo-btn').on('click', function() {
            const originalContent = $(this).data('original');
            $field.val(originalContent).trigger('change');

            if ($field.data('preview')) {
                updateBlockPreview($field);
            }

            $undoContainer.remove();
            showNotice('AI change undone successfully!', 'info');
        });

        // Backup restoration functionality
        $undoContainer.find('.ai-restore-backup-btn').on('click', function() {
            restoreFromBackup(fieldId, $undoContainer);
        });
    }

    /**
     * Restore content from backup system
     */
    function restoreFromBackup(fieldId, $undoContainer) {
        const aiBackups = JSON.parse(localStorage.getItem('ai_backup_ids') || '{}');
        const backupInfo = aiBackups[fieldId];

        if (!backupInfo) {
            showNotice('No backup found for this field.', 'error');
            return;
        }

        // Show loading state
        const $restoreBtn = $undoContainer.find('.ai-restore-backup-btn');
        const originalText = $restoreBtn.html();
        $restoreBtn.html('<span class="dashicons dashicons-update spin"></span> Restoring...').prop('disabled', true);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'restore_backup',
                nonce: website_generator_ajax.nonce,
                backup_id: backupInfo.backup_id
            },
            success: function(response) {
                if (response.success) {
                    // Restore the original content
                    const $field = $('#' + fieldId);
                    $field.val(backupInfo.original_content).trigger('change');

                    if ($field.data('preview')) {
                        updateBlockPreview($field);
                    }

                    $undoContainer.remove();
                    showNotice('Content restored from backup successfully!', 'success');

                    // Clean up backup reference
                    delete aiBackups[fieldId];
                    localStorage.setItem('ai_backup_ids', JSON.stringify(aiBackups));
                } else {
                    showNotice('Backup restoration failed: ' + (response.data || 'Unknown error'), 'error');
                }
            },
            error: function() {
                showNotice('Backup restoration request failed.', 'error');
            },
            complete: function() {
                $restoreBtn.html(originalText).prop('disabled', false);
            }
        });
    }
    
    /**
     * Utility functions
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function showNotice(message, type) {
        const noticeClass = type === 'error' ? 'notice-error' : (type === 'success' ? 'notice-success' : 'notice-info');
        const notice = $(`
            <div class="notice ${noticeClass} is-dismissible ai-notice">
                <p>${message}</p>
                <button type="button" class="notice-dismiss">
                    <span class="screen-reader-text">Dismiss this notice.</span>
                </button>
            </div>
        `);
        
        $('.visual-editor-header').after(notice);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            notice.fadeOut(() => notice.remove());
        }, 5000);
        
        // Manual dismiss
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut(() => notice.remove());
        });
    }
    
    // Initialize when any tab with AI-enabled fields is active
    const aiEnabledTabs = ['homepage'];
    
    // Check if any AI-enabled tab is currently active on page load
    aiEnabledTabs.forEach(tab => {
        if ($(`#${tab}-tab`).hasClass('active')) {
            initAIRegeneration();
        }
    });

    // Initialize when any AI-enabled tab becomes active
    aiEnabledTabs.forEach(tab => {
        $(document).on('click', `[data-tab="${tab}"]`, function() {
            setTimeout(() => {
                // Reset and re-initialize to ensure clean state
                resetAISystem();
                initAIRegeneration();
            }, 100);
        });
    });

    // Re-check AI readiness when switching to business info or AI config tabs
    $(document).on('click', '[data-tab="business-info"], [data-tab="ai-configuration"]', function() {
        setTimeout(checkAIReadiness, 500);
    });
});
