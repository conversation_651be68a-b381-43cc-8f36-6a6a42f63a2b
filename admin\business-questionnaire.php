<?php
/**
 * Business Information Questionnaire
 * 
 * A step-by-step questionnaire for collecting device repair store information
 * to generate personalized AI content.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="business-questionnaire-container">
    <div class="questionnaire-header">
        <h2>🔧 Device Repair Store Setup</h2>
        <p>Complete this questionnaire to help AI generate personalized content for your repair store website.</p>
        
        <div class="progress-indicator">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 16.67%"></div>
            </div>
            <span class="progress-text" id="progress-text">Step 1 of 6</span>
        </div>
    </div>

    <form id="business-questionnaire-form" class="questionnaire-form">
        
        <!-- Step 1: Basic Business Information -->
        <div class="form-step active" data-step="1">
            <div class="step-header">
                <h3>📍 Basic Business Information</h3>
                <p>Let's start with the basics about your repair store</p>
            </div>
            
            <div class="form-grid">
                <div class="form-group full-width">
                    <label for="business_name">Business Name *</label>
                    <input type="text" id="business_name" name="business_name" required 
                           placeholder="e.g., TechFix Repair Center">
                    <small>This will appear as your main business name throughout the website</small>
                </div>
                
                <div class="form-group">
                    <label for="city">City *</label>
                    <input type="text" id="city" name="city" required 
                           placeholder="e.g., Miami">
                </div>
                
                <div class="form-group">
                    <label for="state">State *</label>
                    <input type="text" id="state" name="state" required 
                           placeholder="e.g., FL">
                </div>
                
                <div class="form-group">
                    <label for="phone">Phone Number *</label>
                    <input type="tel" id="phone" name="phone" required 
                           placeholder="(*************">
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" 
                           placeholder="<EMAIL>">
                </div>
                
                <div class="form-group full-width">
                    <label for="address">Full Address *</label>
                    <input type="text" id="address" name="address" required 
                           placeholder="123 Main Street, Miami, FL 33101">
                    <small>This will be used for location-based content and contact information</small>
                </div>
            </div>
        </div>

        <!-- Step 2: Device Repair Services -->
        <div class="form-step" data-step="2">
            <div class="step-header">
                <h3>📱 What Devices Do You Repair?</h3>
                <p>Select all the devices your store repairs (check all that apply)</p>
            </div>
            
            <div class="services-grid">
                <div class="service-category">
                    <h4>📱 Mobile Devices</h4>
                    <div class="checkbox-group">

                        

                    </div>
                </div>
                
                <div class="service-category">
                    <h4>💻 Computers & Laptops</h4>
                    <div class="checkbox-group">

                    </div>
                </div>
                
                <div class="service-category">
                    <h4>🎮 Gaming & Wearables</h4>
                    <div class="checkbox-group">
                        <label class="checkbox-item">
                            <input type="checkbox" name="services[]" value="smartwatch_repair">
                            <span class="checkmark"></span>
                            <div class="service-info">
                                <strong>Other Smartwatch Repair</strong>
                                <small>Samsung Galaxy Watch, Fitbit, etc.</small>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 3: Business Characteristics -->
        <div class="form-step" data-step="3">
            <div class="step-header">
                <h3>🏆 What Makes Your Store Special?</h3>
                <p>Select the key advantages that set your repair store apart</p>
            </div>

            <div class="characteristics-grid">
                <label class="feature-card">
                    <input type="checkbox" name="characteristics[]" value="same_day_service">
                    <div class="card-content">
                        <div class="card-icon">⚡</div>
                        <h4>Same Day Service</h4>
                        <p>Most repairs completed within hours</p>
                    </div>
                </label>

                <label class="feature-card">
                    <input type="checkbox" name="characteristics[]" value="expert_technicians">
                    <div class="card-content">
                        <div class="card-icon">👨‍🔧</div>
                        <h4>Expert Technicians</h4>
                        <p>Certified and experienced repair specialists</p>
                    </div>
                </label>

                <label class="feature-card">
                    <input type="checkbox" name="characteristics[]" value="warranty_guarantee">
                    <div class="card-content">
                        <div class="card-icon">🛡️</div>
                        <h4>Warranty & Guarantee</h4>
                        <p>All repairs backed by warranty</p>
                    </div>
                </label>

                <label class="feature-card">
                    <input type="checkbox" name="characteristics[]" value="competitive_pricing">
                    <div class="card-content">
                        <div class="card-icon">💰</div>
                        <h4>Competitive Pricing</h4>
                        <p>Fair prices and price matching</p>
                    </div>
                </label>

                <label class="feature-card">
                    <input type="checkbox" name="characteristics[]" value="quality_parts">
                    <div class="card-content">
                        <div class="card-icon">⭐</div>
                        <h4>Quality Parts</h4>
                        <p>OEM and high-quality replacement parts</p>
                    </div>
                </label>

                <label class="feature-card">
                    <input type="checkbox" name="characteristics[]" value="customer_service">
                    <div class="card-content">
                        <div class="card-icon">🤝</div>
                        <h4>Excellent Customer Service</h4>
                        <p>Friendly, helpful, and professional staff</p>
                    </div>
                </label>
            </div>
        </div>

        <!-- Step 4: Additional Services -->
        <div class="form-step" data-step="4">
            <div class="step-header">
                <h3>🔧 Additional Services</h3>
                <p>What extra services do you offer beyond basic repairs?</p>
            </div>

            <div class="additional-services-grid">
                <label class="service-option">
                    <input type="checkbox" name="additional_services[]" value="buy_sell_devices">
                    <div class="option-content">
                        <div class="option-icon">💰</div>
                        <div class="option-text">
                            <strong>Buy & Sell Devices</strong>
                            <small>Purchase and sell used/refurbished devices</small>
                        </div>
                    </div>
                </label>

                <label class="service-option">
                    <input type="checkbox" name="additional_services[]" value="data_recovery">
                    <div class="option-content">
                        <div class="option-icon">💾</div>
                        <div class="option-text">
                            <strong>Data Recovery</strong>
                            <small>Recover lost data from damaged devices</small>
                        </div>
                    </div>
                </label>

                <label class="service-option">
                    <input type="checkbox" name="additional_services[]" value="device_protection">
                    <div class="option-content">
                        <div class="option-icon">🛡️</div>
                        <div class="option-text">
                            <strong>Device Protection</strong>
                            <small>Screen protectors, cases, insurance</small>
                        </div>
                    </div>
                </label>

                <label class="service-option">
                    <input type="checkbox" name="additional_services[]" value="onsite_service">
                    <div class="option-content">
                        <div class="option-icon">🚗</div>
                        <div class="option-text">
                            <strong>On-Site Service</strong>
                            <small>Mobile repair service at customer location</small>
                        </div>
                    </div>
                </label>

                <label class="service-option">
                    <input type="checkbox" name="additional_services[]" value="business_services">
                    <div class="option-content">
                        <div class="option-icon">🏢</div>
                        <div class="option-text">
                            <strong>Business Services</strong>
                            <small>Corporate device management and repair</small>
                        </div>
                    </div>
                </label>

                <label class="service-option">
                    <input type="checkbox" name="additional_services[]" value="device_setup">
                    <div class="option-content">
                        <div class="option-icon">⚙️</div>
                        <div class="option-text">
                            <strong>Device Setup</strong>
                            <small>New device setup and data transfer</small>
                        </div>
                    </div>
                </label>
            </div>
        </div>

        <!-- Step 5: Business Experience -->
        <div class="form-step" data-step="5">
            <div class="step-header">
                <h3>📈 Business Experience</h3>
                <p>Tell us about your experience and business details</p>
            </div>

            <div class="experience-form">
                <div class="form-group">
                    <label for="years_in_business">Years in Business</label>
                    <select id="years_in_business" name="years_in_business">
                        <option value="">Select...</option>
                        <option value="new">New Business (Less than 1 year)</option>
                        <option value="1-2">1-2 years</option>
                        <option value="3-5">3-5 years</option>
                        <option value="6-10">6-10 years</option>
                        <option value="10+">10+ years</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="team_size">Team Size</label>
                    <select id="team_size" name="team_size">
                        <option value="">Select...</option>
                        <option value="solo">Solo (Just me)</option>
                        <option value="2-3">2-3 people</option>
                        <option value="4-10">4-10 people</option>
                        <option value="10+">10+ people</option>
                    </select>
                </div>

                <div class="form-group full-width">
                    <label for="business_hours">Business Hours</label>
                    <input type="text" id="business_hours" name="business_hours"
                           placeholder="e.g., Mon-Fri 9AM-6PM, Sat 10AM-4PM">
                    <small>When are you open for customers?</small>
                </div>

                <div class="form-group full-width">
                    <label for="special_certifications">Special Certifications or Training</label>
                    <textarea id="special_certifications" name="special_certifications" rows="3"
                              placeholder="e.g., Apple Certified, Samsung Authorized, CompTIA A+, etc."></textarea>
                    <small>Any certifications or special training that sets you apart</small>
                </div>
            </div>
        </div>

        <!-- Step 6: Online Presence & Contact -->
        <div class="form-step" data-step="6">
            <div class="step-header">
                <h3>🌐 Online Presence & Contact</h3>
                <p>Help customers find and connect with you online</p>
            </div>

            <div class="online-presence-form">
                <div class="form-group">
                    <label for="facebook_url">Facebook Page</label>
                    <input type="url" id="facebook_url" name="facebook_url"
                           placeholder="https://facebook.com/yourbusiness">
                </div>

                <div class="form-group">
                    <label for="instagram_url">Instagram Page</label>
                    <input type="url" id="instagram_url" name="instagram_url"
                           placeholder="https://instagram.com/yourbusiness">
                </div>

                <div class="form-group">
                    <label for="google_business_url">Google Business Profile</label>
                    <input type="url" id="google_business_url" name="google_business_url"
                           placeholder="https://g.page/yourbusiness">
                    <small>For reviews and directions</small>
                </div>

                <div class="form-group">
                    <label for="website_url">Existing Website (if any)</label>
                    <input type="url" id="website_url" name="website_url"
                           placeholder="https://yourrepairstore.com">
                </div>



                <div class="form-group full-width">
                    <label for="special_notes">Special Notes or Unique Selling Points</label>
                    <textarea id="special_notes" name="special_notes" rows="3"
                              placeholder="Anything else that makes your repair store unique or special?"></textarea>
                    <small>Any additional information that would help AI create better content</small>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="form-navigation">
            <button type="button" id="prev-step" class="button button-secondary" style="display: none;">
                <span class="dashicons dashicons-arrow-left-alt2"></span>
                Previous
            </button>
            
            <button type="button" id="next-step" class="button button-primary">
                Next
                <span class="dashicons dashicons-arrow-right-alt2"></span>
            </button>
            
            <button type="submit" id="submit-questionnaire" class="button button-primary button-large" style="display: none;">
                <span class="dashicons dashicons-yes"></span>
                Complete Setup
            </button>
        </div>
    </form>
</div>

<style>
/* Business Questionnaire Styles */
.business-questionnaire-container {
    max-width: 900px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.questionnaire-header {
    background: linear-gradient(135deg, #0073aa, #005a87);
    color: white;
    padding: 30px;
    text-align: center;
}

.questionnaire-header h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
}

.questionnaire-header p {
    margin: 0 0 20px 0;
    opacity: 0.9;
    font-size: 16px;
}

.progress-indicator {
    margin-top: 20px;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.2);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    background: #00d084;
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
}

/* Form Steps */
.questionnaire-form {
    padding: 40px;
}

.form-step {
    display: none;
    animation: fadeIn 0.3s ease;
}

.form-step.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.step-header {
    text-align: center;
    margin-bottom: 40px;
}

.step-header h3 {
    margin: 0 0 10px 0;
    font-size: 24px;
    color: #23282d;
}

.step-header p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #23282d;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

.form-group small {
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-category h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    color: #0073aa;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 8px;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.checkbox-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    background: #fafafa;
}

.checkbox-item:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.checkbox-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 12px;
    flex-shrink: 0;
    position: relative;
    transition: all 0.2s;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
    background: #0073aa;
    border-color: #0073aa;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.service-info strong {
    display: block;
    margin-bottom: 4px;
    color: #23282d;
    font-size: 14px;
}

.service-info small {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
}

/* Characteristics Grid */
.characteristics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.feature-card {
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s;
    background: #fafafa;
    text-align: center;
}

.feature-card:hover {
    border-color: #0073aa;
    background: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 115, 170, 0.1);
}

.feature-card input[type="checkbox"] {
    display: none;
}

.feature-card input[type="checkbox"]:checked + .card-content {
    color: #0073aa;
}

.feature-card input[type="checkbox"]:checked + .card-content .card-icon {
    background: #0073aa;
    color: white;
}

.card-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px auto;
    transition: all 0.3s;
}

.card-content h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
}

.card-content p {
    margin: 0;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

/* Additional Services */
.additional-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.service-option {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    background: #fafafa;
}

.service-option:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.service-option input[type="checkbox"] {
    display: none;
}

.service-option input[type="checkbox"]:checked + .option-content {
    color: #0073aa;
}

.service-option input[type="checkbox"]:checked + .option-content .option-icon {
    background: #0073aa;
    color: white;
}

.option-content {
    display: flex;
    align-items: center;
    width: 100%;
}

.option-icon {
    font-size: 24px;
    width: 45px;
    height: 45px;
    border-radius: 8px;
    background: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    transition: all 0.3s;
}

.option-text strong {
    display: block;
    margin-bottom: 3px;
    font-size: 14px;
    font-weight: 600;
}

.option-text small {
    color: #666;
    font-size: 12px;
}

/* Experience Form */
.experience-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.online-presence-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Navigation */
.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #e0e0e0;
}

.form-navigation .button {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
}

.form-navigation .button-primary {
    background: #0073aa;
    border-color: #0073aa;
}

.form-navigation .button-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

/* Responsive Design */
@media (max-width: 768px) {
    .questionnaire-form {
        padding: 20px;
    }

    .form-grid,
    .experience-form,
    .online-presence-form {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .characteristics-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .additional-services-grid {
        grid-template-columns: 1fr;
    }

    .form-navigation {
        flex-direction: column;
        gap: 15px;
    }

    .form-navigation .button {
        width: 100%;
        justify-content: center;
    }
}
</style>
