/**
 * Business Information Tab JavaScript
 * 
 * Handles business information collection interface
 * 
 * @package WebsiteGenerator
 * @version 2.0.0
 */

jQuery(document).ready(function($) {
    
    let businessInfoData = {};
    let fieldDefinitions = {};
    
    /**
     * Initialize business info tab
     */
    function initBusinessInfoTab() {
        loadBusinessInfo();
        bindEvents();
    }
    
    /**
     * Load business information from server
     */
    function loadBusinessInfo() {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_business_info',
                nonce: website_generator_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    businessInfoData = response.data.business_info;
                    fieldDefinitions = response.data.field_definitions;
                    renderBusinessInfoForm();
                    updateCompletionStatus();
                } else {
                    showNotice('Error loading business information: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotice('Failed to load business information', 'error');
            }
        });
    }
    
    /**
     * Render business information form
     */
    function renderBusinessInfoForm() {
        const categories = ['identity', 'contact', 'services', 'characteristics', 'additional'];
        
        categories.forEach(category => {
            const container = $('#' + category + '-fields');
            container.empty();
            
            // Get fields for this category
            const categoryFields = Object.keys(businessInfoData).filter(key => 
                businessInfoData[key].category === category
            ).sort((a, b) => 
                businessInfoData[a].display_order - businessInfoData[b].display_order
            );
            
            categoryFields.forEach(fieldKey => {
                const field = businessInfoData[fieldKey];
                const fieldHtml = renderField(fieldKey, field);
                container.append(fieldHtml);
            });
        });
    }
    
    /**
     * Render individual field
     */
    function renderField(fieldKey, field) {
        const value = field.current_value || '';
        const isRequired = field.is_required;
        const requiredClass = isRequired ? 'required' : '';
        const requiredMark = isRequired ? ' *' : '';
        
        let fieldHtml = `
            <div class="field-group" data-field="${fieldKey}">
                <label class="field-label ${requiredClass}" for="${fieldKey}">
                    ${field.label}${requiredMark}
                </label>
        `;
        
        if (field.description) {
            fieldHtml += `<div class="field-description">${field.description}</div>`;
        }
        
        // Render field based on type
        switch (field.field_type) {
            case 'textarea':
                fieldHtml += `
                    <textarea 
                        id="${fieldKey}" 
                        name="${fieldKey}" 
                        class="field-input" 
                        rows="3"
                        placeholder="${field.placeholder || ''}"
                        ${isRequired ? 'required' : ''}
                    >${value}</textarea>
                `;
                break;
                
            case 'select':
                fieldHtml += `<select id="${fieldKey}" name="${fieldKey}" class="field-select" ${isRequired ? 'required' : ''}>`;
                fieldHtml += '<option value="">Select an option...</option>';
                
                if (field.options) {
                    Object.keys(field.options).forEach(optionKey => {
                        const selected = value === optionKey ? 'selected' : '';
                        fieldHtml += `<option value="${optionKey}" ${selected}>${field.options[optionKey]}</option>`;
                    });
                }
                fieldHtml += '</select>';
                break;
                
            case 'checkbox':
                fieldHtml += '<div class="checkbox-group">';
                if (field.options) {
                    const selectedValues = value ? value.split(',') : [];
                    Object.keys(field.options).forEach(optionKey => {
                        const checked = selectedValues.includes(optionKey) ? 'checked' : '';
                        fieldHtml += `
                            <div class="checkbox-item">
                                <input 
                                    type="checkbox" 
                                    id="${fieldKey}_${optionKey}" 
                                    name="${fieldKey}[]" 
                                    value="${optionKey}" 
                                    ${checked}
                                >
                                <label for="${fieldKey}_${optionKey}">${field.options[optionKey]}</label>
                            </div>
                        `;
                    });
                }
                fieldHtml += '</div>';
                break;
                
            case 'number':
                fieldHtml += `
                    <input 
                        type="number" 
                        id="${fieldKey}" 
                        name="${fieldKey}" 
                        class="field-input" 
                        value="${value}"
                        placeholder="${field.placeholder || ''}"
                        ${isRequired ? 'required' : ''}
                    >
                `;
                break;
                
            case 'email':
                fieldHtml += `
                    <input 
                        type="email" 
                        id="${fieldKey}" 
                        name="${fieldKey}" 
                        class="field-input" 
                        value="${value}"
                        placeholder="${field.placeholder || ''}"
                        ${isRequired ? 'required' : ''}
                    >
                `;
                break;
                
            case 'phone':
                fieldHtml += `
                    <input 
                        type="tel" 
                        id="${fieldKey}" 
                        name="${fieldKey}" 
                        class="field-input" 
                        value="${value}"
                        placeholder="${field.placeholder || ''}"
                        ${isRequired ? 'required' : ''}
                    >
                `;
                break;
                
            case 'text':
            default:
                fieldHtml += `
                    <input 
                        type="text" 
                        id="${fieldKey}" 
                        name="${fieldKey}" 
                        class="field-input" 
                        value="${value}"
                        placeholder="${field.placeholder || ''}"
                        ${isRequired ? 'required' : ''}
                    >
                `;
                break;
        }
        
        fieldHtml += `
                <div class="field-error" id="${fieldKey}_error"></div>
            </div>
        `;
        
        return fieldHtml;
    }
    
    /**
     * Bind events
     */
    function bindEvents() {
        // Form submission
        $('#business-info-form').on('submit', function(e) {
            e.preventDefault();
            saveBusinessInfo();
        });
        
        // Field changes
        $(document).on('change input', '.field-input, .field-select, input[type="checkbox"]', function() {
            updateCompletionStatus();
            validateField($(this));
        });
        
        // Preview AI content
        $('#preview-ai-content').on('click', function() {
            showAIPreview();
        });
        
        // Modal close
        $('.ai-modal-close').on('click', function() {
            $('#ai-preview-modal').hide();
        });
        
        // Generate sample content
        $('#generate-sample-content').on('click', function() {
            generateSampleContent();
        });
    }
    
    /**
     * Save business information
     */
    function saveBusinessInfo() {
        const formData = collectFormData();
        
        // Show loading state
        const submitButton = $('#business-info-form button[type="submit"]');
        const originalText = submitButton.html();
        submitButton.html('<span class="dashicons dashicons-update spin"></span> Saving...').prop('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'save_business_info',
                nonce: website_generator_ajax.nonce,
                business_info: formData
            },
            success: function(response) {
                if (response.success) {
                    showNotice('Business information saved successfully!', 'success');
                    loadBusinessInfo(); // Reload to get updated data
                    
                    // Enable AI preview if enough info is provided
                    if (response.data.completion_rate >= 80) {
                        $('#preview-ai-content').prop('disabled', false);
                    }
                } else {
                    showNotice('Error saving business information: ' + response.data, 'error');
                    
                    // Show field-specific errors
                    if (response.data.errors) {
                        Object.keys(response.data.errors).forEach(fieldKey => {
                            showFieldError(fieldKey, response.data.errors[fieldKey]);
                        });
                    }
                }
            },
            error: function() {
                showNotice('Failed to save business information', 'error');
            },
            complete: function() {
                submitButton.html(originalText).prop('disabled', false);
            }
        });
    }
    
    /**
     * Collect form data
     */
    function collectFormData() {
        const formData = {};
        
        // Regular fields
        $('.field-input, .field-select').each(function() {
            const fieldName = $(this).attr('name');
            if (fieldName) {
                formData[fieldName] = $(this).val();
            }
        });
        
        // Checkbox fields
        $('input[type="checkbox"]:checked').each(function() {
            const fieldName = $(this).attr('name').replace('[]', '');
            if (!formData[fieldName]) {
                formData[fieldName] = [];
            }
            formData[fieldName].push($(this).val());
        });
        
        return formData;
    }
    
    /**
     * Update completion status
     */
    function updateCompletionStatus() {
        const formData = collectFormData();
        let totalRequired = 0;
        let completedRequired = 0;
        
        Object.keys(businessInfoData).forEach(fieldKey => {
            const field = businessInfoData[fieldKey];
            if (field.is_required) {
                totalRequired++;
                if (formData[fieldKey] && formData[fieldKey] !== '') {
                    completedRequired++;
                }
            }
        });
        
        const completionRate = totalRequired > 0 ? Math.round((completedRequired / totalRequired) * 100) : 100;
        
        $('#completion-progress').css('width', completionRate + '%');
        $('#completion-text').text(completionRate + '% Complete');
        
        // Enable/disable AI preview based on completion
        $('#preview-ai-content').prop('disabled', completionRate < 80);
    }
    
    /**
     * Validate individual field
     */
    function validateField($field) {
        const fieldName = $field.attr('name');
        const value = $field.val();
        const fieldData = businessInfoData[fieldName];
        
        if (!fieldData) return;
        
        clearFieldError(fieldName);
        
        // Check required fields
        if (fieldData.is_required && (!value || value.trim() === '')) {
            showFieldError(fieldName, fieldData.label + ' is required');
            return false;
        }
        
        // Validate email
        if (fieldData.field_type === 'email' && value && !isValidEmail(value)) {
            showFieldError(fieldName, 'Please enter a valid email address');
            return false;
        }
        
        // Validate phone
        if (fieldData.field_type === 'phone' && value && !isValidPhone(value)) {
            showFieldError(fieldName, 'Please enter a valid phone number');
            return false;
        }
        
        return true;
    }
    
    /**
     * Show field error
     */
    function showFieldError(fieldName, message) {
        const errorElement = $('#' + fieldName + '_error');
        const fieldElement = $('#' + fieldName);
        
        errorElement.text(message).show();
        fieldElement.addClass('error');
    }
    
    /**
     * Clear field error
     */
    function clearFieldError(fieldName) {
        const errorElement = $('#' + fieldName + '_error');
        const fieldElement = $('#' + fieldName);
        
        errorElement.hide();
        fieldElement.removeClass('error');
    }
    
    /**
     * Show AI preview modal
     */
    function showAIPreview() {
        const formData = collectFormData();
        
        let previewHtml = '<div class="ai-preview-sections">';
        
        // Show how data will be used
        previewHtml += '<h4>Your Business Information:</h4>';
        previewHtml += '<ul>';
        
        Object.keys(formData).forEach(key => {
            const field = businessInfoData[key];
            if (field && formData[key]) {
                let value = formData[key];
                if (Array.isArray(value)) {
                    value = value.join(', ');
                }
                previewHtml += `<li><strong>${field.label}:</strong> ${value}</li>`;
            }
        });
        
        previewHtml += '</ul>';
        previewHtml += '<p><em>This information will be used to generate personalized content for your website.</em></p>';
        previewHtml += '</div>';
        
        $('#ai-preview-content').html(previewHtml);
        $('#ai-preview-modal').show();
    }
    
    /**
     * Generate sample content
     */
    function generateSampleContent() {
        // This will be implemented when AI integration is ready
        showNotice('AI content generation will be available once AI configuration is complete.', 'info');
    }
    
    /**
     * Utility functions
     */
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function isValidPhone(phone) {
        const phoneRegex = /^[\+]?[1-9]?[\-\s\(\)]?[0-9]{3}[\-\s\(\)]?[0-9]{3}[\-\s]?[0-9]{4}$/;
        return phoneRegex.test(phone.replace(/[^\d\+\-\s\(\)]/g, ''));
    }
    
    function showNotice(message, type) {
        // Create notice element
        const noticeClass = type === 'error' ? 'notice-error' : (type === 'success' ? 'notice-success' : 'notice-info');
        const notice = $(`
            <div class="notice ${noticeClass} is-dismissible">
                <p>${message}</p>
                <button type="button" class="notice-dismiss">
                    <span class="screen-reader-text">Dismiss this notice.</span>
                </button>
            </div>
        `);
        
        // Add to page
        $('.business-info-header').after(notice);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            notice.fadeOut(() => notice.remove());
        }, 5000);
        
        // Manual dismiss
        notice.find('.notice-dismiss').on('click', function() {
            notice.fadeOut(() => notice.remove());
        });
    }
    
    // Initialize when tab is shown
    if ($('.business-info-tab').length > 0) {
        initBusinessInfoTab();
    }
    
    // Initialize when tab becomes active
    $(document).on('click', '[data-tab="business-info"]', function() {
        setTimeout(initBusinessInfoTab, 100);
    });
});
