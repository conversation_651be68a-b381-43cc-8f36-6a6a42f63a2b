<?php
echo "🧪 Simple Field Standardization Test\n\n";

// Check if the field mapper file exists
if (file_exists('includes/airtable-field-mapper.php')) {
    echo "✅ Field mapper file found\n";
    
    // Mock WordPress functions
    if (!function_exists('sanitize_text_field')) {
        function sanitize_text_field($str) {
            return trim(strip_tags($str));
        }
    }
    
    if (!function_exists('esc_html')) {
        function esc_html($str) {
            return htmlspecialchars($str, ENT_QUOTES, 'UTF-8');
        }
    }
    
    try {
        require_once 'includes/airtable-field-mapper.php';
        echo "✅ Field mapper loaded successfully\n";
        
        $field_mapper = new AirtableFieldMapper();
        echo "✅ Field mapper instantiated\n";
        
        // Test the field mapping
        $test_data = array(
            'hero_title' => 'Test Title',
            'hero_tagline' => 'Test Tagline'
        );
        
        echo "\n📝 Testing with data:\n";
        print_r($test_data);
        
        $result = $field_mapper->map_airtable_to_wordpress($test_data);
        
        echo "\n📤 Mapping result:\n";
        print_r($result);
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
    
} else {
    echo "❌ Field mapper file not found\n";
}

echo "\n✅ Test complete\n";
?>
