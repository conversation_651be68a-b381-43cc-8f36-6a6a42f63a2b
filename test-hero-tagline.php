<?php
/**
 * Test Hero Tagline Integration
 * 
 * This script tests the hero tagline automation to ensure it works
 * exactly like the hero heading automation.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing outside WordPress, define ABSPATH
    define('ABSPATH', dirname(__FILE__) . '/');
}

/**
 * Test Hero Tagline Integration
 */
function test_hero_tagline_integration() {
    echo "<h2>🧪 Hero Tagline Integration Test</h2>\n";
    
    // Test 1: Field Mapping
    echo "<h3>1. Testing Field Mapping</h3>\n";
    
    require_once 'includes/airtable-field-mapper.php';
    $field_mapper = new AirtableFieldMapper();
    
    $test_data = array(
        'Hero Heading' => 'Test Hero Heading from Airtable',
        'Hero Tagline' => 'Test Hero Tagline from Airtable',
        'business_name' => 'Test Repair Shop'
    );
    
    $mapped_data = $field_mapper->map_airtable_to_wordpress($test_data);
    
    if (isset($mapped_data['visual_blocks']['hero_tagline'])) {
        echo "✅ Field mapping successful: hero_tagline = " . $mapped_data['visual_blocks']['hero_tagline'] . "\n";
    } else {
        echo "❌ Field mapping failed: hero_tagline not found\n";
        print_r($mapped_data);
    }
    
    // Test 2: Character Limit Validation
    echo "<h3>2. Testing Character Limit Validation</h3>\n";
    
    require_once 'includes/airtable-webhook-handler.php';
    $webhook_handler = new AirtableWebhookHandler();
    
    $test_long_tagline = str_repeat('a', 100); // Too long (80 char limit)
    $validation_result = $webhook_handler->validate_field_lengths(array(
        'hero_tagline' => $test_long_tagline
    ));
    
    if (!$validation_result['valid']) {
        echo "✅ Character limit validation working: " . implode(', ', $validation_result['errors']) . "\n";
    } else {
        echo "❌ Character limit validation failed: should reject 100-character tagline\n";
    }
    
    // Test 3: Text Manager Integration
    echo "<h3>3. Testing TextManager Integration</h3>\n";
    
    require_once 'includes/text-manager.php';
    $text_manager = new TextManager();
    
    // Simulate saving hero tagline to WordPress options
    $test_tagline = 'Test Tagline from Airtable Integration';
    $custom_texts = array('hero_tagline' => $test_tagline);
    update_option('website_generator_custom_texts', $custom_texts);
    
    // Test content replacement
    $test_content = '<p>Smartphones | Tablets | Computers | & More</p>';
    $replaced_content = $text_manager->replace_content_text($test_content);
    
    if (strpos($replaced_content, $test_tagline) !== false) {
        echo "✅ TextManager replacement working: Content updated with new tagline\n";
    } else {
        echo "❌ TextManager replacement failed: Content not updated\n";
        echo "Original: " . $test_content . "\n";
        echo "Replaced: " . $replaced_content . "\n";
    }
    
    // Test 4: Integration Test
    echo "<h3>4. Testing Complete Integration</h3>\n";
    
    $complete_test_data = array(
        'api_key' => 'test-key',
        'site_identifier' => 'test-site',
        'Hero Heading' => 'Complete Test Hero Heading',
        'Hero Tagline' => 'Complete Test Hero Tagline',
        'business_name' => 'Complete Test Shop'
    );
    
    $complete_mapped = $field_mapper->map_airtable_to_wordpress($complete_test_data);
    
    if (isset($complete_mapped['visual_blocks']['hero_tagline']) && 
        isset($complete_mapped['visual_blocks']['hero_title'])) {
        echo "✅ Complete integration test passed: Both hero_title and hero_tagline mapped\n";
        echo "   - hero_title: " . $complete_mapped['visual_blocks']['hero_title'] . "\n";
        echo "   - hero_tagline: " . $complete_mapped['visual_blocks']['hero_tagline'] . "\n";
    } else {
        echo "❌ Complete integration test failed\n";
        print_r($complete_mapped);
    }
    
    echo "<h3>✅ Hero Tagline Integration Test Complete</h3>\n";
    echo "<p>The hero tagline should now work exactly like the hero heading automation.</p>\n";
}

// Run the test if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    test_hero_tagline_integration();
}
?>
