<?php

/**
 * Simplified Content Manager for RepairLift Plugin Backup System
 * Only handles plugin-specific data - no WordPress core data
 */
class ContentManager {

    /**
     * Create a backup of current plugin state
     */
    public function create_backup($description = '') {
        try {
            $backup_data = array(
                'version' => '2.0.0',
                'timestamp' => current_time('mysql'),
                'description' => sanitize_text_field($description),
                'data' => $this->get_plugin_data()
            );

            // Generate checksum for integrity
            $backup_data['checksum'] = md5(serialize($backup_data['data']));

            // Get existing backups
            $backups = get_option('website_generator_backups', array());

            // Create backup ID
            $backup_id = 'backup_' . time();
            $backups[$backup_id] = $backup_data;

            // Keep only last 10 backups
            if (count($backups) > 10) {
                $backups = array_slice($backups, -10, 10, true);
            }

            // Save backups
            $save_result = update_option('website_generator_backups', $backups);

            // Debug logging
            error_log('Website Generator: Backup created - ID: ' . $backup_id . ', Save result: ' . ($save_result ? 'success' : 'failed'));
            error_log('Website Generator: Total backups after creation: ' . count($backups));

            return array(
                'status' => 'success',
                'backup_id' => $backup_id,
                'message' => 'Plugin backup created successfully'
            );

        } catch (Exception $e) {
            return array(
                'status' => 'error',
                'message' => 'Backup failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get all data that belongs to this plugin
     */
    private function get_plugin_data() {
        return array(
            'plugin_options' => $this->get_plugin_options(),
            'custom_texts' => get_option('website_generator_custom_texts', array()),
            'theme_mods' => $this->get_plugin_theme_mods(),
            'logo_reference' => $this->get_logo_reference()
        );
    }

    /**
     * Get all plugin-specific options
     */
    private function get_plugin_options() {
        $options = array();
        $plugin_option_keys = array(
            'website_generator_primary_color',
            'website_generator_secondary_color',
            'website_generator_accent_color',
            'website_generator_button_style',
            'website_generator_button_size',
            'website_generator_button_text_color',
            'website_generator_typography_heading',
            'website_generator_typography_body',
            'website_generator_hero_animation',
            'website_generator_image_animation',
            'website_generator_animation_speed',
            'website_generator_section_spacing'
        );

        foreach ($plugin_option_keys as $key) {
            $value = get_option($key);
            if ($value !== false) {
                $options[$key] = $value;
            }
        }

        return $options;
    }

    /**
     * Get only theme mods that our plugin modifies
     */
    private function get_plugin_theme_mods() {
        return array(
            'custom_logo' => get_theme_mod('custom_logo'),
            'primary_color' => get_theme_mod('primary_color'),
            'secondary_color' => get_theme_mod('secondary_color')
        );
    }

    /**
     * Get logo reference information
     */
    private function get_logo_reference() {
        $logo_id = get_theme_mod('custom_logo');
        if (!$logo_id) {
            return array('has_logo' => false);
        }

        return array(
            'has_logo' => true,
            'attachment_id' => $logo_id,
            'url' => wp_get_attachment_url($logo_id),
            'filename' => get_the_title($logo_id)
        );
    }

    /**
     * Restore backup with integrity verification
     */
    public function restore_backup($backup_id) {
        try {
            $backups = get_option('website_generator_backups', array());

            if (!isset($backups[$backup_id])) {
                return array('status' => 'error', 'message' => 'Backup not found');
            }

            $backup = $backups[$backup_id];

            // Verify backup integrity
            $expected_checksum = $backup['checksum'];
            $actual_checksum = md5(serialize($backup['data']));

            if ($expected_checksum !== $actual_checksum) {
                return array('status' => 'error', 'message' => 'Backup data corrupted');
            }

            // Restore plugin options
            if (isset($backup['data']['plugin_options'])) {
                foreach ($backup['data']['plugin_options'] as $key => $value) {
                    update_option($key, $value);
                }
            }

            // Restore custom texts
            if (isset($backup['data']['custom_texts'])) {
                update_option('website_generator_custom_texts', $backup['data']['custom_texts']);
            }

            // Restore theme mods
            if (isset($backup['data']['theme_mods'])) {
                foreach ($backup['data']['theme_mods'] as $key => $value) {
                    if ($value !== false && $value !== null) {
                        set_theme_mod($key, $value);
                    } else {
                        remove_theme_mod($key);
                    }
                }
            }

            // Restore logo (validate attachment exists)
            if (isset($backup['data']['logo_reference'])) {
                $this->restore_logo_reference($backup['data']['logo_reference']);
            }

            return array(
                'status' => 'success',
                'message' => 'Plugin backup restored successfully'
            );

        } catch (Exception $e) {
            return array(
                'status' => 'error',
                'message' => 'Restore failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Restore logo reference with validation
     */
    private function restore_logo_reference($logo_ref) {
        if (!$logo_ref['has_logo']) {
            remove_theme_mod('custom_logo');
            return;
        }

        // Verify attachment still exists
        $attachment = get_post($logo_ref['attachment_id']);
        if ($attachment && $attachment->post_type === 'attachment') {
            set_theme_mod('custom_logo', $logo_ref['attachment_id']);
        } else {
            error_log('Website Generator: Logo attachment not found during restore: ' . $logo_ref['attachment_id']);
        }
    }

    /**
     * Get all backups
     */
    public function get_backups() {
        $backups = get_option('website_generator_backups', array());

        // Debug logging
        error_log('Website Generator: Retrieved ' . count($backups) . ' backups from database');
        if (!empty($backups)) {
            error_log('Website Generator: Backup IDs: ' . implode(', ', array_keys($backups)));
        }

        return $backups;
    }

    /**
     * Delete a backup
     */
    public function delete_backup($backup_id) {
        $backups = get_option('website_generator_backups', array());

        if (!isset($backups[$backup_id])) {
            return array('status' => 'error', 'message' => 'Backup not found');
        }

        unset($backups[$backup_id]);
        update_option('website_generator_backups', $backups);

        return array(
            'status' => 'success',
            'message' => 'Backup deleted successfully'
        );
    }
}