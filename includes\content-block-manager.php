<?php

/**
 * ContentBlockManager - Professional Gutenberg Block Content Management
 * 
 * This class safely parses and updates Gutenberg blocks in the database
 * without relying on JavaScript text replacement hacks.
 */
class ContentBlockManager {
    
    /**
     * Update hero heading in the homepage content
     */
    public function update_hero_heading($new_heading) {
        try {
            // Get homepage
            $homepage_id = get_option('page_on_front');
            if (!$homepage_id) {
                return array('success' => false, 'message' => 'No homepage set');
            }
            
            $homepage = get_post($homepage_id);
            if (!$homepage) {
                return array('success' => false, 'message' => 'Homepage not found');
            }
            
            // Create backup first
            $this->create_content_backup($homepage_id, 'hero_heading_update');
            
            // Parse and update content
            $updated_content = $this->update_heading_in_content($homepage->post_content, $new_heading);
            
            if ($updated_content === false) {
                return array('success' => false, 'message' => 'Failed to find hero heading block');
            }
            
            // Update the post
            $result = wp_update_post(array(
                'ID' => $homepage_id,
                'post_content' => $updated_content
            ));
            
            if (is_wp_error($result)) {
                return array('success' => false, 'message' => 'Failed to update homepage: ' . $result->get_error_message());
            }
            
            // Clear any caches
            wp_cache_delete($homepage_id, 'posts');
            clean_post_cache($homepage_id);

            // Force clear any object cache
            if (function_exists('wp_cache_flush')) {
                wp_cache_flush();
            }

            // Verify the update worked by re-reading the post
            $updated_post = get_post($homepage_id);
            $verification_heading = $this->extract_hero_heading_from_content($updated_post->post_content);

            return array(
                'success' => true,
                'message' => 'Hero heading updated successfully',
                'old_content_length' => strlen($homepage->post_content),
                'new_content_length' => strlen($updated_content),
                'verification_heading' => $verification_heading,
                'post_id' => $homepage_id,
                'update_result_id' => $result
            );
            
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }
    
    /**
     * Update heading content in Gutenberg blocks
     */
    private function update_heading_in_content($content, $new_heading) {
        // Parse Gutenberg blocks
        $blocks = parse_blocks($content);
        
        if (empty($blocks)) {
            return false;
        }
        
        // Find and update the hero heading
        $updated = false;
        $blocks = $this->update_blocks_recursive($blocks, $new_heading, $updated);
        
        if (!$updated) {
            return false;
        }
        
        // Serialize blocks back to content
        return serialize_blocks($blocks);
    }
    
    /**
     * Recursively search and update blocks
     */
    private function update_blocks_recursive($blocks, $new_heading, &$updated) {
        foreach ($blocks as &$block) {
            // Check if this is a heading block
            if ($block['blockName'] === 'core/heading') {
                // Check if this looks like the hero heading
                if ($this->is_hero_heading_block($block)) {
                    $old_content = strip_tags($block['innerHTML'] ?? '');
                    $old_innerHTML = $block['innerHTML'] ?? '';

                    // Update the heading content
                    $new_innerHTML = $this->generate_heading_html($block, $new_heading);
                    $block['innerHTML'] = $new_innerHTML;

                    // CRITICAL: Also update the innerContent array if it exists
                    if (isset($block['innerContent']) && is_array($block['innerContent'])) {
                        // Find and replace the HTML content in innerContent
                        for ($i = 0; $i < count($block['innerContent']); $i++) {
                            if (is_string($block['innerContent'][$i]) && strpos($block['innerContent'][$i], '<h1') !== false) {
                                $block['innerContent'][$i] = $new_innerHTML;
                                break;
                            }
                        }
                    }

                    $updated = true;

                    // Log the update
                    error_log('ContentBlockManager: Updated hero heading from "' . $old_content . '" to "' . $new_heading . '"');
                    error_log('ContentBlockManager: Old innerHTML: ' . $old_innerHTML);
                    error_log('ContentBlockManager: New innerHTML: ' . $new_innerHTML);
                    error_log('ContentBlockManager: innerContent updated: ' . (isset($block['innerContent']) ? 'YES' : 'NO'));
                    break;
                }
            }
            
            // Recursively check inner blocks
            if (!empty($block['innerBlocks'])) {
                $block['innerBlocks'] = $this->update_blocks_recursive($block['innerBlocks'], $new_heading, $updated);
            }
        }
        
        return $blocks;
    }
    
    /**
     * Check if this is the hero heading block
     */
    private function is_hero_heading_block($block) {
        // Check if it's an H1 heading
        if (isset($block['attrs']['level']) && $block['attrs']['level'] !== 1) {
            error_log('ContentBlockManager: Skipping heading - not H1, level: ' . $block['attrs']['level']);
            return false;
        }

        // Check for hero-specific classes or content
        $innerHTML = $block['innerHTML'] ?? '';
        $text_content = strip_tags($innerHTML);

        error_log('ContentBlockManager: Checking H1 block - innerHTML: ' . $innerHTML);
        error_log('ContentBlockManager: Text content: ' . $text_content);

        // Look for hero-specific classes (most reliable method)
        if (strpos($innerHTML, 'home-h1') !== false ||
            strpos($innerHTML, 'safari_only_heading2') !== false) {
            error_log('ContentBlockManager: Found hero heading by class (home-h1 or safari_only_heading2)');
            return true;
        }

        // Look for the exact current hero text (with potential trailing space)
        $trimmed_content = trim($text_content);
        if (stripos($trimmed_content, 'premier device repair in anytown') !== false ||
            stripos($trimmed_content, 'premier device repair in medellin') !== false ||
            stripos($trimmed_content, 'device repair in anytown') !== false ||
            stripos($trimmed_content, 'device repair in medellin') !== false) {
            error_log('ContentBlockManager: Found hero heading by exact content match');
            return true;
        }

        // Look for device repair related content (broader match)
        if (stripos($text_content, 'device repair') !== false ||
            stripos($text_content, 'repair') !== false) {
            error_log('ContentBlockManager: Found hero heading by repair keywords');
            return true;
        }

        // If it's an H1 and we haven't found a better match, skip it
        error_log('ContentBlockManager: Skipping H1 - does not match hero criteria: ' . $text_content);
        return false;
    }
    
    /**
     * Generate updated heading HTML
     */
    private function generate_heading_html($block, $new_heading) {
        $innerHTML = $block['innerHTML'] ?? '';

        error_log('ContentBlockManager: Original innerHTML: ' . $innerHTML);

        // Extract the opening and closing tags, replace content
        if (preg_match('/^(<h1[^>]*>).*?(<\/h1>)$/s', $innerHTML, $matches)) {
            $new_html = $matches[1] . esc_html($new_heading) . ' ' . $matches[2];
            error_log('ContentBlockManager: Generated new HTML: ' . $new_html);
            return $new_html;
        }

        // Fallback: create basic H1
        $fallback = '<h1 class="wp-block-heading">' . esc_html($new_heading) . '</h1>';
        error_log('ContentBlockManager: Using fallback HTML: ' . $fallback);
        return $fallback;
    }
    
    /**
     * Create backup before content changes
     */
    private function create_content_backup($post_id, $reason) {
        $post = get_post($post_id);
        if (!$post) {
            return false;
        }
        
        $backup_data = array(
            'post_id' => $post_id,
            'post_content' => $post->post_content,
            'post_title' => $post->post_title,
            'reason' => $reason,
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id()
        );
        
        // Store backup in WordPress options
        $backups = get_option('content_block_backups', array());
        $backup_id = 'content_backup_' . time() . '_' . wp_generate_password(8, false);
        $backups[$backup_id] = $backup_data;
        
        // Keep only last 10 backups
        if (count($backups) > 10) {
            $backups = array_slice($backups, -10, 10, true);
        }
        
        update_option('content_block_backups', $backups);
        
        error_log('ContentBlockManager: Created backup ' . $backup_id . ' for post ' . $post_id);
        
        return $backup_id;
    }
    
    /**
     * Test method to analyze content structure
     */
    public function analyze_homepage_structure() {
        $homepage_id = get_option('page_on_front');
        if (!$homepage_id) {
            return array('error' => 'No homepage set');
        }

        $homepage = get_post($homepage_id);
        if (!$homepage) {
            return array('error' => 'Homepage not found');
        }

        $blocks = parse_blocks($homepage->post_content);

        return array(
            'post_id' => $homepage_id,
            'content_length' => strlen($homepage->post_content),
            'blocks_count' => count($blocks),
            'blocks_structure' => $this->analyze_blocks_structure($blocks),
            'all_headings_found' => $this->find_all_headings($blocks),
            'raw_content_preview' => substr($homepage->post_content, 0, 1000)
        );
    }

    /**
     * Find all heading blocks for debugging
     */
    private function find_all_headings($blocks, $level = 0) {
        $headings = array();

        foreach ($blocks as $index => $block) {
            if ($block['blockName'] === 'core/heading') {
                $headings[] = array(
                    'level' => $level,
                    'index' => $index,
                    'heading_level' => $block['attrs']['level'] ?? 1,
                    'content' => strip_tags($block['innerHTML'] ?? ''),
                    'innerHTML' => $block['innerHTML'] ?? '',
                    'classes' => $this->extract_classes($block['innerHTML'] ?? ''),
                    'is_hero' => $this->is_hero_heading_block($block)
                );
            }

            if (!empty($block['innerBlocks'])) {
                $inner_headings = $this->find_all_headings($block['innerBlocks'], $level + 1);
                $headings = array_merge($headings, $inner_headings);
            }
        }

        return $headings;
    }
    
    /**
     * Analyze blocks structure for debugging
     */
    private function analyze_blocks_structure($blocks, $level = 0) {
        $structure = array();
        
        foreach ($blocks as $index => $block) {
            $block_info = array(
                'index' => $index,
                'blockName' => $block['blockName'],
                'level' => $level
            );
            
            if ($block['blockName'] === 'core/heading') {
                $block_info['heading_level'] = $block['attrs']['level'] ?? 1;
                $block_info['content'] = strip_tags($block['innerHTML'] ?? '');
                $block_info['classes'] = $this->extract_classes($block['innerHTML'] ?? '');
            }
            
            if (!empty($block['innerBlocks'])) {
                $block_info['innerBlocks'] = $this->analyze_blocks_structure($block['innerBlocks'], $level + 1);
            }
            
            $structure[] = $block_info;
        }
        
        return $structure;
    }
    
    /**
     * Extract CSS classes from HTML
     */
    private function extract_classes($html) {
        if (preg_match('/class="([^"]*)"/', $html, $matches)) {
            return $matches[1];
        }
        return '';
    }

    /**
     * Extract hero heading from content for verification
     */
    private function extract_hero_heading_from_content($content) {
        $blocks = parse_blocks($content);

        if (empty($blocks)) {
            return 'No blocks found';
        }

        return $this->find_hero_heading_recursive($blocks);
    }

    /**
     * Recursively find hero heading in blocks
     */
    private function find_hero_heading_recursive($blocks) {
        foreach ($blocks as $block) {
            // Check if this is a heading block
            if ($block['blockName'] === 'core/heading') {
                // Check if this looks like the hero heading
                if ($this->is_hero_heading_block($block)) {
                    return strip_tags($block['innerHTML'] ?? 'No innerHTML');
                }
            }

            // Recursively check inner blocks
            if (!empty($block['innerBlocks'])) {
                $result = $this->find_hero_heading_recursive($block['innerBlocks']);
                if ($result !== 'No hero heading found') {
                    return $result;
                }
            }
        }

        return 'No hero heading found';
    }
}
