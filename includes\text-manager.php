<?php

class TextManager {
    
    /**
     * Get all editable text content from the website
     */
    public function get_all_text_content() {
        $text_content = array();
        
        // Get homepage content
        $homepage_id = get_option('page_on_front');
        if ($homepage_id) {
            $homepage = get_post($homepage_id);
            if ($homepage) {
                $text_content['homepage'] = $this->extract_text_from_content($homepage->post_content, 'Homepage');
            }
        }
        
        // Get site identity
        $text_content['site_identity'] = array(
            'site_title' => array(
                'label' => 'Site Title',
                'current_value' => get_option('blogname'),
                'location' => 'Header, Footer, Browser Tab',
                'type' => 'site_option',
                'key' => 'blogname'
            ),
            'site_tagline' => array(
                'label' => 'Site Tagline',
                'current_value' => get_option('blogdescription'),
                'location' => 'Header area',
                'type' => 'site_option',
                'key' => 'blogdescription'
            )
        );
        
        // Get custom texts (stored in our plugin)
        $custom_texts = get_option('website_generator_custom_texts', array());
        
        // Define common text elements found on the site
        $default_texts = array(
            'phone_number' => array(
                'label' => 'Phone Number',
                'current_value' => isset($custom_texts['phone_number']) ? $custom_texts['phone_number'] : '(*************',
                'location' => 'Header, Call buttons',
                'type' => 'custom_text',
                'key' => 'phone_number'
            ),
            'address' => array(
                'label' => 'Business Address',
                'current_value' => isset($custom_texts['address']) ? $custom_texts['address'] : '123 Main Street, Anytown, FL 12345',
                'location' => 'Footer, Contact section',
                'type' => 'custom_text',
                'key' => 'address'
            ),
            'hero_title' => array(
                'label' => 'Main Headline',
                'current_value' => isset($custom_texts['hero_title']) ? $custom_texts['hero_title'] : 'Premier Device Repair in Anytown, FL',
                'location' => 'Homepage hero section',
                'type' => 'custom_text',
                'key' => 'hero_title'
            ),
            'hero_tagline' => array(
                'label' => 'Hero Tagline',
                'current_value' => isset($custom_texts['hero_tagline']) ? $custom_texts['hero_tagline'] : 'Smartphones | Tablets | Computers | & More',
                'location' => 'Homepage hero section',
                'type' => 'custom_text',
                'key' => 'hero_tagline'
            ),
            'section_title_devices' => array(
                'label' => 'Device Selection Title',
                'current_value' => isset($custom_texts['section_title_devices']) ? $custom_texts['section_title_devices'] : 'Select Your Device To Get Started',
                'location' => 'Device selection section',
                'type' => 'custom_text',
                'key' => 'section_title_devices'
            ),
            'section_subtitle_devices' => array(
                'label' => 'Device Selection Subtitle',
                'current_value' => isset($custom_texts['section_subtitle_devices']) ? $custom_texts['section_subtitle_devices'] : 'What Do You Need Repaired?',
                'location' => 'Device selection section',
                'type' => 'custom_text',
                'key' => 'section_subtitle_devices'
            ),
            'about_title' => array(
                'label' => 'About Section Title',
                'current_value' => isset($custom_texts['about_title']) ? $custom_texts['about_title'] : 'Your One-Stop Shop Repair Store',
                'location' => 'About section',
                'type' => 'custom_text',
                'key' => 'about_title'
            ),
            'about_description' => array(
                'label' => 'About Description',
                'current_value' => isset($custom_texts['about_description']) ? $custom_texts['about_description'] : 'Same Day Service on Major Brands & Devices. Visit us at a location near you. We take pride in what we do. And what we do best is restore your device back to its original condition. You can rest easy when you have your device repaired with us. We offer a warranty on all our repairs.',
                'location' => 'About section',
                'type' => 'custom_text',
                'key' => 'about_description'
            ),
            'features_title' => array(
                'label' => 'Features Section Title',
                'current_value' => isset($custom_texts['features_title']) ? $custom_texts['features_title'] : 'Why Our Customers Love Us',
                'location' => 'Features section',
                'type' => 'custom_text',
                'key' => 'features_title'
            ),
            'cta_title' => array(
                'label' => 'Call-to-Action Title',
                'current_value' => isset($custom_texts['cta_title']) ? $custom_texts['cta_title'] : 'Repair Your Device Today!',
                'location' => 'Bottom CTA section',
                'type' => 'custom_text',
                'key' => 'cta_title'
            ),
            'cta_description' => array(
                'label' => 'Call-to-Action Description',
                'current_value' => isset($custom_texts['cta_description']) ? $custom_texts['cta_description'] : 'We offer reliable and professional service for your electronic device by using the highest quality replacement parts currently available in the market.',
                'location' => 'Bottom CTA section',
                'type' => 'custom_text',
                'key' => 'cta_description'
            ),
            'copyright_text' => array(
                'label' => 'Copyright Text',
                'current_value' => $this->get_auto_updated_copyright_text(),
                'location' => 'Footer',
                'type' => 'custom_text',
                'key' => 'copyright_text'
            )
        );
        
        $text_content['custom_texts'] = $default_texts;
        
        return $text_content;
    }

    /**
     * Get auto-updated copyright text with current year
     */
    private function get_auto_updated_copyright_text() {
        $custom_texts = get_option('website_generator_custom_texts', array());
        $current_year = date('Y');

        // Get stored copyright text or use default
        $copyright_text = isset($custom_texts['copyright_text'])
            ? $custom_texts['copyright_text']
            : '© 2023 All Rights Reserved | Not affiliated with Apple, Inc., LG Corp. or Samsung Corp';

        // Update any year in the copyright text to current year
        $updated_copyright = preg_replace('/© \d{4}/', '© ' . $current_year, $copyright_text);

        // If the copyright text was updated, save it back
        if ($updated_copyright !== $copyright_text) {
            $custom_texts['copyright_text'] = $updated_copyright;
            update_option('website_generator_custom_texts', $custom_texts);
        }

        return $updated_copyright;
    }

    /**
     * Update text content
     */
    public function update_text_content($updates) {
        $results = array();
        
        foreach ($updates as $key => $value) {
            $sanitized_value = sanitize_text_field($value);
            
            // Check if it's a site option or custom text
            if (in_array($key, array('blogname', 'blogdescription'))) {
                update_option($key, $sanitized_value);
                $results[$key] = 'Updated site option: ' . $key;
            } else {
                // Store as custom text
                $custom_texts = get_option('website_generator_custom_texts', array());
                $custom_texts[$key] = $sanitized_value;
                update_option('website_generator_custom_texts', $custom_texts);
                $results[$key] = 'Updated custom text: ' . $key;
            }
        }
        
        return array(
            'status' => 'success',
            'results' => $results,
            'message' => 'Text content updated successfully'
        );
    }
    
    /**
     * Extract text content from HTML content
     */
    private function extract_text_from_content($content, $section_name) {
        // This is a simplified version - in a full implementation,
        // you'd parse the HTML and extract specific text elements
        return array(
            'content' => array(
                'label' => $section_name . ' Content',
                'current_value' => wp_strip_all_tags($content),
                'location' => $section_name,
                'type' => 'post_content',
                'key' => 'homepage_content'
            )
        );
    }
    
    /**
     * Get unused files and assets
     */
    public function get_unused_assets() {
        $unused_assets = array();
        
        // Check for unused plugins
        $all_plugins = get_plugins();
        $active_plugins = get_option('active_plugins');
        
        foreach ($all_plugins as $plugin_file => $plugin_data) {
            if (!in_array($plugin_file, $active_plugins)) {
                $unused_assets['plugins'][] = array(
                    'name' => $plugin_data['Name'],
                    'file' => $plugin_file,
                    'description' => $plugin_data['Description']
                );
            }
        }
        
        // Check for unused themes
        $all_themes = wp_get_themes();
        $current_theme = get_stylesheet();
        
        foreach ($all_themes as $theme_slug => $theme_data) {
            if ($theme_slug !== $current_theme) {
                $unused_assets['themes'][] = array(
                    'name' => $theme_data->get('Name'),
                    'slug' => $theme_slug,
                    'description' => $theme_data->get('Description')
                );
            }
        }
        
        return $unused_assets;
    }

    /**
     * Apply custom texts to the website by hooking into WordPress
     */
    public function apply_custom_texts() {
        $custom_texts = get_option('website_generator_custom_texts', array());

        if (empty($custom_texts)) {
            return;
        }

        // Disable JavaScript replacement - use server-side only
        // add_action('wp_head', array($this, 'inject_text_replacement_script'));
        add_filter('the_content', array($this, 'replace_content_text'));
        add_filter('wp_title', array($this, 'replace_title_text'));
    }

    /**
     * Inject JavaScript to replace text content on the frontend
     */
    public function inject_text_replacement_script() {
        $custom_texts = get_option('website_generator_custom_texts', array());

        if (empty($custom_texts)) {
            return;
        }

        echo '<script type="text/javascript">';
        echo 'document.addEventListener("DOMContentLoaded", function() {';

        // Replace text content using JavaScript
        foreach ($custom_texts as $key => $value) {
            $escaped_value = esc_js($value);

            switch ($key) {
                case 'phone_number':
                    echo 'document.querySelectorAll("a[href*=\'tel:\'], .phone").forEach(function(el) {';
                    echo '  if (el.textContent.includes("(*************")) {';
                    echo '    el.textContent = el.textContent.replace("(*************", "' . $escaped_value . '");';
                    echo '    if (el.href && el.href.includes("tel:")) {';
                    echo '      el.href = "tel:" + "' . $escaped_value . '".replace(/[^0-9]/g, "");';
                    echo '    }';
                    echo '  }';
                    echo '});';
                    break;

                case 'address':
                    echo 'document.querySelectorAll("p, div, span").forEach(function(el) {';
                    echo '  if (el.textContent.includes("123 Main Street, Anytown, FL 12345")) {';
                    echo '    el.innerHTML = el.innerHTML.replace("123 Main Street, Anytown, FL 12345", "' . $escaped_value . '");';
                    echo '  }';
                    echo '});';
                    break;

                case 'hero_title':
                    echo 'document.querySelectorAll("h1").forEach(function(el) {';
                    echo '  // Check if this H1 contains any device repair related text';
                    echo '  var text = el.textContent.toLowerCase();';
                    echo '  if (text.includes("device repair") || text.includes("repair") || text.includes("premier") || text.includes("anytown") || text.includes("medellin")) {';
                    echo '    console.log("Replacing hero title: " + el.textContent + " -> ' . $escaped_value . '");';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;

                case 'hero_tagline':
                    echo 'document.querySelectorAll("p").forEach(function(el) {';
                    echo '  var text = el.textContent.toLowerCase();';
                    echo '  if (text.includes("smartphones") || text.includes("tablets") || text.includes("computers") || text.includes("& more")) {';
                    echo '    console.log("✅ Replacing hero tagline: " + el.textContent + " -> ' . $escaped_value . '");';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;

                case 'section_title_devices':
                    echo 'document.querySelectorAll("h2").forEach(function(el) {';
                    echo '  if (el.textContent.includes("Select Your Device To Get Started")) {';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;

                case 'section_subtitle_devices':
                    echo 'document.querySelectorAll("p").forEach(function(el) {';
                    echo '  if (el.textContent.includes("What Do You Need Repaired?")) {';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;

                case 'about_title':
                    echo 'document.querySelectorAll("h3").forEach(function(el) {';
                    echo '  if (el.textContent.includes("Your One-Stop Shop Repair Store")) {';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;

                case 'about_description':
                    echo 'document.querySelectorAll("p").forEach(function(el) {';
                    echo '  if (el.textContent.includes("Same Day Service on Major Brands")) {';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;

                case 'features_title':
                    echo 'document.querySelectorAll("p, strong").forEach(function(el) {';
                    echo '  if (el.textContent.includes("Why Our Customers Love Us")) {';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;

                case 'cta_title':
                    echo 'document.querySelectorAll("p, h2, h3").forEach(function(el) {';
                    echo '  var text = el.textContent.toLowerCase();';
                    echo '  if (text.includes("ready to get") || text.includes("repair your device") || text.includes("get your device fixed")) {';
                    echo '    console.log("✅ Replacing CTA title: " + el.textContent + " -> ' . $escaped_value . '");';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;

                case 'cta_description':
                    echo 'document.querySelectorAll("p").forEach(function(el) {';
                    echo '  var text = el.textContent.toLowerCase();';
                    echo '  if (text.includes("reliable and professional service") || text.includes("highest quality replacement parts")) {';
                    echo '    console.log("✅ Replacing CTA description: " + el.textContent.substring(0,50) + "... -> ' . substr($escaped_value, 0, 50) . '...");';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;

                case 'copyright_text':
                    echo 'document.querySelectorAll("p").forEach(function(el) {';
                    echo '  if (el.textContent.includes("© 2023 All Rights Reserved")) {';
                    echo '    el.textContent = "' . $escaped_value . '";';
                    echo '  }';
                    echo '});';
                    break;
            }
        }

        echo '});';
        echo '</script>';

        // ENHANCED: Add a separate script for more robust hero title replacement
        if (isset($custom_texts['hero_title'])) {
            $escaped_hero_title = esc_js($custom_texts['hero_title']);
            echo '<script type="text/javascript">';
            echo 'try {';
            echo '  document.addEventListener("DOMContentLoaded", function() {';
            echo '    console.log("🔄 Enhanced hero title replacement starting...");';
            echo '    setTimeout(function() {';
            echo '      try {';
            echo '        var heroSelectors = [';
            echo '          "h1:first-of-type",';
            echo '          ".wp-block-group:first-child h1",';
            echo '          ".hero-section h1",';
            echo '          ".wp-site-blocks > .wp-block-group:first-child h1",';
            echo '          "main h1:first-of-type"';
            echo '        ];';
            echo '        ';
            echo '        heroSelectors.forEach(function(selector) {';
            echo '          try {';
            echo '            var heroElement = document.querySelector(selector);';
            echo '            if (heroElement) {';
            echo '              var text = heroElement.textContent.toLowerCase();';
            echo '              if (text.includes("device repair") || text.includes("repair") || text.includes("premier")) {';
            echo '                console.log("✅ Enhanced hero replacement: " + heroElement.textContent + " -> ' . $escaped_hero_title . '");';
            echo '                heroElement.textContent = "' . $escaped_hero_title . '";';
            echo '              }';
            echo '            }';
            echo '          } catch(e) {';
            echo '            console.log("⚠️ Error with selector " + selector + ":", e);';
            echo '          }';
            echo '        });';
            echo '      } catch(e) {';
            echo '        console.log("⚠️ Error in hero replacement:", e);';
            echo '      }';
            echo '    }, 1000);';
            echo '  });';
            echo '} catch(e) {';
            echo '  console.log("⚠️ Error setting up hero replacement:", e);';
            echo '}';
            echo '</script>';
        }
    }

    /**
     * Replace content text using WordPress filters
     */
    public function replace_content_text($content) {
        $custom_texts = get_option('website_generator_custom_texts', array());

        // Only process on homepage
        if (!is_front_page()) {
            return $content;
        }

        foreach ($custom_texts as $key => $value) {
            switch ($key) {
                case 'hero_title':
                    // Replace any H1 content that looks like a hero heading
                    $content = preg_replace_callback(
                        '/<h1[^>]*>(.*?)<\/h1>/s',
                        function($matches) use ($value) {
                            $current_text = strip_tags($matches[1]);
                            // Replace if it contains repair-related keywords or test content
                            if (stripos($current_text, 'repair') !== false ||
                                stripos($current_text, 'premier') !== false ||
                                stripos($current_text, 'device') !== false ||
                                stripos($current_text, 'test from airtable') !== false ||
                                stripos($current_text, 'anytown') !== false ||
                                stripos($current_text, 'medellin') !== false) {

                                error_log('TextManager: Replacing hero title "' . $current_text . '" with "' . $value . '"');
                                return str_replace($matches[1], esc_html($value), $matches[0]);
                            }
                            return $matches[0];
                        },
                        $content
                    );
                    break;

                case 'hero_tagline':
                    // Replace any paragraph content that looks like a hero tagline/subtitle
                    $content = preg_replace_callback(
                        '/<p[^>]*>(.*?)<\/p>/s',
                        function($matches) use ($value) {
                            $current_text = strip_tags($matches[1]);
                            // Replace if it contains device-related keywords or test content
                            if (stripos($current_text, 'smartphones') !== false ||
                                stripos($current_text, 'tablets') !== false ||
                                stripos($current_text, 'computers') !== false ||
                                stripos($current_text, '& more') !== false ||
                                stripos($current_text, 'test from airtable') !== false ||
                                stripos($current_text, 'devices') !== false ||
                                stripos($current_text, 'electronics') !== false) {

                                error_log('TextManager: Replacing hero tagline "' . $current_text . '" with "' . $value . '"');
                                return str_replace($matches[1], esc_html($value), $matches[0]);
                            }
                            return $matches[0];
                        },
                        $content
                    );
                    break;

                case 'about_description':
                    $content = str_replace(
                        'Same Day Service on Major Brands & Devices. Visit us at a location near you. We take pride in what we do. And what we do best is restore your device back to its original condition. You can rest easy when you have your device repaired with us. We offer a warranty on all our repairs.',
                        $value,
                        $content
                    );
                    break;
            }
        }

        return $content;
    }

    /**
     * Replace title text
     */
    public function replace_title_text($title) {
        // This can be used to modify page titles if needed
        return $title;
    }
}
