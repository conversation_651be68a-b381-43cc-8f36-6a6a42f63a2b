<?php

class ThemeCustomizer {
    
    private $child_theme_path;
    
    public function __construct() {
        $this->child_theme_path = get_stylesheet_directory();
    }
    
    /**
     * Update theme colors - 2 Button Colors
     */
    public function update_colors($primary_color = null, $secondary_color = null, $button_text_color = null) {
        // Get colors from WordPress options if not provided
        $primary_color = $primary_color ?: get_option('website_generator_primary_color', '#165C9C');
        $secondary_color = $secondary_color ?: get_option('website_generator_secondary_color', '#111111');
        $button_text_color = $button_text_color ?: get_option('website_generator_button_text_color', '#FFFFFF');

        // Apply safe, targeted CSS for both button colors
        $this->apply_safe_hero_css($primary_color, $secondary_color, $button_text_color);

        return array(
            'primary' => $primary_color,
            'secondary' => $secondary_color,
            'button_text' => $button_text_color,
            'status' => 'success'
        );
    }

    /**
     * Regenerate CSS using saved WordPress options
     */
    public function regenerate_css_from_options() {
        return $this->update_colors();
    }

    /**
     * Apply safe CSS targeting ONLY specific hero buttons with separate colors
     */
    private function apply_safe_hero_css($primary_color = null, $secondary_color = null, $button_text_color = null) {
        // Get colors from WordPress options if not provided
        $primary_color = $primary_color ?: get_option('website_generator_primary_color', '#165C9C');
        $secondary_color = $secondary_color ?: get_option('website_generator_secondary_color', '#111111');
        $button_text_color = $button_text_color ?: get_option('website_generator_button_text_color', '#FFFFFF');

        error_log('Website Generator: Applying SAFE hero CSS - Primary: ' . $primary_color . ', Secondary: ' . $secondary_color . ', Button Text: ' . $button_text_color);

        // Get design options for button shapes and animations
        $button_style = get_option('website_generator_button_style', 'pill');
        $hero_animation = get_option('website_generator_hero_animation', 'fadeInUp');
        $image_animation = get_option('website_generator_image_animation', 'fadeIn');
        $animation_speed = get_option('website_generator_animation_speed', 'normal');

        // Debug logging
        error_log('Website Generator: Button style from DB: ' . $button_style);
        error_log('Website Generator: Secondary color: ' . $secondary_color);
        error_log('Website Generator: Border radius will be: ' . $this->get_button_border_radius($button_style));

        // Get animation duration
        $animation_duration = $this->get_animation_duration($animation_speed);

        // Get button border radius based on style
        $border_radius = $this->get_button_border_radius($button_style);

        $css = "
/* Website Generator Pro - COMPREHENSIVE BUTTON TARGETING WITH DESIGN SYSTEM */
/* Generated on: " . date('Y-m-d H:i:s') . " */

/* PRIMARY BUTTONS - Main CTAs (Get Instant Quote, Start a Repair) */
a[href*='instant-quote'],
.wp-block-button__link.has-primary-background-color {
    background-color: {$primary_color} !important;
    color: {$button_text_color} !important;
    border-color: {$primary_color} !important;
    border-radius: {$border_radius} !important;
    transition: all 0.3s ease !important;
}

/* SECONDARY BUTTONS - Only specific content section buttons */
/* Call Us Now button in hero (tel link with button styling) */
a[href^='tel:'].wp-block-button__link,
a[href='tel:5552221111'].wp-block-button__link {
    background-color: {$secondary_color} !important;
    color: {$button_text_color} !important;
    border-color: {$secondary_color} !important;
    border-radius: {$border_radius} !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
}

/* PRIMARY BUTTON HOVER EFFECTS */
a[href*='instant-quote']:hover,
.wp-block-button__link.has-primary-background-color:hover {
    background-color: {$primary_color} !important;
    opacity: 0.8 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

/* SECONDARY BUTTON HOVER EFFECTS */
a[href^='tel:'].wp-block-button__link:hover,
a[href='tel:5552221111'].wp-block-button__link:hover {
    background-color: {$secondary_color} !important;
    opacity: 0.8 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

/* HERO SECTION ANIMATIONS */
.wp-block-group:first-of-type,
.hero-section,
.wp-site-blocks > .wp-block-group:first-child {
    animation: {$hero_animation} {$animation_duration} ease-out !important;
}

/* IMAGE ANIMATIONS */
img:not(.wp-block-site-logo img),
.wp-block-image img,
figure img {
    animation: {$image_animation} {$animation_duration} ease-out !important;
}

/* ANIMATION KEYFRAMES */
" . $this->get_animation_keyframes() . "
";

        // Write the safe CSS
        $this->write_custom_css($css);
    }

    /**
     * Get button border radius based on style
     */
    public function get_button_border_radius($button_style) {
        switch ($button_style) {
            case 'pill':
                return '65px';
            case 'rounded':
                return '8px';
            case 'square':
                return '0px';
            default:
                return '65px';
        }
    }



    /**
     * Remove all custom CSS and revert to defaults (comprehensive revert function)
     */
    public function revert_all_changes() {
        $errors = array();
        $success_messages = array();

        // 1. Remove custom CSS file
        $custom_css_path = $this->child_theme_path . '/website-generator-custom.css';
        if (file_exists($custom_css_path)) {
            $result = unlink($custom_css_path);
            if ($result) {
                $success_messages[] = 'Custom CSS file removed';
                error_log('Website Generator: Custom CSS file removed successfully');
            } else {
                $errors[] = 'Failed to remove custom CSS file';
                error_log('Website Generator: Failed to remove custom CSS file');
            }
        } else {
            $success_messages[] = 'No custom CSS file to remove';
        }

        // 2. Reset all WordPress options to defaults
        $this->reset_plugin_options_to_defaults();
        $success_messages[] = 'Plugin options reset to defaults';

        // 3. Remove theme modifications
        $this->reset_theme_modifications();
        $success_messages[] = 'Theme modifications removed';

        // 4. Clear WordPress caches
        $this->clear_wordpress_caches();
        $success_messages[] = 'WordPress caches cleared';

        if (!empty($errors)) {
            return array(
                'status' => 'partial',
                'message' => 'Some changes reverted with errors: ' . implode(', ', $errors),
                'success_items' => $success_messages,
                'errors' => $errors
            );
        } else {
            return array(
                'status' => 'success',
                'message' => 'All changes reverted successfully: ' . implode(', ', $success_messages)
            );
        }
    }

    /**
     * Reset all plugin WordPress options to their default values
     */
    private function reset_plugin_options_to_defaults() {
        // Default color values (from get_default_design_system)
        $defaults = array(
            'website_generator_primary_color' => '#165C9C',
            'website_generator_secondary_color' => '#6AA7E0',
            'website_generator_accent_color' => '#F7F7F7',
            'website_generator_button_style' => 'pill',
            'website_generator_button_size' => 'medium',
            'website_generator_button_text_color' => '#FFFFFF',
            'website_generator_typography_heading' => 'font-modern',
            'website_generator_typography_body' => 'font-modern',
            'website_generator_hero_animation' => 'fadeInUp',
            'website_generator_image_animation' => 'fadeIn',
            'website_generator_animation_speed' => 'normal',
            'website_generator_section_spacing' => 'normal'
        );

        foreach ($defaults as $option_name => $default_value) {
            update_option($option_name, $default_value);
        }

        // Clear custom texts (reset to defaults)
        $default_custom_texts = array(
            'phone_number' => '(*************',
            'address' => '123 Main Street, Anytown, FL 12345',
            'email' => '<EMAIL>'
        );
        update_option('website_generator_custom_texts', $default_custom_texts);

        error_log('Website Generator: Plugin options reset to defaults');
    }

    /**
     * Reset theme modifications
     */
    private function reset_theme_modifications() {
        // Remove custom logo
        remove_theme_mod('custom_logo');

        // Reset theme colors to defaults
        set_theme_mod('primary_color', '#165C9C');
        set_theme_mod('secondary_color', '#6AA7E0');

        error_log('Website Generator: Theme modifications reset');
    }

    /**
     * Clear WordPress caches
     */
    private function clear_wordpress_caches() {
        // Clear theme cache
        if (function_exists('wp_clean_themes_cache')) {
            wp_clean_themes_cache();
        }

        // Clear object cache if available
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        // Clear any transients
        delete_transient('website_generator_detected_colors');

        error_log('Website Generator: WordPress caches cleared');
    }

    /**
     * Update theme.json colors
     */
    private function update_theme_json_colors($primary_color, $secondary_color, $tertiary_color = null) {
        $theme_json_path = $this->child_theme_path . '/theme.json';

        if (!file_exists($theme_json_path)) {
            throw new Exception('theme.json file not found');
        }

        // Read current theme.json
        $theme_json_content = file_get_contents($theme_json_path);
        $theme_data = json_decode($theme_json_content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON in theme.json');
        }

        // Update colors in the palette
        foreach ($theme_data['settings']['color']['palette'] as &$color) {
            if ($color['slug'] === 'primary') {
                $color['color'] = $primary_color;
            }
            if ($color['slug'] === 'secondary') {
                $color['color'] = $secondary_color;
            }
            if ($color['slug'] === 'tertiary' && $tertiary_color) {
                $color['color'] = $tertiary_color;
            }
        }

        // Write back to theme.json
        $updated_json = json_encode($theme_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

        if (file_put_contents($theme_json_path, $updated_json) === false) {
            throw new Exception('Failed to write theme.json');
        }

        // Clear any WordPress caches
        if (function_exists('wp_clean_themes_cache')) {
            wp_clean_themes_cache();
        }
    }

    /**
     * Apply custom CSS to ensure colors and styles actually take effect on the website
     */
    private function apply_custom_css($primary_color, $secondary_color, $tertiary_color = null, $button_text_color = null, $design_options = array()) {
        error_log('Website Generator: Generating CSS with colors - Primary: ' . $primary_color . ', Secondary: ' . $secondary_color);

        // Get design options from WordPress options if not provided
        if (empty($design_options)) {
            $design_options = array(
                'button_style' => get_option('website_generator_button_style', 'pill'),
                'button_size' => get_option('website_generator_button_size', 'medium'),
                'typography_heading' => get_option('website_generator_typography_heading', 'font-modern'),
                'typography_body' => get_option('website_generator_typography_body', 'font-modern'),
                'hero_animation' => get_option('website_generator_hero_animation', 'fadeInUp'),
                'image_animation' => get_option('website_generator_image_animation', 'fadeIn'),
                'animation_speed' => get_option('website_generator_animation_speed', 'normal'),
                'section_spacing' => get_option('website_generator_section_spacing', 'normal')
            );
        }

        $css = "
/* Website Generator Pro - Complete Style Overrides */
/* Generated on: " . date('Y-m-d H:i:s') . " */
:root {
    --wg-primary-color: {$primary_color};
    --wg-secondary-color: {$secondary_color};
    --wg-tertiary-color: " . ($tertiary_color ?: '#F7F7F7') . ";
    --wg-button-text-color: " . ($button_text_color ?: '#FFFFFF') . ";
}

/* Primary Button Overrides - Target actual website buttons with high specificity */
body.website-generator-active .wp-block-button__link.has-primary-background-color,
body.website-generator-active .wp-block-button__link,
body.website-generator-active a[href*='instant-quote'],
body.website-generator-active a[href*='repair'],
body.website-generator-active a[href*='quote'],
body.website-generator-active a[href*='location'],
body.website-generator-active a[href*='buy'],
body.website-generator-active a[href*='sell'],
body.website-generator-active .button,
body.website-generator-active .btn,
body.website-generator-active input[type='submit'],
body.website-generator-active button[type='submit'] {
    background-color: var(--wg-primary-color) !important;
    color: var(--wg-button-text-color) !important;
    border-color: var(--wg-primary-color) !important;
}

/* Button hover effects */
body.website-generator-active .wp-block-button__link:hover,
body.website-generator-active a[href*='instant-quote']:hover,
body.website-generator-active a[href*='repair']:hover,
body.website-generator-active a[href*='quote']:hover,
body.website-generator-active .button:hover,
body.website-generator-active .btn:hover {
    background-color: var(--wg-primary-color) !important;
    opacity: 0.9 !important;
}

/* Link color overrides */
body.website-generator-active a:not(.wp-block-button__link) {
    color: var(--wg-primary-color) !important;
}

/* Footer link overrides */
body.website-generator-active footer a {
    color: var(--wg-primary-color) !important;
}

/* Navigation links */
body.website-generator-active .wp-block-navigation-item__content {
    color: var(--wg-secondary-color) !important;
}

/* Secondary color applications */
body.website-generator-active .secondary-color,
body.website-generator-active .has-secondary-color {
    color: var(--wg-secondary-color) !important;
}

body.website-generator-active .secondary-background,
body.website-generator-active .has-secondary-background-color {
    background-color: var(--wg-secondary-color) !important;
}

/* Text color overrides */
body.website-generator-active,
body.website-generator-active p,
body.website-generator-active .wp-block-paragraph {
    color: var(--wg-secondary-color) !important;
}

/* Background color overrides */
body.website-generator-active,
body.website-generator-active .wp-site-blocks {
    background-color: var(--wg-tertiary-color) !important;
}
";

        // Add button style CSS
        $css .= $this->generate_button_style_css($design_options);

        // Add typography CSS
        $css .= $this->generate_typography_css($design_options);

        // Add animation CSS
        $css .= $this->generate_animation_css($design_options);

        // Add spacing CSS
        $css .= $this->generate_spacing_css($design_options);

        // Write CSS to child theme style.css or create a separate CSS file
        $this->write_custom_css($css);
    }

    /**
     * Write custom CSS to the child theme
     */
    private function write_custom_css($css) {
        $custom_css_path = $this->child_theme_path . '/website-generator-custom.css';

        // Ensure the directory exists and is writable
        if (!is_dir($this->child_theme_path)) {
            throw new Exception('Child theme directory does not exist: ' . $this->child_theme_path);
        }

        if (!is_writable($this->child_theme_path)) {
            throw new Exception('Child theme directory is not writable: ' . $this->child_theme_path);
        }

        // Write the CSS file
        $result = file_put_contents($custom_css_path, $css);

        if ($result === false) {
            error_log('Website Generator: Failed to write CSS file: ' . $custom_css_path);
            throw new Exception('Failed to write CSS file: ' . $custom_css_path);
        }

        // Verify the file was created
        if (!file_exists($custom_css_path)) {
            error_log('Website Generator: CSS file was not created: ' . $custom_css_path);
            throw new Exception('CSS file was not created: ' . $custom_css_path);
        }

        error_log('Website Generator: CSS file created successfully: ' . $custom_css_path . ' (' . $result . ' bytes)');

        // Log a sample of the CSS for debugging
        $css_preview = substr($css, 0, 200) . '...';
        error_log('Website Generator: CSS Preview: ' . $css_preview);

        return $custom_css_path;
    }

    /**
     * Generate button style CSS
     */
    private function generate_button_style_css($design_options) {
        $button_style = $design_options['button_style'] ?? 'pill';
        $button_size = $design_options['button_size'] ?? 'medium';

        $css = "\n/* Button Style Overrides */\n";

        // Button shape styles
        switch ($button_style) {
            case 'pill':
                $css .= "
body.website-generator-active .wp-block-button__link,
body.website-generator-active a[href*='instant-quote'],
body.website-generator-active a[href*='repair'],
body.website-generator-active a[href*='quote'],
body.website-generator-active .button,
body.website-generator-active .btn {
    border-radius: 65px !important;
}";
                break;
            case 'rounded':
                $css .= "
body.website-generator-active .wp-block-button__link,
body.website-generator-active a[href*='instant-quote'],
body.website-generator-active a[href*='repair'],
body.website-generator-active a[href*='quote'],
body.website-generator-active .button,
body.website-generator-active .btn {
    border-radius: 8px !important;
}";
                break;
            case 'square':
                $css .= "
body.website-generator-active .wp-block-button__link,
body.website-generator-active a[href*='instant-quote'],
body.website-generator-active a[href*='repair'],
body.website-generator-active a[href*='quote'],
body.website-generator-active .button,
body.website-generator-active .btn {
    border-radius: 0px !important;
}";
                break;
        }

        // Button size styles
        switch ($button_size) {
            case 'small':
                $css .= "
body.website-generator-active .wp-block-button__link,
body.website-generator-active a[href*='instant-quote'],
body.website-generator-active a[href*='repair'],
body.website-generator-active a[href*='quote'],
body.website-generator-active .button,
body.website-generator-active .btn {
    padding: 8px 24px !important;
    font-size: 14px !important;
}";
                break;
            case 'medium':
                $css .= "
body.website-generator-active .wp-block-button__link,
body.website-generator-active a[href*='instant-quote'],
body.website-generator-active a[href*='repair'],
body.website-generator-active a[href*='quote'],
body.website-generator-active .button,
body.website-generator-active .btn {
    padding: 12px 44px !important;
    font-size: 15px !important;
}";
                break;
            case 'large':
                $css .= "
body.website-generator-active .wp-block-button__link,
body.website-generator-active a[href*='instant-quote'],
body.website-generator-active a[href*='repair'],
body.website-generator-active a[href*='quote'],
body.website-generator-active .button,
body.website-generator-active .btn {
    padding: 16px 56px !important;
    font-size: 18px !important;
}";
                break;
        }

        return $css;
    }

    /**
     * Generate typography CSS
     */
    private function generate_typography_css($design_options) {
        $heading_font = $design_options['typography_heading'] ?? 'font-modern';
        $body_font = $design_options['typography_body'] ?? 'font-modern';

        $css = "\n/* Typography Overrides */\n";

        // Heading fonts
        $heading_family = $this->get_font_family($heading_font);
        $css .= "
h1, h2, h3, h4, h5, h6,
.wp-block-heading {
    font-family: {$heading_family} !important;
}";

        // Body fonts
        $body_family = $this->get_font_family($body_font);
        $css .= "
body,
p,
.wp-block-paragraph,
.wp-block-navigation-item__content {
    font-family: {$body_family} !important;
}";

        return $css;
    }

    /**
     * Get font family CSS value
     */
    private function get_font_family($font_class) {
        switch ($font_class) {
            case 'font-modern':
                return "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif";
            case 'font-classic':
                return "'Georgia', 'Times New Roman', serif";
            case 'font-tech':
                return "'JetBrains Mono', 'Courier New', monospace";
            case 'font-friendly':
                return "'Poppins', 'Helvetica Neue', Arial, sans-serif";
            default:
                return "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif";
        }
    }

    /**
     * Generate animation CSS
     */
    private function generate_animation_css($design_options) {
        $hero_animation = $design_options['hero_animation'] ?? 'fadeInUp';
        $image_animation = $design_options['image_animation'] ?? 'fadeIn';
        $animation_speed = $design_options['animation_speed'] ?? 'normal';

        $duration = $this->get_animation_duration($animation_speed);

        $css = "\n/* Animation Overrides */\n";

        // Hero section animations
        $css .= "
.wp-block-group:first-of-type,
.hero-section {
    animation: {$hero_animation} {$duration} ease-out !important;
}";

        // Image animations
        $css .= "
img,
.wp-block-image,
figure {
    animation: {$image_animation} {$duration} ease-out !important;
}";

        // Animation keyframes
        $css .= $this->get_animation_keyframes();

        return $css;
    }

    /**
     * Generate spacing CSS
     */
    private function generate_spacing_css($design_options) {
        $section_spacing = $design_options['section_spacing'] ?? 'normal';

        $css = "\n/* Spacing Overrides */\n";

        switch ($section_spacing) {
            case 'compact':
                $css .= "
.wp-block-group,
.wp-block-cover {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
    margin-bottom: 1rem !important;
}";
                break;
            case 'normal':
                $css .= "
.wp-block-group,
.wp-block-cover {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
    margin-bottom: 2rem !important;
}";
                break;
            case 'spacious':
                $css .= "
.wp-block-group,
.wp-block-cover {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important;
    margin-bottom: 3rem !important;
}";
                break;
        }

        return $css;
    }

    /**
     * Get animation duration based on speed setting
     */
    private function get_animation_duration($speed) {
        switch ($speed) {
            case 'fast':
                return '0.3s';
            case 'normal':
                return '0.6s';
            case 'slow':
                return '1s';
            default:
                return '0.6s';
        }
    }

    /**
     * Get animation keyframes CSS
     */
    private function get_animation_keyframes() {
        return "
/* Animation Keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-10deg);
    }
    to {
        opacity: 1;
        transform: rotate(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}
";
    }

    /**
     * Handle logo upload and set as site logo
     */
    public function update_logo($logo_file) {
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        
        // Validate file
        $allowed_types = array('image/jpeg', 'image/png', 'image/gif', 'image/svg+xml');
        if (!in_array($logo_file['type'], $allowed_types)) {
            throw new Exception('Invalid file type. Please upload JPG, PNG, GIF, or SVG.');
        }
        
        // Handle upload
        $uploaded_file = wp_handle_upload($logo_file, array('test_form' => false));
        
        if (isset($uploaded_file['error'])) {
            throw new Exception('Upload failed: ' . $uploaded_file['error']);
        }
        
        // Create attachment
        $attachment = array(
            'post_mime_type' => $uploaded_file['type'],
            'post_title' => 'Company Logo',
            'post_content' => '',
            'post_status' => 'inherit'
        );
        
        $attach_id = wp_insert_attachment($attachment, $uploaded_file['file']);
        
        if (is_wp_error($attach_id)) {
            throw new Exception('Failed to create attachment');
        }
        
        // Generate attachment metadata
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attach_data = wp_generate_attachment_metadata($attach_id, $uploaded_file['file']);
        wp_update_attachment_metadata($attach_id, $attach_data);
        
        // Set as custom logo
        set_theme_mod('custom_logo', $attach_id);
        
        return array(
            'attachment_id' => $attach_id,
            'url' => $uploaded_file['url'],
            'status' => 'success'
        );
    }
    
    /**
     * Create a backup of current theme.json
     */
    public function backup_theme_json() {
        $theme_json_path = $this->child_theme_path . '/theme.json';
        $backup_path = $this->child_theme_path . '/theme.json.backup.' . date('Y-m-d-H-i-s');
        
        if (file_exists($theme_json_path)) {
            copy($theme_json_path, $backup_path);
            return $backup_path;
        }
        
        return false;
    }
    
    /**
     * Restore theme.json from backup
     */
    public function restore_theme_json($backup_file) {
        $theme_json_path = $this->child_theme_path . '/theme.json';
        
        if (file_exists($backup_file)) {
            copy($backup_file, $theme_json_path);
            
            // Clear caches
            if (function_exists('wp_clean_themes_cache')) {
                wp_clean_themes_cache();
            }
            
            return true;
        }
        
        return false;
    }
}
